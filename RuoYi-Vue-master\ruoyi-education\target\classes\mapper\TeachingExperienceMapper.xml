<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeachingExperienceMapper">

    <resultMap type="TeachingExperience" id="TeachingExperienceResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="teachingTarget"    column="teaching_target"    />
        <result property="content"    column="content"    />
        <result property="images"    column="images"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTeachingExperienceVo">
        select id, teacher_id, teaching_target, content, images, start_date, end_date, create_time, update_time, create_by, update_by from teaching_experience
    </sql>

    <select id="selectTeachingExperienceList" parameterType="TeachingExperience" resultMap="TeachingExperienceResult">
        <include refid="selectTeachingExperienceVo"/>
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
        </where>
    </select>

    <select id="selectTeachingExperienceById" parameterType="Long" resultMap="TeachingExperienceResult">
        <include refid="selectTeachingExperienceVo"/>
        where id = #{id}
    </select>

    <insert id="insertTeachingExperience" parameterType="TeachingExperience" useGeneratedKeys="true" keyProperty="id">
        insert into teaching_experience
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="teachingTarget != null and teachingTarget != ''">teaching_target,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="images != null and images != ''">images,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="teachingTarget != null and teachingTarget != ''">#{teachingTarget},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="images != null and images != ''">#{images},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateTeachingExperience" parameterType="TeachingExperience">
        update teaching_experience
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="teachingTarget != null and teachingTarget != ''">teaching_target = #{teachingTarget},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="images != null and images != ''">images = #{images},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeachingExperienceById" parameterType="Long">
        delete from teaching_experience where id = #{id}
    </delete>

    <delete id="deleteTeachingExperienceByIds" parameterType="String">
        delete from teaching_experience where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 
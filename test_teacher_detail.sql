-- 测试教师详情功能的SQL

-- 1. 查看当前教师数据
SELECT 
    id,
    real_name,
    nick_name,
    university,
    major,
    grade,
    teaching_subjects,
    education_background,
    experience_background,
    self_introduction,
    certification_status,
    status,
    age,
    gender,
    rating,
    total_orders,
    success_orders
FROM teacher_info 
WHERE status = '0' 
ORDER BY id;

-- 2. 如果没有合适的测试数据，更新现有数据
UPDATE teacher_info 
SET 
    education_background = '北京大学数学系本科在读，专业成绩优异，多次获得奖学金',
    experience_background = '3年家教经验，擅长初高中数学教学，帮助多名学生提高成绩',
    self_introduction = '我是一名热爱教育的大学生，性格开朗，有耐心。擅长用简单易懂的方式讲解复杂的数学概念，能够根据学生的特点制定个性化的学习方案。',
    teaching_subjects = '数学,物理',
    age = 22,
    gender = '0',
    rating = 4.8,
    total_orders = 15,
    success_orders = 14
WHERE id = (SELECT id FROM teacher_info WHERE status = '0' LIMIT 1);

-- 3. 验证更新结果
SELECT 
    id,
    CONCAT(LEFT(real_name, 1), '老师') as display_name,
    real_name,
    university,
    major,
    teaching_subjects,
    education_background,
    experience_background,
    self_introduction,
    certification_status,
    CASE 
        WHEN certification_status = '4' THEN '已实名认证'
        WHEN certification_status = '2' THEN '已认证'
        ELSE '未认证'
    END as certification_text,
    age,
    CASE 
        WHEN gender = '0' THEN '男'
        WHEN gender = '1' THEN '女'
        ELSE '未知'
    END as gender_text,
    rating,
    total_orders,
    success_orders
FROM teacher_info 
WHERE status = '0' 
  AND certification_status IN ('2', '4')
ORDER BY id;

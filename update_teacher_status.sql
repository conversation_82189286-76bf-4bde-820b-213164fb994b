-- 更新教师状态，使其符合推荐教师的条件
-- 这个脚本会将前两个教师设置为符合条件的状态

-- 1. 首先查看现有的教师数据
SELECT id, nick_name, real_name, status, certification_status FROM teacher_info ORDER BY id LIMIT 5;

-- 2. 更新前两个教师的状态
-- 将第一个教师设置为学生认证
UPDATE teacher_info 
SET 
    status = '0',  -- 正常状态
    certification_status = '1',  -- 学生认证
    teaching_subjects = '数学,英语,物理',  -- 设置教授科目
    self_introduction = '本人性格开朗、乐观向上、爱与人沟通，具有较强的组织协调能力和团队合作精神。在教学工作中，本人认真负责，有耐心，善于引导学生思考。',
    rating = 4.8,
    university = COALESCE(university, '湘潭大学'),
    major = COALESCE(major, '本科生'),
    grade = COALESCE(grade, '大三')
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info ORDER BY id LIMIT 1) as temp);

-- 将第二个教师设置为实名认证
UPDATE teacher_info 
SET 
    status = '0',  -- 正常状态
    certification_status = '2',  -- 实名认证
    teaching_subjects = '语文,英语,历史,政治',  -- 设置教授科目
    self_introduction = '有耐心，善于掌控全局，学习生活长期担任班干部，有条不紊，上课有条理，书面分明，有参分明。具有丰富的教学经验。',
    rating = 4.6,
    university = COALESCE(university, '长沙理工大学'),
    major = COALESCE(major, '本科生'),
    grade = COALESCE(grade, '大二')
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info ORDER BY id LIMIT 1,1) as temp);

-- 3. 验证更新结果
SELECT 
    id,
    nick_name,
    real_name,
    university,
    major,
    status,
    certification_status,
    CASE 
        WHEN certification_status = '1' THEN '学生认证'
        WHEN certification_status = '2' THEN '实名认证'
        ELSE '未认证'
    END as certification_text,
    teaching_subjects,
    rating
FROM teacher_info 
WHERE status = '0' AND certification_status IN ('1', '2')
ORDER BY certification_status DESC, rating DESC;

package com.ruoyi.education.controller.applet;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.education.domain.Subject;
import com.ruoyi.education.service.ISubjectService;

/**
 * 小程序配置Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/applet/config")
public class AppletConfigController extends BaseController
{
    @Autowired
    private ISubjectService subjectService;

    /**
     * 获取科目列表
     */
    @GetMapping("/subjects")
    public AjaxResult getSubjects()
    {
        try {
            Subject subject = new Subject();
            subject.setStatus("0"); // 只获取启用的科目
            List<Subject> list = subjectService.selectSubjectList(subject);
            
            // 转换为前端需要的格式
            List<Map<String, Object>> result = new java.util.ArrayList<>();
            for (Subject item : list) {
                Map<String, Object> subjectMap = new HashMap<>();
                subjectMap.put("id", item.getId());
                subjectMap.put("name", item.getSubjectName());
                result.add(subjectMap);
            }
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取科目列表失败", e);
            return AjaxResult.error("获取科目列表失败");
        }
    }

    /**
     * 获取轮播图列表
     */
    @GetMapping("/banners")
    public AjaxResult getBanners(String type)
    {
        try {
            // 这里可以根据type参数返回不同类型的轮播图
            // 暂时返回空列表
            return AjaxResult.success(new java.util.ArrayList<>());
        } catch (Exception e) {
            logger.error("获取轮播图失败", e);
            return AjaxResult.error("获取轮播图失败");
        }
    }

    /**
     * 获取公告列表
     */
    @GetMapping("/announcements")
    public AjaxResult getAnnouncements(String type)
    {
        try {
            // 这里可以根据type参数返回不同类型的公告
            // 暂时返回空列表
            return AjaxResult.success(new java.util.ArrayList<>());
        } catch (Exception e) {
            logger.error("获取公告失败", e);
            return AjaxResult.error("获取公告失败");
        }
    }
}

{"pages": ["pages/login/index", "pages/teacher/home/<USER>", "pages/teacher/announcement/detail/index", "pages/teacher/workspace/index", "pages/teacher/profile/index", "pages/teacher/membership/index", "pages/teacher/certification/index", "pages/teacher/profile-detail/index", "pages/teacher/subjects/index", "pages/teacher/profile/edit-education/index", "pages/teacher/profile/edit-education/form/index", "pages/teacher/profile/edit-experience/index", "pages/teacher/profile/edit-experience/form/index", "pages/teacher/student_source/index", "pages/teacher/order/detail/index", "pages/teacher/workspace/detail/index", "pages/teacher/wallet/index", "pages/teacher/wallet/income-detail/index", "pages/teacher/wallet/withdraw-apply/index", "pages/teacher/wallet/withdraw-record/index", "pages/teacher/deposit/index", "pages/debug-api/index", "pages/test-wallet/index", "pages/debug-request/index", "pages/debug-home-api/index", "pages/debug-login/index", "pages/parent/home/<USER>", "pages/parent/publish/index", "pages/parent/teacher_detail/index", "pages/parent/profile/index", "pages/parent/order/list/index", "pages/parent/order/detail/index", "pages/parent/order/edit/index", "pages/parent/profile/edit/index", "pages/parent/announcement/detail/index"], "window": {"navigationBarTextStyle": "black", "navigationStyle": "custom"}, "tabBar": {"custom": true, "color": "#666666", "selectedColor": "#1296db", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/teacher/home/<USER>", "text": "首页"}, {"pagePath": "pages/teacher/workspace/index", "text": "工作台"}, {"pagePath": "pages/teacher/student_source/index", "text": "生源"}, {"pagePath": "pages/teacher/profile/index", "text": "我的"}, {"pagePath": "pages/parent/home/<USER>", "text": "首页"}]}, "style": "v2", "renderer": "skyline", "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "defaultContentBox": true, "tagNameStyleIsolation": "legacy", "disableABTest": true, "sdkVersionBegin": "3.0.0", "sdkVersionEnd": "15.255.255"}}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}
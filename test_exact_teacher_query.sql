-- 测试精确的教师查询，基于您提供的数据状态

-- 1. 查看符合条件的教师数据
SELECT 
    id,
    nick_name,
    real_name,
    university,
    major,
    grade,
    status,
    certification_status,
    certification_fee_paid,
    del_flag,
    teaching_subjects,
    self_introduction,
    rating,
    create_time
FROM teacher_info 
WHERE status = '0' 
  AND certification_status = '2' 
  AND del_flag = '0'
ORDER BY rating DESC, create_time DESC;

-- 2. 测试完整的推荐教师查询（与API完全一致）
SELECT
    t.id,
    t.nick_name as name,
    t.real_name,
    t.avatar,
    t.university,
    t.major,
    t.grade,
    t.certification_status,
    CASE 
        WHEN t.certification_status = '1' THEN '学生认证'
        WHEN t.certification_status = '2' THEN '实名认证'
        ELSE '未认证'
    END as certification_text,
    COALESCE(t.teaching_subjects, '暂未设置') as subject,
    '经验丰富' as experience,
    IFNULL(t.rating, 4.5) as rating,
    COALESCE(t.self_introduction, '该教师暂未填写自我介绍') as features,
    '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三' as grades
FROM teacher_info t
WHERE t.status = '0' AND t.certification_status IN ('1', '2')
ORDER BY t.certification_status DESC, t.rating DESC, t.create_time DESC
LIMIT 10;

-- 3. 检查字段是否为空
SELECT 
    id,
    nick_name,
    CASE WHEN nick_name IS NULL THEN 'NULL' ELSE 'NOT NULL' END as nick_name_status,
    CASE WHEN university IS NULL THEN 'NULL' ELSE 'NOT NULL' END as university_status,
    CASE WHEN major IS NULL THEN 'NULL' ELSE 'NOT NULL' END as major_status,
    CASE WHEN teaching_subjects IS NULL THEN 'NULL' ELSE 'NOT NULL' END as teaching_subjects_status,
    CASE WHEN self_introduction IS NULL THEN 'NULL' ELSE 'NOT NULL' END as self_introduction_status
FROM teacher_info 
WHERE status = '0' AND certification_status = '2';

-- 4. 查看具体的字段内容
SELECT 
    id,
    nick_name,
    university,
    major,
    teaching_subjects,
    LEFT(self_introduction, 50) as self_intro_preview
FROM teacher_info 
WHERE status = '0' AND certification_status = '2';

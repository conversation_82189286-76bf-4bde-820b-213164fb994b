-- 检查数据库中的表结构
-- 查看所有表

SHOW TABLES;

-- 检查teacher_info表结构
DESCRIBE teacher_info;

-- 检查是否有teacher_subject表
SHOW TABLES LIKE '%subject%';

-- 检查是否有teaching_subjects字段（可能科目信息存储在teacher_info表中）
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'tutoring-db' 
AND TABLE_NAME = 'teacher_info' 
AND COLUMN_NAME LIKE '%subject%';

-- 查看teacher_info表中的teaching_subjects字段内容
SELECT id, nick_name, teaching_subjects FROM teacher_info LIMIT 5;

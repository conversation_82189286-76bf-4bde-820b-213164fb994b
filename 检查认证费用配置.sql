-- 检查认证费用配置的SQL脚本

-- 1. 检查platform_config表中是否存在认证费用配置
SELECT 
    id,
    config_key,
    config_value,
    config_name,
    config_desc,
    create_time,
    update_time
FROM platform_config 
WHERE config_key = 'teacher.certification.fee';

-- 2. 如果不存在，插入配置
INSERT INTO platform_config (
    config_key, 
    config_value, 
    config_name, 
    config_desc, 
    config_type, 
    create_time, 
    update_time
) 
SELECT 
    'teacher.certification.fee', 
    '99.00', 
    '教师认证费用', 
    '教师进行身份认证需要缴纳的费用金额', 
    'system', 
    NOW(), 
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM platform_config WHERE config_key = 'teacher.certification.fee'
);

-- 3. 如果存在但值为0或空，则更新
UPDATE platform_config 
SET 
    config_value = '99.00',
    update_time = NOW()
WHERE config_key = 'teacher.certification.fee' 
  AND (config_value = '0' OR config_value = '0.00' OR config_value IS NULL OR config_value = '');

-- 4. 验证配置是否正确
SELECT 
    config_key,
    config_value,
    config_name,
    config_desc
FROM platform_config 
WHERE config_key = 'teacher.certification.fee';

-- 5. 查看所有相关配置
SELECT 
    config_key,
    config_value,
    config_name
FROM platform_config 
WHERE config_key LIKE '%teacher%' OR config_key LIKE '%certification%' OR config_key LIKE '%fee%'
ORDER BY config_key;

-- 6. 检查教师表中的认证费用缴纳状态
SELECT 
    id,
    real_name,
    phone_number,
    certification_status,
    certification_fee_paid,
    create_time
FROM teacher_info 
WHERE id = 30  -- 根据日志中的教师ID
LIMIT 5;

-- 7. 查看所有教师的认证费用缴纳状态统计
SELECT 
    certification_fee_paid,
    COUNT(*) as count,
    CASE certification_fee_paid
        WHEN '0' THEN '未缴纳'
        WHEN '1' THEN '已缴纳'
        ELSE '未知状态'
    END as status_desc
FROM teacher_info 
GROUP BY certification_fee_paid;

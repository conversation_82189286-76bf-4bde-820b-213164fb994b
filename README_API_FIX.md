# 首页API接口404问题修复指南

## 问题描述
首页调用的三个API接口返回404错误：
- `/applet/config/banners` 
- `/applet/config/announcements`
- `/applet/config/cities`

## 解决方案

### 方案1：使用现有平台配置接口（推荐）

我已经修改了API调用，现在使用现有的平台配置接口：
- 轮播图：`/applet/config/value?configKey=teacher.banners`
- 公告：`/applet/config/value?configKey=teacher.announcement`  
- 城市：`/applet/config/value?configKey=cities`

### 步骤1：执行数据库初始化
```sql
-- 在MySQL中执行以下SQL文件
source sql/init_home_config.sql;
```

### 步骤2：验证配置数据
在管理后台查看平台配置是否已正确插入：
- 登录管理后台
- 进入"教育管理" -> "内容管理"
- 检查轮播图、公告、城市配置

### 步骤3：测试API接口
1. 在微信开发者工具中打开调试页面：
   - 页面路径：`pages/debug-home-api/index`
   - 或直接访问：`/pages/debug-home-api/index`

2. 查看测试结果，确认所有接口返回正常

### 步骤4：刷新配置缓存
如果后端有缓存机制，可能需要：
- 重启后端服务
- 或调用缓存刷新接口（如果有的话）

## 方案2：后端添加新接口（备选）

如果需要专门的接口，可以在后端添加：

```java
@RestController
@RequestMapping("/applet/config")
public class AppletConfigController {
    
    @Autowired
    private IPlatformConfigService platformConfigService;
    
    @GetMapping("/banners")
    public AjaxResult getBanners() {
        String banners = platformConfigService.selectConfigByKey("teacher.banners");
        if (StringUtils.isNotEmpty(banners)) {
            return AjaxResult.success(JSON.parseArray(banners));
        }
        return AjaxResult.success(new ArrayList<>());
    }
    
    @GetMapping("/announcements")
    public AjaxResult getAnnouncements() {
        String announcement = platformConfigService.selectConfigByKey("teacher.announcement");
        if (StringUtils.isNotEmpty(announcement)) {
            String[] lines = announcement.split("\n");
            List<Map<String, Object>> announcements = new ArrayList<>();
            for (int i = 0; i < lines.length; i++) {
                if (StringUtils.isNotBlank(lines[i])) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("id", i + 1);
                    item.put("title", lines[i].trim());
                    item.put("content", lines[i].trim());
                    announcements.add(item);
                }
            }
            return AjaxResult.success(announcements);
        }
        return AjaxResult.success(new ArrayList<>());
    }
    
    @GetMapping("/cities")
    public AjaxResult getCities() {
        String cities = platformConfigService.selectConfigByKey("cities");
        if (StringUtils.isNotEmpty(cities)) {
            return AjaxResult.success(JSON.parseArray(cities));
        }
        // 返回默认城市列表
        List<Map<String, String>> defaultCities = Arrays.asList(
            Map.of("name", "上海", "value", "shanghai"),
            Map.of("name", "北京", "value", "beijing"),
            Map.of("name", "广州", "value", "guangzhou"),
            Map.of("name", "深圳", "value", "shenzhen")
        );
        return AjaxResult.success(defaultCities);
    }
}
```

## 测试验证

### 1. 使用调试页面
访问 `/pages/debug-home-api/index` 页面，查看所有接口的测试结果。

### 2. 检查首页显示
访问教师端首页，确认：
- 轮播图正常显示
- 公告正常滚动
- 城市选择器正常工作
- 订单列表正常加载

### 3. 控制台日志
查看微信开发者工具控制台，确认没有API错误。

## 常见问题

### Q1: 数据库插入失败
- 检查platform_config表是否存在
- 确认数据库连接正常
- 检查SQL语法是否正确

### Q2: 接口仍然404
- 确认后端服务已重启
- 检查路由配置是否正确
- 验证请求URL是否匹配

### Q3: 数据格式错误
- 检查JSON格式是否正确
- 确认配置值是否正确存储
- 验证解析逻辑是否正确

## 联系支持
如果问题仍然存在，请提供：
1. 调试页面的测试结果截图
2. 后端服务日志
3. 数据库中platform_config表的相关记录

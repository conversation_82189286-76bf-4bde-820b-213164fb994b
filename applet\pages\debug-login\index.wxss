/* pages/debug-login/index.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  background: #007aff;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: none;
}

.btn.secondary {
  background: #666;
}

.results {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-content {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 800rpx;
  overflow-y: scroll;
}

.result-line {
  display: block;
  font-size: 24rpx;
  line-height: 1.5;
  margin-bottom: 10rpx;
  word-break: break-all;
  color: #666;
}

.result-line:last-child {
  margin-bottom: 0;
}

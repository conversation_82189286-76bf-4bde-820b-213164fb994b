-- 第二步：数据标准化脚本
-- 根据第一步的检查结果，执行相应的标准化操作

-- 注意：执行前请先备份数据库！

-- 1. 如果teacher_info表没有deleted字段，添加它
-- （如果第一步检查显示已有deleted字段，跳过此步骤）
/*
ALTER TABLE teacher_info 
ADD COLUMN deleted int DEFAULT 0 COMMENT '逻辑删除（0未删除 1已删除）';
*/

-- 2. 如果有del_flag字段，将数据迁移到deleted字段
-- （根据第一步检查结果决定是否执行）
/*
UPDATE teacher_info 
SET deleted = CASE 
    WHEN del_flag = '0' THEN 0
    WHEN del_flag = '2' THEN 1
    ELSE 0
END
WHERE del_flag IS NOT NULL;
*/

-- 3. 确保认证状态数据的一致性
-- 检查是否有异常的认证状态值
SELECT '=== 检查异常认证状态 ===' as info;
SELECT 
    certification_status,
    COUNT(*) as count
FROM teacher_info 
WHERE certification_status NOT IN ('0', '1', '2', '4')
GROUP BY certification_status;

-- 4. 创建标准化视图（用于过渡期间的兼容性）
CREATE OR REPLACE VIEW teacher_info_standard AS
SELECT 
    id,
    real_name,
    nick_name,
    avatar,
    university,
    major,
    grade,
    teaching_subjects,
    self_introduction,
    education_background,
    experience_background,
    certification_status,
    CASE 
        WHEN certification_status = '0' THEN '未认证'
        WHEN certification_status = '1' THEN '学生认证'
        WHEN certification_status = '2' THEN '已认证'
        WHEN certification_status = '4' THEN '已实名认证'
        ELSE CONCAT('状态', certification_status)
    END as certification_text,
    -- 姓名显示格式标准化
    CASE 
        WHEN real_name IS NOT NULL AND real_name != '' 
        THEN CONCAT(LEFT(real_name, 1), '老师')
        ELSE CONCAT(IFNULL(nick_name, '未知'), '老师')
    END as display_name,
    -- 性别显示标准化
    CASE 
        WHEN gender = '0' THEN '男'
        WHEN gender = '1' THEN '女'
        ELSE '未知'
    END as gender_text,
    age,
    rating,
    total_orders,
    success_orders,
    status,
    COALESCE(deleted, 0) as deleted,
    create_time,
    update_time
FROM teacher_info;

-- 5. 验证标准化结果
SELECT '=== 标准化验证 ===' as info;
SELECT 
    certification_status,
    certification_text,
    COUNT(*) as count
FROM teacher_info_standard
GROUP BY certification_status, certification_text
ORDER BY certification_status;

SELECT '=== 数据标准化完成 ===' as result;

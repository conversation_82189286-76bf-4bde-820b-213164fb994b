# TutorOrderMapper修复完成指南

## 🎯 问题分析

### 错误信息
```
Invalid bound statement (not found): com.ruoyi.education.mapper.TutorOrderMapper.selectNewTutorOrderList
```

### 问题根源
1. **MyBatis映射问题**：XML中的SQL语句可能引用了不存在的字段
2. **数据库字段缺失**：`tutor_order`表可能缺少`view_count`、`deleted`等字段
3. **关联表缺失**：`order_application`表可能不存在，影响子查询

---

## ✅ 修复方案

### 1. 数据库字段修复

#### 执行SQL脚本
在数据库中执行：`RuoYi-Vue-master/sql/fix_tutor_order_mapper.sql`

**关键修复内容**：
```sql
-- 添加view_count字段
ALTER TABLE tutor_order ADD COLUMN view_count int(11) DEFAULT 0 COMMENT '浏览次数';

-- 添加deleted字段  
ALTER TABLE tutor_order ADD COLUMN deleted tinyint(1) DEFAULT 0 COMMENT '是否删除（0否 1是）';

-- 创建order_application表（如果不存在）
CREATE TABLE order_application (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
    order_id bigint NOT NULL COMMENT '订单ID', 
    teacher_id bigint NOT NULL COMMENT '教师ID',
    status char(1) DEFAULT '0' COMMENT '状态（0待审核 1已通过 2已拒绝）',
    apply_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_teacher_id (teacher_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单报名表';
```

### 2. MyBatis映射修复

#### 修复selectNewTutorOrderList方法
**修复前**：
```xml
<select id="selectNewTutorOrderList" resultMap="TutorOrderResult">
    <include refid="selectTutorOrderVo"/>
    where o.status = '2'
      and o.selected_teacher_id is null
      and (o.deleted is null or o.deleted = 0)
    order by o.is_top desc, o.create_time desc
</select>
```

**修复后**：
```xml
<select id="selectNewTutorOrderList" resultMap="TutorOrderResult">
    select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode, 
           o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots, 
           o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type, 
           o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone, 
           o.teaching_requirements, o.publisher_type, o.status, o.selected_teacher_id, o.select_time, 
           o.is_top, o.create_time,
           COALESCE(o.view_count, 0) as view_count,
           COALESCE((select count(*) from order_application a where a.order_id = o.id), 0) as apply_count
    from tutor_order o
    where o.status = '2'
      and o.selected_teacher_id is null
      and (o.deleted is null or o.deleted = 0)
    order by o.is_top desc, o.create_time desc
</select>
```

**修复要点**：
- 不再使用`<include refid="selectTutorOrderVo"/>`，直接写完整SQL
- 确保所有字段都存在于数据库表中
- 使用`COALESCE`处理可能为NULL的字段

---

## 🔧 修复步骤

### 步骤1：执行数据库修复脚本
```bash
# 在数据库管理工具中执行
mysql -u username -p database_name < RuoYi-Vue-master/sql/fix_tutor_order_mapper.sql
```

### 步骤2：验证数据库修复
```sql
-- 检查字段是否添加成功
DESCRIBE tutor_order;

-- 检查order_application表是否存在
SHOW TABLES LIKE 'order_application';

-- 测试查询是否正常
SELECT COUNT(*) FROM tutor_order o 
WHERE o.status = '2' 
  AND o.selected_teacher_id IS NULL 
  AND (o.deleted IS NULL OR o.deleted = 0);
```

### 步骤3：重新编译项目
```bash
cd RuoYi-Vue-master
mvn clean compile
```

### 步骤4：重启应用
```bash
.\ry.bat
```

### 步骤5：测试功能
- 访问教师端首页
- 查看"新家教订单"列表
- 确认不再出现MyBatis错误

---

## 📊 字段对应关系

### tutor_order表关键字段
| 数据库字段 | Java属性 | 类型 | 说明 |
|-----------|----------|------|------|
| `view_count` | `viewCount` | `Integer` | 浏览次数 |
| `deleted` | `deleted` | `Integer` | 删除标记 |
| `is_top` | `isTop` | `Integer` | 是否置顶 |
| `selected_teacher_id` | `selectedTeacherId` | `Long` | 选中的教师ID |

### order_application表字段
| 数据库字段 | 说明 |
|-----------|------|
| `id` | 报名ID |
| `order_id` | 订单ID |
| `teacher_id` | 教师ID |
| `status` | 报名状态 |
| `apply_time` | 报名时间 |

---

## 🧪 测试验证

### 1. 数据库验证
```sql
-- 验证字段存在
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME IN ('view_count', 'deleted');

-- 验证表存在
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME = 'order_application';
```

### 2. 应用验证
- ✅ 启动应用无MyBatis错误
- ✅ 教师端首页正常加载
- ✅ 新家教订单列表正常显示
- ✅ 订单数据正确查询

### 3. 功能验证
- ✅ 订单列表分页正常
- ✅ 订单详情显示正确
- ✅ 报名数量统计准确

---

## ⚠️ 注意事项

### 1. 数据库备份
- 执行修复脚本前请备份数据库
- 确保有回滚方案

### 2. 字段默认值
- `view_count`默认为0
- `deleted`默认为0（未删除）
- 新字段不影响现有数据

### 3. 性能考虑
- 子查询可能影响性能
- 建议为`order_application.order_id`添加索引

### 4. 兼容性
- 修复方案向后兼容
- 不影响现有功能

---

## 🎉 预期结果

修复完成后：
- ✅ 不再出现"Invalid bound statement"错误
- ✅ `selectNewTutorOrderList`方法正常工作
- ✅ 教师端首页正常显示新家教订单
- ✅ 订单查询功能完整可用
- ✅ 数据统计准确无误

---

## 🚨 如果仍有问题

### 检查清单
1. **数据库连接**：确认应用能正常连接数据库
2. **表权限**：确认数据库用户有足够权限
3. **字段类型**：确认字段类型与Java属性匹配
4. **编码问题**：确认数据库编码为utf8mb4

### 调试方法
1. **查看日志**：检查MyBatis详细错误信息
2. **SQL测试**：直接在数据库中执行SQL语句
3. **字段检查**：逐一验证每个字段是否存在

现在请按照步骤执行修复，问题应该能够完全解决！🚀

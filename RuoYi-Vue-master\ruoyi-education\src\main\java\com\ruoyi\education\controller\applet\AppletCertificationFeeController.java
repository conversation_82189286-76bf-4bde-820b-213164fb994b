package com.ruoyi.education.controller.applet;

import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.education.domain.TeacherInfo;
import com.ruoyi.education.mapper.TeacherInfoMapper;
import com.ruoyi.education.service.IPlatformConfigService;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.service.WxPayService;

/**
 * 小程序认证费支付控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/applet/teacher/certification")
public class AppletCertificationFeeController extends BaseController {

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private IPlatformConfigService platformConfigService;

    @Autowired
    private TeacherInfoMapper teacherInfoMapper;

    /**
     * 获取认证费信息
     */
    @GetMapping("/fee-info")
    public AjaxResult getCertificationFeeInfo() {
        try {
            LoginUser loginUser = getLoginUser();
            System.out.println("=== 获取认证费用信息开始 ===");
            System.out.println("用户ID: " + loginUser.getUserId());

            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(loginUser.getUserId());
            System.out.println("教师信息: " + (teacherInfo != null ? "存在" : "不存在"));

            String feeAmount = platformConfigService.selectConfigValueByKey("teacher.certification.fee", "0");
            System.out.println("从配置获取的认证费用: " + feeAmount);

            String feePaidStatus = teacherInfo != null ? teacherInfo.getCertificationFeePaid() : "0";
            System.out.println("教师认证费用缴纳状态: " + feePaidStatus);

            AjaxResult result = AjaxResult.success();
            result.put("feeAmount", feeAmount);
            result.put("feePaidStatus", feePaidStatus);

            System.out.println("返回结果: " + result);
            System.out.println("=== 获取认证费用信息结束 ===");
            return result;
        } catch (Exception e) {
            System.err.println("获取认证费用信息失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("获取认证费用信息失败: " + e.getMessage());
        }
    }

    /**
     * 测试配置获取接口
     */
    @GetMapping("/test-config")
    public AjaxResult testConfig() {
        try {
            System.out.println("=== 测试配置获取 ===");

            // 直接测试配置获取
            String feeAmount = platformConfigService.selectConfigValueByKey("teacher.certification.fee");
            String feeAmountWithDefault = platformConfigService.selectConfigValueByKey("teacher.certification.fee", "0");

            System.out.println("不带默认值的结果: " + feeAmount);
            System.out.println("带默认值的结果: " + feeAmountWithDefault);

            AjaxResult result = AjaxResult.success();
            result.put("feeAmount", feeAmount);
            result.put("feeAmountWithDefault", feeAmountWithDefault);
            result.put("isEmpty", feeAmount == null || feeAmount.isEmpty());

            return result;
        } catch (Exception e) {
            System.err.println("测试配置获取失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 清除配置缓存
     */
    @GetMapping("/clear-cache")
    public AjaxResult clearCache() {
        try {
            platformConfigService.resetConfigCache();
            return AjaxResult.success("缓存清除成功");
        } catch (Exception e) {
            System.err.println("清除缓存失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 查询认证费缴费状态
     */
    @GetMapping("/fee-status")
    public AjaxResult getCertificationFeeStatus() {
        LoginUser loginUser = getLoginUser();
        TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(loginUser.getUserId());
        
        String feeAmount = platformConfigService.selectConfigValueByKey("teacher.certification.fee", "0");
        String feePaidStatus = teacherInfo != null ? teacherInfo.getCertificationFeePaid() : "0";

        AjaxResult result = AjaxResult.success();
        result.put("feeAmount", feeAmount);
        result.put("feePaidStatus", feePaidStatus);
        return result;
    }

    /**
     * 创建认证费支付订单
     */
    @PostMapping("/create-order")
    @Log(title = "创建认证费支付订单", businessType = BusinessType.INSERT)
    public AjaxResult createCertificationFeeOrder() throws Exception {
        try {
            LoginUser loginUser = getLoginUser();
            System.out.println("=== 创建认证费支付订单开始 ===");
            System.out.println("用户ID: " + loginUser.getUserId());

            // 检查是否已经缴纳
            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(loginUser.getUserId());
            if (teacherInfo == null) {
                System.out.println("教师信息不存在");
                return AjaxResult.error("教师信息不存在");
            }

            System.out.println("教师openid: " + teacherInfo.getOpenid());
            System.out.println("教师缴费状态: " + teacherInfo.getCertificationFeePaid());

            if ("1".equals(teacherInfo.getCertificationFeePaid())) {
                return AjaxResult.error("您已缴纳认证费，无需重复缴纳");
            }

            // 获取openid - 从TeacherInfo中获取
            String openid = teacherInfo.getOpenid();
            if (openid == null || openid.isEmpty()) {
                System.out.println("教师openid为空");
                return AjaxResult.error("用户openid不存在，请重新登录");
            }

        // 1. 从平台配置中获取费用金额
        String feeString = platformConfigService.selectConfigValueByKey("teacher.certification.fee", "0");
        if (feeString == null || feeString.isEmpty() || "0".equals(feeString)) {
            return AjaxResult.error("认证费金额配置不存在或为0");
        }
        
        BigDecimal fee = new BigDecimal(feeString);
        int feeInCent = fee.multiply(new BigDecimal("100")).intValue();

        // 2. 构建微信支付请求
        WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
        request.setBody("教师认证费 - 身份认证费用");
        request.setOutTradeNo("CERT_FEE_" + System.currentTimeMillis()); // 生成唯一订单号
        request.setTotalFee(feeInCent); // 金额，单位为分
        request.setSpbillCreateIp("127.0.0.1"); // 终端IP

        // 设置完整的回调URL - 使用natapp域名
        String notifyUrl = "http://t8f63f47.natappfree.cc/applet/teacher/certification/notify";
        request.setNotifyUrl(notifyUrl);
        System.out.println("设置的回调URL: " + notifyUrl);

        request.setTradeType("JSAPI");
        request.setOpenid(openid);

        WxPayMpOrderResult result = wxPayService.createOrder(request);
        System.out.println("微信支付订单创建成功: " + result);
        System.out.println("=== 创建认证费支付订单结束 ===");
        return AjaxResult.success(result);

        } catch (Exception e) {
            System.err.println("创建认证费支付订单失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取认证费缴费记录
     */
    @GetMapping("/fee-record")
    public AjaxResult getCertificationFeeRecord() {
        LoginUser loginUser = getLoginUser();
        TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(loginUser.getUserId());
        
        if (teacherInfo == null || !"1".equals(teacherInfo.getCertificationFeePaid())) {
            return AjaxResult.success("暂无缴费记录");
        }

        // 这里可以返回更详细的缴费记录信息
        AjaxResult result = AjaxResult.success();
        result.put("feeAmount", platformConfigService.selectConfigValueByKey("teacher.certification.fee", "0"));
        result.put("paymentTime", teacherInfo.getUpdateTime()); // 使用更新时间作为缴费时间
        result.put("status", "已缴纳");
        return result;
    }
    
    /**
     * 认证费支付回调
     */
    @PostMapping("/notify")
    public String handleCertificationFeeNotify(@RequestBody String xmlData) throws Exception {
        try {
            System.out.println("=== 收到微信支付回调 ===");
            System.out.println("回调数据: " + xmlData);

            final WxPayOrderNotifyResult notifyResult = this.wxPayService.parseOrderNotifyResult(xmlData);
            System.out.println("解析后的回调结果: " + notifyResult);

            // 处理业务逻辑：更新教师认证费缴费状态
            String outTradeNo = notifyResult.getOutTradeNo();
            String openid = notifyResult.getOpenid();

            System.out.println("订单号: " + outTradeNo);
            System.out.println("用户openid: " + openid);

            // 根据openid查找教师并更新缴费状态
            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoByOpenid(openid);
            System.out.println("查找到的教师信息: " + (teacherInfo != null ? "存在，ID=" + teacherInfo.getId() : "不存在"));

            if (teacherInfo != null) {
                System.out.println("更新前的缴费状态: " + teacherInfo.getCertificationFeePaid());
                teacherInfo.setCertificationFeePaid("1");
                int updateResult = teacherInfoMapper.updateTeacherInfo(teacherInfo);
                System.out.println("数据库更新结果: " + updateResult);

                // 验证更新是否成功
                TeacherInfo updatedTeacher = teacherInfoMapper.selectTeacherInfoById(teacherInfo.getId());
                System.out.println("更新后的缴费状态: " + updatedTeacher.getCertificationFeePaid());

                System.out.println("教师认证费缴费成功，教师ID: " + teacherInfo.getId() + ", 订单号: " + outTradeNo);
            } else {
                System.err.println("根据openid未找到教师信息: " + openid);
            }

            System.out.println("=== 微信支付回调处理完成 ===");
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";

        } catch (Exception e) {
            System.err.println("处理微信支付回调失败: " + e.getMessage());
            e.printStackTrace();
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>";
        }
    }

    /**
     * 手动更新缴费状态（测试用）
     */
    @PostMapping("/update-fee-status")
    public AjaxResult updateFeeStatus() {
        try {
            LoginUser loginUser = getLoginUser();
            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(loginUser.getUserId());

            if (teacherInfo == null) {
                return AjaxResult.error("教师信息不存在");
            }

            System.out.println("=== 手动更新缴费状态 ===");
            System.out.println("教师ID: " + teacherInfo.getId());
            System.out.println("更新前状态: " + teacherInfo.getCertificationFeePaid());

            teacherInfo.setCertificationFeePaid("1");
            int updateResult = teacherInfoMapper.updateTeacherInfo(teacherInfo);

            System.out.println("数据库更新结果: " + updateResult);

            // 验证更新结果
            TeacherInfo updatedTeacher = teacherInfoMapper.selectTeacherInfoById(teacherInfo.getId());
            System.out.println("更新后状态: " + updatedTeacher.getCertificationFeePaid());

            return AjaxResult.success("缴费状态更新成功");

        } catch (Exception e) {
            System.err.println("更新缴费状态失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }
}

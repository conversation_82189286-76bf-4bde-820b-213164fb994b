package com.ruoyi.education.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.education.mapper.TeacherMembershipMapper;
import com.ruoyi.education.domain.TeacherMembership;
import com.ruoyi.education.service.ITeacherMembershipService;

/**
 * 教师会员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-30
 */
@Service
public class TeacherMembershipServiceImpl implements ITeacherMembershipService 
{
    @Autowired
    private TeacherMembershipMapper teacherMembershipMapper;

    /**
     * 查询教师会员
     * 
     * @param id 教师会员主键
     * @return 教师会员
     */
    @Override
    public TeacherMembership selectTeacherMembershipById(Long id)
    {
        return teacherMembershipMapper.selectTeacherMembershipById(id);
    }

    /**
     * 根据教师ID查询有效的会员信息
     * 
     * @param teacherId 教师ID
     * @return 教师会员
     */
    @Override
    public TeacherMembership selectActiveByTeacherId(Long teacherId)
    {
        return teacherMembershipMapper.selectActiveByTeacherId(teacherId);
    }

    /**
     * 查询教师会员列表
     * 
     * @param teacherMembership 教师会员
     * @return 教师会员
     */
    @Override
    public List<TeacherMembership> selectTeacherMembershipList(TeacherMembership teacherMembership)
    {
        return teacherMembershipMapper.selectTeacherMembershipList(teacherMembership);
    }

    /**
     * 新增教师会员
     * 
     * @param teacherMembership 教师会员
     * @return 结果
     */
    @Override
    public int insertTeacherMembership(TeacherMembership teacherMembership)
    {
        return teacherMembershipMapper.insertTeacherMembership(teacherMembership);
    }

    /**
     * 修改教师会员
     * 
     * @param teacherMembership 教师会员
     * @return 结果
     */
    @Override
    public int updateTeacherMembership(TeacherMembership teacherMembership)
    {
        return teacherMembershipMapper.updateTeacherMembership(teacherMembership);
    }

    /**
     * 批量删除教师会员
     * 
     * @param ids 需要删除的教师会员主键
     * @return 结果
     */
    @Override
    public int deleteTeacherMembershipByIds(Long[] ids)
    {
        return teacherMembershipMapper.deleteTeacherMembershipByIds(ids);
    }

    /**
     * 删除教师会员信息
     * 
     * @param id 教师会员主键
     * @return 结果
     */
    @Override
    public int deleteTeacherMembershipById(Long id)
    {
        return teacherMembershipMapper.deleteTeacherMembershipById(id);
    }

    /**
     * 检查教师是否为有效VIP会员
     *
     * @param teacherId 教师ID
     * @return 是否为VIP
     */
    @Override
    public boolean isValidVip(Long teacherId)
    {
        TeacherMembership membership = selectActiveByTeacherId(teacherId);
        if (membership == null) {
            return false;
        }

        // 检查会员类型是否为高级会员（永久有效）
        return "2".equals(membership.getMembershipType());
    }
}

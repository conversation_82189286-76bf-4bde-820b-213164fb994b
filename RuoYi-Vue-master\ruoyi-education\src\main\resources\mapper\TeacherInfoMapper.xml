<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherInfoMapper">

    <resultMap type="TeacherInfo" id="TeacherInfoResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="userName"    column="username"    />
        <result property="nickName"    column="nick_name"    />
        <result property="realName"    column="real_name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="phone"    column="phone_number"    />
        <result property="wechatNumber"    column="wechat_number"    />
        <result property="gender"    column="gender"    />
        <result property="age"    column="age"    />
        <result property="birthday"    column="birthday"    />
        <result property="avatar"    column="avatar"    />
        <result property="password"    column="password"    />
        <result property="idCard"    column="id_card"    />
        <result property="university"    column="university"    />
        <result property="major"    column="major"    />
        <result property="grade"    column="grade"    />
        <result property="studentId"    column="student_id"    />
        <result property="educationBackground"    column="education_background"    />
        <result property="experienceBackground"    column="experience_background"    />
        <result property="teachingSubjects"    column="teaching_subjects"    />
        <result property="selfIntroduction"    column="self_introduction"    />
        <result property="certificationStatus"    column="certification_status"    />
        <result property="certificationFeePaid"    column="certification_fee_paid"    />
        <result property="depositFeePaid"    column="deposit_fee_paid"    />
        <result property="totalOrders"    column="total_orders"    />
        <result property="successOrders"    column="success_orders"    />
        <result property="successExperience"    column="success_experience"    />
        <result property="rating"    column="rating"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="blacklistStatus"    column="blacklist_status"    />
        <result property="blacklistReason"    column="blacklist_reason"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginDate"    column="login_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTeacherInfoVo">
        select id, openid, username, nick_name, real_name, phone_number, wechat_number, gender, age, birthday, avatar, password, id_card, university, major, grade, student_id, education_background, experience_background, teaching_subjects, self_introduction, certification_status, certification_fee_paid, deposit_fee_paid, total_orders, success_orders, success_experience, rating, area_code, area_name, blacklist_status, blacklist_reason, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark from teacher_info
    </sql>

    <select id="selectTeacherInfoList" parameterType="TeacherInfo" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        <where>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="phone != null  and phone != ''"> and phone_number = #{phone}</if>
            <if test="blacklistStatus != null  and blacklistStatus != ''"> and blacklist_status = #{blacklistStatus}</if>
        </where>
    </select>

    <select id="selectTeacherInfoById" parameterType="Long" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectTeacherInfoByOpenid" parameterType="String" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        where openid = #{openid}
    </select>

    <select id="selectTeacherInfoByPhoneNumber" parameterType="String" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        where phone_number = #{phoneNumber}
    </select>

    <insert id="insertTeacherInfo" parameterType="TeacherInfo" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">username,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="realName != null and realName != ''">real_name,</if>
            <if test="phone != null and phone != ''">phone_number,</if>
            <if test="wechatNumber != null and wechatNumber != ''">wechat_number,</if>
            <if test="gender != null">gender,</if>
            <if test="age != null">age,</if>
            <if test="birthday != null">birthday,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="university != null and university != ''">university,</if>
            <if test="major != null and major != ''">major,</if>
            <if test="grade != null and grade != ''">grade,</if>
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="educationBackground != null">education_background,</if>
            <if test="experienceBackground != null">experience_background,</if>
            <if test="teachingSubjects != null">teaching_subjects,</if>
            <if test="selfIntroduction != null">self_introduction,</if>
            <if test="certificationStatus != null">certification_status,</if>
            <if test="certificationFeePaid != null">certification_fee_paid,</if>
            <if test="depositFeePaid != null">deposit_fee_paid,</if>
            <if test="areaCode != null and areaCode != ''">area_code,</if>
            <if test="areaName != null and areaName != ''">area_name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="realName != null and realName != ''">#{realName},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="wechatNumber != null and wechatNumber != ''">#{wechatNumber},</if>
            <if test="gender != null">#{gender},</if>
            <if test="age != null">#{age},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="university != null and university != ''">#{university},</if>
            <if test="major != null and major != ''">#{major},</if>
            <if test="grade != null and grade != ''">#{grade},</if>
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="educationBackground != null">#{educationBackground},</if>
            <if test="experienceBackground != null">#{experienceBackground},</if>
            <if test="teachingSubjects != null">#{teachingSubjects},</if>
            <if test="selfIntroduction != null">#{selfIntroduction},</if>
            <if test="certificationStatus != null">#{certificationStatus},</if>
            <if test="certificationFeePaid != null">#{certificationFeePaid},</if>
            <if test="depositFeePaid != null">#{depositFeePaid},</if>
            <if test="areaCode != null and areaCode != ''">#{areaCode},</if>
            <if test="areaName != null and areaName != ''">#{areaName},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateTeacherInfo" parameterType="TeacherInfo">
        update teacher_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">username = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="phone != null and phone != ''">phone_number = #{phone},</if>
            <if test="wechatNumber != null">wechat_number = #{wechatNumber},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="age != null">age = #{age},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="university != null">university = #{university},</if>
            <if test="major != null">major = #{major},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="educationBackground != null">education_background = #{educationBackground},</if>
            <if test="experienceBackground != null">experience_background = #{experienceBackground},</if>
            <if test="teachingSubjects != null">teaching_subjects = #{teachingSubjects},</if>
            <if test="selfIntroduction != null">self_introduction = #{selfIntroduction},</if>
            <if test="certificationStatus != null">certification_status = #{certificationStatus},</if>
            <if test="certificationFeePaid != null">certification_fee_paid = #{certificationFeePaid},</if>
            <if test="depositFeePaid != null">deposit_fee_paid = #{depositFeePaid},</if>
            <if test="totalOrders != null">total_orders = #{totalOrders},</if>
            <if test="successOrders != null">success_orders = #{successOrders},</if>
            <if test="successExperience != null">success_experience = #{successExperience},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="blacklistStatus != null">blacklist_status = #{blacklistStatus},</if>
            <if test="blacklistReason != null">blacklist_reason = #{blacklistReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <!-- 单独更新教师认证状态 -->
    <update id="updateTeacherCertificationStatus">
        update teacher_info
        set
            certification_status = #{certificationStatus},
            update_time = sysdate(),
            update_by = #{updateBy}
        where id = #{teacherId}
    </update>

    <delete id="deleteTeacherInfoById" parameterType="Long">
        delete from teacher_info where id = #{id}
    </delete>

    <delete id="deleteTeacherInfoByIds" parameterType="String">
        delete from teacher_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据教师ID查询所教的科目列表 -->
    <select id="selectSubjectsByTeacherId" parameterType="Long" resultMap="com.ruoyi.education.mapper.SubjectMapper.SubjectResult">
        SELECT s.id, s.subject_name, s.sort_order
        FROM course_subject s
                 JOIN teacher_subject ts ON s.id = ts.subject_id
        WHERE ts.teacher_id = #{teacherId}
        ORDER BY s.sort_order ASC
    </select>

    <!-- 根据教师ID删除教师-科目关联 -->
    <delete id="deleteTeacherSubjectByTeacherId" parameterType="Long">
        DELETE FROM teacher_subject WHERE teacher_id = #{teacherId}
    </delete>

    <!-- 批量新增教师-科目关联 -->
    <insert id="batchInsertTeacherSubject">
        INSERT INTO teacher_subject(teacher_id, subject_id) VALUES
        <foreach collection="subjectIds" item="subjectId" separator=",">
            (#{teacherId}, #{subjectId})
        </foreach>
    </insert>

    <!-- 批量拉黑教师 -->
    <update id="blacklistTeacherByIds">
        update teacher_info set 
            blacklist_status = '1',
            blacklist_reason = #{blacklistReason},
            update_time = sysdate(),
            update_by = #{updateBy}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量解除拉黑 -->
    <update id="unblacklistTeacherByIds">
        update teacher_info set 
            blacklist_status = '0',
            blacklist_reason = '',
            update_time = sysdate(),
            update_by = #{updateBy}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>

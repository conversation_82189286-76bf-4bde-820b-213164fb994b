{"_from": "named-placeholders@^1.1.3", "_id": "named-placeholders@1.1.3", "_inBundle": false, "_integrity": "sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==", "_location": "/named-placeholders", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "named-placeholders@^1.1.3", "name": "named-placeholders", "escapedName": "named-placeholders", "rawSpec": "^1.1.3", "saveSpec": null, "fetchSpec": "^1.1.3"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmmirror.com/named-placeholders/-/named-placeholders-1.1.3.tgz", "_shasum": "df595799a36654da55dda6152ba7a137ad1d9351", "_spec": "named-placeholders@^1.1.3", "_where": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\node_modules\\mysql2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "bundleDependencies": false, "dependencies": {"lru-cache": "^7.14.1"}, "deprecated": false, "description": "sql named placeholders to unnamed compiler", "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3"}, "engines": {"node": ">=12.0.0"}, "files": [], "homepage": "https://github.com/sidorares/named-placeholders#readme", "keywords": ["sql", "pdo", "named", "placeholders"], "license": "MIT", "main": "index.js", "name": "named-placeholders", "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "scripts": {"test": "mocha"}, "version": "1.1.3"}
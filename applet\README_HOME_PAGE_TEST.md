# 教师端首页功能测试指南

## 功能概述

教师端首页是众桥辅导平台的核心页面，采用简洁的时光家教风格设计，包含位置选择、轮播图、公告和订单展示功能。

## 当前功能特性

### 1. 设计风格
- **简洁布局**: 参考时光家教应用的简洁设计
- **蓝色主题**: 使用 #6c7ae0 作为主题色
- **白色背景**: 干净的白色卡片布局
- **去除筛选**: 简化用户操作，专注核心功能

### 2. 核心功能
- **位置选择**: 左上角城市选择器
- **轮播图**: 顶部图片轮播展示
- **公告滚动**: 重要通知滚动显示
- **订单列表**: 展示可报名的家教订单

## 测试环境准备

### 1. 后端API准备
确保以下API接口已实现：
- `/applet/config/banners` - 轮播图接口
- `/applet/config/announcements` - 公告接口
- `/applet/config/cities` - 城市列表接口
- `/applet/teacher/order/new` - 新订单列表接口

### 2. 启动项目
```bash
# 用微信开发者工具打开 applet 目录
# 确保已配置小程序AppID和后端服务地址
# 编译并预览
```

## 功能测试清单

### 1. 页面基础功能
- [ ] 页面正常加载，显示自定义导航栏
- [ ] 紫色渐变背景正常显示
- [ ] 页面支持下拉刷新
- [ ] 页面支持上拉加载更多

### 2. 位置选择功能
- [ ] 左上角显示当前城市（默认：上海）
- [ ] 点击位置选择器弹出城市列表
- [ ] 选择不同城市后页面数据刷新
- [ ] 城市选择器正常关闭

### 3. 轮播图功能
- [ ] 轮播图正常显示（3张测试图片）
- [ ] 自动播放，5秒切换一次
- [ ] 支持手动滑动切换
- [ ] 圆点指示器正常显示
- [ ] 点击轮播图可以跳转（测试用占位链接）

### 4. 公告功能
- [ ] 公告区域正常显示
- [ ] 公告内容自动滚动切换（3秒间隔）
- [ ] 点击公告显示详细内容弹窗
- [ ] 公告切换动画平滑

### 5. 订单列表功能
- [ ] 订单卡片正常显示
- [ ] 订单信息完整（科目、年级、薪资等）
- [ ] 点击订单卡片跳转到详情页
- [ ] 报名按钮正常显示
- [ ] 空状态正确处理

### 6. 认证费用检查
- [ ] 点击报名按钮触发认证费用检查
- [ ] 未缴费时显示引导弹窗
- [ ] 已缴费时正常跳转到订单详情

## API接口说明

### 轮播图接口 `/applet/config/banners`
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "imageUrl": "图片URL",
      "title": "标题",
      "linkType": "page|web",
      "linkUrl": "跳转链接"
    }
  ]
}
```

### 公告接口 `/applet/config/announcements`
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "title": "公告标题",
      "content": "公告详细内容"
    }
  ]
}
```

### 城市接口 `/applet/config/cities`
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "name": "上海",
      "value": "shanghai"
    }
  ]
}
```

## 常见问题排查

### 1. 页面不显示内容
- 检查是否在开发环境（envVersion === 'develop'）
- 确认模拟数据正确导入
- 查看控制台是否有错误信息

### 2. 轮播图不显示
- 检查图片链接是否有效
- 确认swiper组件配置正确
- 查看CSS样式是否正确应用

### 3. 公告不滚动
- 检查定时器是否正常启动
- 确认公告数据长度大于1
- 查看CSS动画是否正确



## 性能测试

### 1. 加载性能
- 页面首次加载时间应小于2秒
- 切换城市后数据加载应小于1秒
- 滚动列表应该流畅，无卡顿

### 2. 内存使用
- 长时间使用页面，内存使用应稳定
- 切换页面后，定时器应正确清理
- 图片资源应正确释放

## 用户体验测试

### 1. 交互反馈
- 所有可点击元素应有点击反馈
- 加载状态应有明确提示
- 错误状态应有友好提示

### 2. 视觉效果
- 颜色搭配协调，符合品牌风格
- 字体大小适中，易于阅读
- 间距合理，布局清晰

## 测试完成标准

- [ ] 所有功能测试项目通过
- [ ] 无明显性能问题
- [ ] 用户体验良好
- [ ] 错误处理完善
- [ ] 代码无明显bug

## 后续优化建议

1. **图片优化**: 添加图片懒加载和压缩
2. **缓存策略**: 实现数据缓存，提升加载速度
3. **个性化推荐**: 根据教师信息推荐相关订单
4. **消息推送**: 集成新订单通知功能
5. **位置筛选**: 根据选择的城市筛选订单

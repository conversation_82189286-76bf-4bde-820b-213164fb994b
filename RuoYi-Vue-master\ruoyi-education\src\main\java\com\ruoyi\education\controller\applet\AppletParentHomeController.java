package com.ruoyi.education.controller.applet;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.education.domain.ParentInfo;
import com.ruoyi.education.domain.TeacherInfo;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.service.IParentInfoService;
import com.ruoyi.education.service.ITeacherInfoService;
import com.ruoyi.education.service.IEducationExperienceService;
import com.ruoyi.education.service.ITeachingExperienceService;
import com.ruoyi.education.service.ITutorOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序家长端首页控制器
 */
@RestController
@RequestMapping("/applet/parent")
public class AppletParentHomeController extends BaseController {

    @Autowired
    private IParentInfoService parentInfoService;

    @Autowired
    private ITutorOrderService tutorOrderService;

    @Autowired
    private ITeacherInfoService teacherInfoService;

    @Autowired
    private IEducationExperienceService educationExperienceService;

    @Autowired
    private ITeachingExperienceService teachingExperienceService;

    /**
     * 获取家长统计数据
     */
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 统计家长发布的订单数据
            Map<String, Object> stats = new HashMap<>();
            
            // 已发布订单数（所有状态）
            int publishedOrders = tutorOrderService.countOrdersByParentId(parentId);
            stats.put("publishedOrders", publishedOrders);
            
            // 进行中订单数（已选择教师，正在进行）
            int ongoingOrders = tutorOrderService.countOngoingOrdersByParentId(parentId);
            stats.put("ongoingOrders", ongoingOrders);
            
            // 已完成订单数
            int completedOrders = tutorOrderService.countCompletedOrdersByParentId(parentId);
            stats.put("completedOrders", completedOrders);

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取家长统计数据失败", e);
            return AjaxResult.error("获取统计数据失败");
        }
    }

    /**
     * 获取家长最近订单
     */
    @GetMapping("/orders/recent")
    public AjaxResult getRecentOrders() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 获取最近5个订单
            List<TutorOrder> recentOrders = tutorOrderService.selectRecentOrdersByParentId(parentId, 5);
            
            return AjaxResult.success(recentOrders);
        } catch (Exception e) {
            logger.error("获取家长最近订单失败", e);
            return AjaxResult.error("获取最近订单失败");
        }
    }

    /**
     * 获取推荐教师
     */
    @GetMapping("/teachers/recommend")
    public AjaxResult getRecommendTeachers() {
        try {
            logger.info("=== 开始获取推荐教师列表 ===");

            // 使用标准的TeacherInfoService查询
            TeacherInfo queryParam = new TeacherInfo();
            queryParam.setStatus("0"); // 正常状态
            queryParam.setCertificationStatus("4"); // 已实名认证

            List<TeacherInfo> teacherList = teacherInfoService.selectTeacherInfoList(queryParam);
            logger.info("从数据库获取到推荐教师数量: {}", teacherList != null ? teacherList.size() : 0);

            // 转换为前端需要的格式
            List<Map<String, Object>> recommendTeachers = new ArrayList<>();

            if (teacherList != null && !teacherList.isEmpty()) {
                for (TeacherInfo teacher : teacherList) {
                    Map<String, Object> teacherMap = new HashMap<>();

                    // 基本信息
                    teacherMap.put("id", teacher.getId());
                    teacherMap.put("real_name", teacher.getRealName());
                    teacherMap.put("nick_name", teacher.getNickName());

                    // 显示姓名：真实姓名第一个字+老师
                    String displayName = teacher.getRealName() != null && !teacher.getRealName().isEmpty()
                        ? teacher.getRealName().substring(0, 1) + "老师"
                        : (teacher.getNickName() != null ? teacher.getNickName() + "老师" : "未知老师");
                    teacherMap.put("display_name", displayName);

                    // 其他信息
                    teacherMap.put("avatar", teacher.getAvatar());

                    // 根据生日计算年龄
                    Integer age = teacher.getAge();
                    if (teacher.getBirthday() != null) {
                        java.time.LocalDate birthDate = teacher.getBirthday().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        java.time.LocalDate currentDate = java.time.LocalDate.now();
                        age = java.time.Period.between(birthDate, currentDate).getYears();
                    }
                    teacherMap.put("age", age);

                    // 联表查询科目信息
                    List<com.ruoyi.education.domain.Subject> subjects = teacherInfoService.selectSubjectsByTeacherId(teacher.getId());
                    String subjectNames = subjects.stream()
                        .map(subject -> subject.getName())
                        .collect(java.util.stream.Collectors.joining(","));
                    teacherMap.put("teaching_subjects", subjectNames.isEmpty() ? "暂未设置" : subjectNames);

                    teacherMap.put("self_introduction", teacher.getSelfIntroduction());

                    // 认证状态
                    teacherMap.put("certification_status", teacher.getCertificationStatus());
                    String certText = "4".equals(teacher.getCertificationStatus()) ? "已实名认证" : "已认证";
                    teacherMap.put("certification_text", certText);

                    recommendTeachers.add(teacherMap);

                    logger.info("教师详情: ID={}, 显示姓名={}, 年龄={}, 科目={}",
                        teacher.getId(), displayName, age, subjectNames);
                }
                logger.info("=== 推荐教师数据转换完成 ===");
            } else {
                logger.warn("没有找到符合条件的推荐教师（已实名认证且状态正常）");
            }

            return AjaxResult.success(recommendTeachers);
        } catch (Exception e) {
            logger.error("获取推荐教师失败", e);
            return AjaxResult.error("获取推荐教师失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师详情
     */
    @GetMapping("/teachers/{teacherId}")
    public AjaxResult getTeacherDetail(@PathVariable Long teacherId) {
        try {
            logger.info("=== 开始获取教师详情，教师ID: {} ===", teacherId);

            if (teacherId == null) {
                return AjaxResult.error("教师ID不能为空");
            }

            // 使用标准的TeacherInfoService查询
            TeacherInfo teacher = teacherInfoService.selectTeacherInfoById(teacherId);

            if (teacher == null || !"0".equals(teacher.getStatus())) {
                logger.warn("未找到教师详情或教师已停用，教师ID: {}", teacherId);
                return AjaxResult.error("教师不存在或已被删除");
            }

            // 转换为前端需要的格式
            Map<String, Object> teacherDetail = new HashMap<>();

            // 基本信息
            teacherDetail.put("id", teacher.getId());
            teacherDetail.put("real_name", teacher.getRealName());
            teacherDetail.put("nick_name", teacher.getNickName());

            // 显示姓名：真实姓名第一个字+老师
            String displayName = teacher.getRealName() != null && !teacher.getRealName().isEmpty()
                ? teacher.getRealName().substring(0, 1) + "老师"
                : (teacher.getNickName() != null ? teacher.getNickName() + "老师" : "未知老师");
            teacherDetail.put("display_name", displayName);

            // 详细信息
            teacherDetail.put("avatar", teacher.getAvatar());

            // 根据生日计算年龄
            Integer age = teacher.getAge();
            if (teacher.getBirthday() != null) {
                java.time.LocalDate birthDate = teacher.getBirthday().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                java.time.LocalDate currentDate = java.time.LocalDate.now();
                age = java.time.Period.between(birthDate, currentDate).getYears();
            }
            teacherDetail.put("age", age);

            teacherDetail.put("gender", teacher.getGender());
            teacherDetail.put("gender_text", "0".equals(teacher.getGender()) ? "男" : "1".equals(teacher.getGender()) ? "女" : "未知");
            // 联表查询科目信息
            List<com.ruoyi.education.domain.Subject> subjects = teacherInfoService.selectSubjectsByTeacherId(teacher.getId());
            String subjectNames = subjects.stream()
                .map(subject -> subject.getName())
                .collect(java.util.stream.Collectors.joining(","));
            teacherDetail.put("teaching_subjects", subjectNames.isEmpty() ? "暂未设置" : subjectNames);

            // 联表查询教育背景
            com.ruoyi.education.domain.EducationExperience queryEducation = new com.ruoyi.education.domain.EducationExperience();
            queryEducation.setTeacherId(teacher.getId());
            List<com.ruoyi.education.domain.EducationExperience> educationList = educationExperienceService.selectEducationExperienceList(queryEducation);
            String educationBackground = educationList.stream()
                .map(edu -> {
                    StringBuilder sb = new StringBuilder();
                    sb.append(edu.getSchool()).append("-").append(edu.getDegree()).append("-").append(edu.getMajor());
                    if (edu.getStartDate() != null || edu.getEndDate() != null) {
                        sb.append("(");
                        if (edu.getStartDate() != null) {
                            sb.append(new java.text.SimpleDateFormat("yyyy-MM").format(edu.getStartDate()));
                        }
                        sb.append("至");
                        if (edu.getEndDate() != null) {
                            sb.append(new java.text.SimpleDateFormat("yyyy-MM").format(edu.getEndDate()));
                        }
                        sb.append(")");
                    }
                    return sb.toString();
                })
                .collect(java.util.stream.Collectors.joining("; "));
            teacherDetail.put("education_background", educationBackground.isEmpty() ? "暂未填写" : educationBackground);

            // 联表查询教学经历
            com.ruoyi.education.domain.TeachingExperience queryExperience = new com.ruoyi.education.domain.TeachingExperience();
            queryExperience.setTeacherId(teacher.getId());
            List<com.ruoyi.education.domain.TeachingExperience> experienceList = teachingExperienceService.selectTeachingExperienceList(queryExperience);
            String experienceBackground = experienceList.stream()
                .map(exp -> {
                    StringBuilder sb = new StringBuilder();
                    sb.append(exp.getTeachingTarget());
                    if (exp.getStartDate() != null || exp.getEndDate() != null) {
                        sb.append("(");
                        if (exp.getStartDate() != null) {
                            sb.append(new java.text.SimpleDateFormat("yyyy-MM").format(exp.getStartDate()));
                        }
                        sb.append("至");
                        if (exp.getEndDate() != null) {
                            sb.append(new java.text.SimpleDateFormat("yyyy-MM").format(exp.getEndDate()));
                        }
                        sb.append(")");
                    }
                    if (exp.getContent() != null && !exp.getContent().isEmpty()) {
                        sb.append(": ").append(exp.getContent());
                    }
                    return sb.toString();
                })
                .collect(java.util.stream.Collectors.joining("; "));
            teacherDetail.put("experience_background", experienceBackground.isEmpty() ? "暂未填写" : experienceBackground);
            teacherDetail.put("self_introduction", teacher.getSelfIntroduction());
            teacherDetail.put("phone_number", teacher.getPhoneNumber());
            teacherDetail.put("wechat_number", teacher.getWechatNumber());
            teacherDetail.put("area_name", teacher.getAreaName());

            // 统计信息（移除评分）
            teacherDetail.put("total_orders", teacher.getTotalOrders() != null ? teacher.getTotalOrders() : 0);
            teacherDetail.put("success_orders", teacher.getSuccessOrders() != null ? teacher.getSuccessOrders() : 0);
            teacherDetail.put("success_experience", teacher.getSuccessExperience());

            // 认证状态
            teacherDetail.put("certification_status", teacher.getCertificationStatus());
            String certText = "4".equals(teacher.getCertificationStatus()) ? "已实名认证" :
                             "2".equals(teacher.getCertificationStatus()) ? "已认证" : "未认证";
            teacherDetail.put("certification_text", certText);

            teacherDetail.put("create_time", teacher.getCreateTime());

            logger.info("教师详情获取成功: ID={}, 显示姓名={}, 年龄={}, 性别={}, 学校={}, 专业={}, 科目={}, 认证状态={}",
                teacher.getId(), displayName, age,
                teacherDetail.get("gender_text"), teacher.getUniversity(),
                teacher.getMajor(), subjectNames, certText);

            return AjaxResult.success(teacherDetail);
        } catch (Exception e) {
            logger.error("获取教师详情失败，教师ID: {}", teacherId, e);
            return AjaxResult.error("获取教师详情失败: " + e.getMessage());
        }
    }




    /**
     * 创建家教订单
     */
    @PostMapping("/orders/create")
    @Log(title = "家长创建订单", businessType = BusinessType.INSERT)
    public AjaxResult createOrder(@RequestBody TutorOrder order) {
        try {
            LoginUser loginUser = getLoginUser();

            if (loginUser == null) {
                return AjaxResult.error(401, "用户未登录，请重新登录");
            }

            Long parentId = loginUser.getParentId();
            if (parentId == null) {
                Long teacherId = loginUser.getTeacherId();
                if (teacherId != null) {
                    return AjaxResult.error(403, "当前登录用户为教师，无法访问家长功能");
                }
                return AjaxResult.error(401, "家长信息缺失，请重新登录");
            }

            // 设置订单基本信息
            order.setPublisherId(parentId);
            order.setPublisherType("1"); // 家长发布
            order.setOrderType("1"); // 家教订单
            order.setStatus("1"); // 待审核
            order.setCreateBy(loginUser.getUsername());

            // 生成订单号
            String orderNo = "ORDER_" + System.currentTimeMillis();
            order.setOrderNo(orderNo);

            int result = tutorOrderService.insertTutorOrder(order);

            if (result > 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", order.getId());
                response.put("orderNo", orderNo);
                return AjaxResult.success("订单创建成功", response);
            } else {
                return AjaxResult.error("订单创建失败");
            }
        } catch (Exception e) {
            logger.error("创建家教订单失败", e);
            return AjaxResult.error("创建订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取家长个人信息
     */
    @GetMapping("/profile")
    public AjaxResult getProfile() {
        try {
            LoginUser loginUser = getLoginUser();

            if (loginUser == null) {
                return AjaxResult.error(401, "用户未登录，请重新登录");
            }

            Long parentId = loginUser.getParentId();
            if (parentId == null) {
                Long teacherId = loginUser.getTeacherId();
                if (teacherId != null) {
                    return AjaxResult.error(403, "当前登录用户为教师，无法访问家长功能");
                }
                return AjaxResult.error(401, "家长信息缺失，请重新登录");
            }

            ParentInfo parentInfo = parentInfoService.selectParentInfoById(parentId);
            return AjaxResult.success(parentInfo);
        } catch (Exception e) {
            logger.error("获取家长个人信息失败", e);
            return AjaxResult.error("获取个人信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新家长个人信息（仅允许更新姓名和头像）
     */
    @PutMapping("/profile")
    @Log(title = "更新家长信息", businessType = BusinessType.UPDATE)
    public AjaxResult updateProfile(@RequestBody ParentInfo parentInfo) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 只允许更新特定字段：姓名和头像
            ParentInfo updateInfo = new ParentInfo();
            updateInfo.setId(parentId);
            updateInfo.setNickName(parentInfo.getNickName());
            updateInfo.setAvatar(parentInfo.getAvatar());
            updateInfo.setUpdateBy(loginUser.getUsername());

            int result = parentInfoService.updateParentInfo(updateInfo);

            if (result > 0) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新家长个人信息失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取家长发布的所有订单列表
     */
    @GetMapping("/orders/all")
    public AjaxResult getAllOrders(@RequestParam(defaultValue = "1") int pageNum,
                                   @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长发布的所有订单
            List<TutorOrder> orders = tutorOrderService.selectOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长订单列表失败", e);
            return AjaxResult.error("获取订单列表失败");
        }
    }

    /**
     * 获取家长进行中的订单列表
     */
    @GetMapping("/orders/ongoing")
    public AjaxResult getOngoingOrders(@RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();

            // 增加调试信息
            logger.info("获取家长进行中订单 - LoginUser: {}", loginUser != null ? "存在" : "null");
            if (loginUser != null) {
                logger.info("LoginUser详情 - UserId: {}, ParentId: {}, TeacherId: {}",
                    loginUser.getUserId(), loginUser.getParentId(), loginUser.getTeacherId());
            }

            if (loginUser == null) {
                return AjaxResult.error(401, "用户未登录，请重新登录");
            }

            Long parentId = loginUser.getParentId();
            if (parentId == null) {
                // 检查是否是教师用户误访问了家长接口
                Long teacherId = loginUser.getTeacherId();
                if (teacherId != null) {
                    return AjaxResult.error(403, "当前登录用户为教师，无法访问家长功能");
                }
                return AjaxResult.error(401, "家长信息缺失，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长进行中的订单
            List<TutorOrder> orders = tutorOrderService.selectOngoingOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长进行中订单失败", e);
            return AjaxResult.error("获取进行中订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取家长已完成的订单列表
     */
    @GetMapping("/orders/completed")
    public AjaxResult getCompletedOrders(@RequestParam(defaultValue = "1") int pageNum,
                                         @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();

            if (loginUser == null) {
                return AjaxResult.error(401, "用户未登录，请重新登录");
            }

            Long parentId = loginUser.getParentId();
            if (parentId == null) {
                Long teacherId = loginUser.getTeacherId();
                if (teacherId != null) {
                    return AjaxResult.error(403, "当前登录用户为教师，无法访问家长功能");
                }
                return AjaxResult.error(401, "家长信息缺失，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长已完成的订单
            List<TutorOrder> orders = tutorOrderService.selectCompletedOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长已完成订单失败", e);
            return AjaxResult.error("获取已完成订单失败: " + e.getMessage());
        }
    }



    /**
     * 获取当前登录用户（重写父类方法以添加异常处理）
     */
    @Override
    public LoginUser getLoginUser() {
        try {
            return super.getLoginUser();
        } catch (Exception e) {
            logger.error("获取登录用户失败", e);
            return null;
        }
    }
}

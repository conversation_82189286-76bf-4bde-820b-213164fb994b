package com.ruoyi.education.controller.applet;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.education.domain.ParentInfo;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.service.IParentInfoService;
import com.ruoyi.education.service.ITutorOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序家长端首页控制器
 */
@RestController
@RequestMapping("/applet/parent")
public class AppletParentHomeController extends BaseController {

    @Autowired
    private IParentInfoService parentInfoService;

    @Autowired
    private ITutorOrderService tutorOrderService;

    /**
     * 获取家长统计数据
     */
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 统计家长发布的订单数据
            Map<String, Object> stats = new HashMap<>();
            
            // 已发布订单数（所有状态）
            int publishedOrders = tutorOrderService.countOrdersByParentId(parentId);
            stats.put("publishedOrders", publishedOrders);
            
            // 进行中订单数（已选择教师，正在进行）
            int ongoingOrders = tutorOrderService.countOngoingOrdersByParentId(parentId);
            stats.put("ongoingOrders", ongoingOrders);
            
            // 已完成订单数
            int completedOrders = tutorOrderService.countCompletedOrdersByParentId(parentId);
            stats.put("completedOrders", completedOrders);

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取家长统计数据失败", e);
            return AjaxResult.error("获取统计数据失败");
        }
    }

    /**
     * 获取家长最近订单
     */
    @GetMapping("/orders/recent")
    public AjaxResult getRecentOrders() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 获取最近5个订单
            List<TutorOrder> recentOrders = tutorOrderService.selectRecentOrdersByParentId(parentId, 5);
            
            return AjaxResult.success(recentOrders);
        } catch (Exception e) {
            logger.error("获取家长最近订单失败", e);
            return AjaxResult.error("获取最近订单失败");
        }
    }

    /**
     * 获取推荐教师
     */
    @GetMapping("/teachers/recommend")
    public AjaxResult getRecommendTeachers() {
        try {
            logger.info("开始获取推荐教师列表");

            List<Map<String, Object>> recommendTeachers = null;

            try {
                // 尝试从数据库获取推荐教师
                recommendTeachers = tutorOrderService.getRecommendTeachers(10);
                logger.info("从数据库获取到推荐教师数量: {}", recommendTeachers != null ? recommendTeachers.size() : 0);
            } catch (Exception dbError) {
                logger.warn("从数据库获取推荐教师失败: {}", dbError.getMessage());
            }

            // 如果数据库没有数据，返回模拟数据
            if (recommendTeachers == null || recommendTeachers.isEmpty()) {
                logger.info("返回模拟推荐教师数据");
                recommendTeachers = getMockRecommendTeachers();
            }

            return AjaxResult.success(recommendTeachers);
        } catch (Exception e) {
            logger.error("获取推荐教师失败", e);
            return AjaxResult.error("获取推荐教师失败: " + e.getMessage());
        }
    }

    /**
     * 获取模拟推荐教师数据
     */
    private List<Map<String, Object>> getMockRecommendTeachers() {
        List<Map<String, Object>> mockTeachers = new ArrayList<>();

        Map<String, Object> teacher1 = new HashMap<>();
        teacher1.put("id", 1);
        teacher1.put("name", "唐老师");
        teacher1.put("avatar", "");
        teacher1.put("university", "湘潭大学");
        teacher1.put("major", "本科生");
        teacher1.put("grade", "大三");
        teacher1.put("subject", "英语,语文,生物,物理,外语(除英语)");
        teacher1.put("experience", "经验丰富");
        teacher1.put("rating", 4.8);
        teacher1.put("features", "本人性格开朗、乐观向上、爱与人沟通，具有较强的组织协调能力和团队合作精神。在教学工作中，本人认真负责，...");
        teacher1.put("grades", "幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三");
        mockTeachers.add(teacher1);

        Map<String, Object> teacher2 = new HashMap<>();
        teacher2.put("id", 2);
        teacher2.put("name", "董老师");
        teacher2.put("avatar", "");
        teacher2.put("university", "长沙理工大学");
        teacher2.put("major", "本科生");
        teacher2.put("grade", "大二");
        teacher2.put("subject", "数学,英语,物理,化学,生物");
        teacher2.put("experience", "经验丰富");
        teacher2.put("rating", 4.6);
        teacher2.put("features", "有耐心，善于掌控全局，学习生活长期担任班干部，有条不紊，上课有条理，书面分明，有参分明。");
        teacher2.put("grades", "一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三");
        mockTeachers.add(teacher2);

        Map<String, Object> teacher3 = new HashMap<>();
        teacher3.put("id", 3);
        teacher3.put("name", "王老师");
        teacher3.put("avatar", "");
        teacher3.put("university", "长沙学院");
        teacher3.put("major", "本科生");
        teacher3.put("grade", "大四");
        teacher3.put("subject", "英语,语文,作文,历史,政治,美术,书法");
        teacher3.put("experience", "经验丰富");
        teacher3.put("rating", 4.9);
        teacher3.put("features", "性格开朗有耐心，擅长引导学生思考，有丰富的家教经验，能够根据学生特点制定个性化教学方案。");
        teacher3.put("grades", "一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三,高一,高二,高三,IB A level,AP");
        mockTeachers.add(teacher3);

        return mockTeachers;
    }

    /**
     * 创建家教订单
     */
    @PostMapping("/orders/create")
    @Log(title = "家长创建订单", businessType = BusinessType.INSERT)
    public AjaxResult createOrder(@RequestBody TutorOrder order) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置订单基本信息
            order.setPublisherId(parentId);
            order.setPublisherType("1"); // 家长发布
            order.setOrderType("1"); // 家教订单
            order.setStatus("1"); // 待审核
            order.setCreateBy(loginUser.getUsername());

            // 生成订单号
            String orderNo = "ORDER_" + System.currentTimeMillis();
            order.setOrderNo(orderNo);

            int result = tutorOrderService.insertTutorOrder(order);
            
            if (result > 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", order.getId());
                response.put("orderNo", orderNo);
                return AjaxResult.success("订单创建成功", response);
            } else {
                return AjaxResult.error("订单创建失败");
            }
        } catch (Exception e) {
            logger.error("创建家教订单失败", e);
            return AjaxResult.error("创建订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取家长个人信息
     */
    @GetMapping("/profile")
    public AjaxResult getProfile() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            ParentInfo parentInfo = parentInfoService.selectParentInfoById(parentId);
            return AjaxResult.success(parentInfo);
        } catch (Exception e) {
            logger.error("获取家长个人信息失败", e);
            return AjaxResult.error("获取个人信息失败");
        }
    }

    /**
     * 更新家长个人信息（仅允许更新姓名和头像）
     */
    @PutMapping("/profile")
    @Log(title = "更新家长信息", businessType = BusinessType.UPDATE)
    public AjaxResult updateProfile(@RequestBody ParentInfo parentInfo) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 只允许更新特定字段：姓名和头像
            ParentInfo updateInfo = new ParentInfo();
            updateInfo.setId(parentId);
            updateInfo.setNickName(parentInfo.getNickName());
            updateInfo.setAvatar(parentInfo.getAvatar());
            updateInfo.setUpdateBy(loginUser.getUsername());

            int result = parentInfoService.updateParentInfo(updateInfo);

            if (result > 0) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新家长个人信息失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取家长发布的所有订单列表
     */
    @GetMapping("/orders/all")
    public AjaxResult getAllOrders(@RequestParam(defaultValue = "1") int pageNum,
                                   @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长发布的所有订单
            List<TutorOrder> orders = tutorOrderService.selectOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长订单列表失败", e);
            return AjaxResult.error("获取订单列表失败");
        }
    }

    /**
     * 获取家长进行中的订单列表
     */
    @GetMapping("/orders/ongoing")
    public AjaxResult getOngoingOrders(@RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长进行中的订单
            List<TutorOrder> orders = tutorOrderService.selectOngoingOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长进行中订单失败", e);
            return AjaxResult.error("获取进行中订单失败");
        }
    }

    /**
     * 获取家长已完成的订单列表
     */
    @GetMapping("/orders/completed")
    public AjaxResult getCompletedOrders(@RequestParam(defaultValue = "1") int pageNum,
                                         @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长已完成的订单
            List<TutorOrder> orders = tutorOrderService.selectCompletedOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长已完成订单失败", e);
            return AjaxResult.error("获取已完成订单失败");
        }
    }



    /**
     * 获取当前登录用户（重写父类方法以添加异常处理）
     */
    @Override
    public LoginUser getLoginUser() {
        try {
            return super.getLoginUser();
        } catch (Exception e) {
            logger.error("获取登录用户失败", e);
            return null;
        }
    }
}

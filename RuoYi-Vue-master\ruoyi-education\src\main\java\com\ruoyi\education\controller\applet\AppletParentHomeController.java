package com.ruoyi.education.controller.applet;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.education.domain.ParentInfo;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.service.IParentInfoService;
import com.ruoyi.education.service.ITutorOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序家长端首页控制器
 */
@RestController
@RequestMapping("/applet/parent")
public class AppletParentHomeController extends BaseController {

    @Autowired
    private IParentInfoService parentInfoService;

    @Autowired
    private ITutorOrderService tutorOrderService;

    /**
     * 获取家长统计数据
     */
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 统计家长发布的订单数据
            Map<String, Object> stats = new HashMap<>();
            
            // 已发布订单数（所有状态）
            int publishedOrders = tutorOrderService.countOrdersByParentId(parentId);
            stats.put("publishedOrders", publishedOrders);
            
            // 进行中订单数（已选择教师，正在进行）
            int ongoingOrders = tutorOrderService.countOngoingOrdersByParentId(parentId);
            stats.put("ongoingOrders", ongoingOrders);
            
            // 已完成订单数
            int completedOrders = tutorOrderService.countCompletedOrdersByParentId(parentId);
            stats.put("completedOrders", completedOrders);

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取家长统计数据失败", e);
            return AjaxResult.error("获取统计数据失败");
        }
    }

    /**
     * 获取家长最近订单
     */
    @GetMapping("/orders/recent")
    public AjaxResult getRecentOrders() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 获取最近5个订单
            List<TutorOrder> recentOrders = tutorOrderService.selectRecentOrdersByParentId(parentId, 5);
            
            return AjaxResult.success(recentOrders);
        } catch (Exception e) {
            logger.error("获取家长最近订单失败", e);
            return AjaxResult.error("获取最近订单失败");
        }
    }

    /**
     * 获取推荐教师
     */
    @GetMapping("/teachers/recommend")
    public AjaxResult getRecommendTeachers() {
        try {
            // 获取推荐教师列表（可以根据家长的历史订单科目等进行推荐）
            // 这里先返回一些优秀的教师
            List<Map<String, Object>> recommendTeachers = tutorOrderService.getRecommendTeachers(10);
            
            return AjaxResult.success(recommendTeachers);
        } catch (Exception e) {
            logger.error("获取推荐教师失败", e);
            return AjaxResult.error("获取推荐教师失败");
        }
    }

    /**
     * 创建家教订单
     */
    @PostMapping("/orders/create")
    @Log(title = "家长创建订单", businessType = BusinessType.INSERT)
    public AjaxResult createOrder(@RequestBody TutorOrder order) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置订单基本信息
            order.setPublisherId(parentId);
            order.setPublisherType("1"); // 家长发布
            order.setOrderType("1"); // 家教订单
            order.setStatus("1"); // 待审核
            order.setCreateBy(loginUser.getUsername());

            // 生成订单号
            String orderNo = "ORDER_" + System.currentTimeMillis();
            order.setOrderNo(orderNo);

            int result = tutorOrderService.insertTutorOrder(order);
            
            if (result > 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", order.getId());
                response.put("orderNo", orderNo);
                return AjaxResult.success("订单创建成功", response);
            } else {
                return AjaxResult.error("订单创建失败");
            }
        } catch (Exception e) {
            logger.error("创建家教订单失败", e);
            return AjaxResult.error("创建订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取家长个人信息
     */
    @GetMapping("/profile")
    public AjaxResult getProfile() {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            ParentInfo parentInfo = parentInfoService.selectParentInfoById(parentId);
            return AjaxResult.success(parentInfo);
        } catch (Exception e) {
            logger.error("获取家长个人信息失败", e);
            return AjaxResult.error("获取个人信息失败");
        }
    }

    /**
     * 更新家长个人信息
     */
    @PutMapping("/profile")
    @Log(title = "更新家长信息", businessType = BusinessType.UPDATE)
    public AjaxResult updateProfile(@RequestBody ParentInfo parentInfo) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();
            
            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            parentInfo.setId(parentId);
            parentInfo.setUpdateBy(loginUser.getUsername());

            int result = parentInfoService.updateParentInfo(parentInfo);
            
            if (result > 0) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新家长个人信息失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取家长发布的所有订单列表
     */
    @GetMapping("/orders/all")
    public AjaxResult getAllOrders(@RequestParam(defaultValue = "1") int pageNum,
                                   @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长发布的所有订单
            List<TutorOrder> orders = tutorOrderService.selectOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长订单列表失败", e);
            return AjaxResult.error("获取订单列表失败");
        }
    }

    /**
     * 获取家长进行中的订单列表
     */
    @GetMapping("/orders/ongoing")
    public AjaxResult getOngoingOrders(@RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长进行中的订单
            List<TutorOrder> orders = tutorOrderService.selectOngoingOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长进行中订单失败", e);
            return AjaxResult.error("获取进行中订单失败");
        }
    }

    /**
     * 获取家长已完成的订单列表
     */
    @GetMapping("/orders/completed")
    public AjaxResult getCompletedOrders(@RequestParam(defaultValue = "1") int pageNum,
                                         @RequestParam(defaultValue = "10") int pageSize) {
        try {
            LoginUser loginUser = getLoginUser();
            Long parentId = loginUser.getParentId();

            if (parentId == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置分页参数
            startPage();

            // 查询家长已完成的订单
            List<TutorOrder> orders = tutorOrderService.selectCompletedOrdersByParentId(parentId);

            return AjaxResult.success(getDataTable(orders));
        } catch (Exception e) {
            logger.error("获取家长已完成订单失败", e);
            return AjaxResult.error("获取已完成订单失败");
        }
    }

    /**
     * 获取当前登录用户
     */
    private LoginUser getLoginUser() {
        try {
            return SecurityUtils.getLoginUser();
        } catch (Exception e) {
            logger.error("获取登录用户失败", e);
            return null;
        }
    }
}

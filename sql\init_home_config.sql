-- 初始化首页配置数据
-- 用于教师端首页的轮播图、公告、城市等配置

-- 插入教师端轮播图配置
INSERT INTO platform_config (config_key, config_value, config_type, config_name, config_desc, create_time, update_time) 
VALUES (
  'teacher.banners',
  '[
    {
      "id": 1,
      "imageUrl": "https://via.placeholder.com/750x320/6c7ae0/ffffff?text=优质教师招募",
      "title": "优质教师招募",
      "linkType": "page",
      "linkUrl": "/pages/teacher/certification/index"
    },
    {
      "id": 2,
      "imageUrl": "https://via.placeholder.com/750x320/ff6b6b/ffffff?text=高薪家教订单",
      "title": "高薪家教订单",
      "linkType": "page",
      "linkUrl": "/pages/teacher/student_source/index"
    },
    {
      "id": 3,
      "imageUrl": "https://via.placeholder.com/750x320/4ecdc4/ffffff?text=平台优势",
      "title": "平台优势",
      "linkType": "web",
      "linkUrl": "https://example.com/advantages"
    }
  ]',
  'json',
  '教师端轮播图',
  '教师端首页轮播图配置，JSON数组格式',
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE 
  config_value = VALUES(config_value),
  update_time = NOW();

-- 插入教师端公告配置
INSERT INTO platform_config (config_key, config_value, config_type, config_name, config_desc, create_time, update_time) 
VALUES (
  'teacher.announcement',
  '🎉 逃单曝光！！！请各位教师注意防范逃单行为
💰 高价回收家教订单，平台现高价回收优质家教订单
💡 【高级心理咨询师】心理咨询，情感疗愈服务
📢 代理招募公告，诚招各地区代理，丰厚佣金等你来拿
🎯 专业指导，筑梦留学~专业留学指导服务',
  'string',
  '教师端公告',
  '教师端首页显示的公告内容，每行一条公告',
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE 
  config_value = VALUES(config_value),
  update_time = NOW();

-- 插入城市配置
INSERT INTO platform_config (config_key, config_value, config_type, config_name, config_desc, create_time, update_time) 
VALUES (
  'cities',
  '[
    { "name": "上海", "value": "shanghai" },
    { "name": "北京", "value": "beijing" },
    { "name": "广州", "value": "guangzhou" },
    { "name": "深圳", "value": "shenzhen" },
    { "name": "杭州", "value": "hangzhou" },
    { "name": "南京", "value": "nanjing" },
    { "name": "苏州", "value": "suzhou" },
    { "name": "成都", "value": "chengdu" },
    { "name": "武汉", "value": "wuhan" },
    { "name": "西安", "value": "xian" },
    { "name": "重庆", "value": "chongqing" },
    { "name": "天津", "value": "tianjin" }
  ]',
  'json',
  '城市列表',
  '平台支持的城市列表配置',
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE 
  config_value = VALUES(config_value),
  update_time = NOW();

-- 刷新配置缓存（如果有缓存机制的话）
-- 注意：这个操作可能需要重启应用或调用刷新缓存的接口

-- 教师会员表创建脚本（跳过重复字段版）
-- 如果字段已存在会自动跳过，不会报错

-- ==========================================
-- 第一步：创建教师会员表
-- ==========================================
DROP TABLE IF EXISTS `teacher_membership`;
CREATE TABLE `teacher_membership` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `teacher_id` bigint(20) NOT NULL COMMENT '教师ID',
  `membership_type` varchar(10) NOT NULL DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）',
  `membership_date` datetime NOT NULL COMMENT '成为会员日期',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `payment_status` varchar(10) NOT NULL DEFAULT '1' COMMENT '支付状态（0未支付 1已支付 2已退款）',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `status` varchar(10) DEFAULT '1' COMMENT '状态（0无效 1有效）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_membership_type` (`membership_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_membership_date` (`membership_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师会员表';

SELECT 'teacher_membership表创建成功！' as step1_result;

-- ==========================================
-- 第二步：检查字段是否存在，不存在才添加
-- ==========================================

-- 检查membership_type字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'membership_type字段已存在，跳过添加'
        ELSE 'membership_type字段不存在，需要添加'
    END as membership_type_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME = 'membership_type';

-- 检查membership_date字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'membership_date字段已存在，跳过添加'
        ELSE 'membership_date字段不存在，需要添加'
    END as membership_date_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME = 'membership_date';

-- ==========================================
-- 第三步：只有字段不存在时才执行添加
-- ==========================================

-- 如果看到上面显示"字段不存在，需要添加"，请手动执行以下语句：
-- ALTER TABLE teacher_info ADD COLUMN membership_type varchar(10) DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）';
-- ALTER TABLE teacher_info ADD COLUMN membership_date datetime DEFAULT NULL COMMENT '成为会员日期';

-- 如果字段已存在，则跳过上面的ALTER语句

-- ==========================================
-- 第四步：检查并创建索引
-- ==========================================

-- 检查索引是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'idx_membership_type索引已存在'
        ELSE 'idx_membership_type索引不存在，需要创建'
    END as membership_type_index_check
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND INDEX_NAME = 'idx_membership_type';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'idx_membership_date索引已存在'
        ELSE 'idx_membership_date索引不存在，需要创建'
    END as membership_date_index_check
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND INDEX_NAME = 'idx_membership_date';

-- 如果看到上面显示"索引不存在，需要创建"，请手动执行以下语句：
-- ALTER TABLE teacher_info ADD INDEX idx_membership_type (membership_type);
-- ALTER TABLE teacher_info ADD INDEX idx_membership_date (membership_date);

-- ==========================================
-- 第五步：初始化数据
-- ==========================================

-- 将所有现有教师设置为普通会员（如果字段存在的话）
UPDATE `teacher_info` 
SET `membership_type` = '1' 
WHERE `membership_type` IS NULL OR `membership_type` = '';

SELECT '数据初始化完成！所有教师已设置为普通会员。' as step5_result;

-- ==========================================
-- 第六步：验证结果
-- ==========================================

-- 检查teacher_info表的会员相关字段
SELECT 'teacher_info表会员字段检查:' as info;
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME IN ('membership_type', 'membership_date')
ORDER BY COLUMN_NAME;

-- 检查会员类型分布
SELECT '教师会员类型分布:' as info;
SELECT 
    membership_type,
    CASE 
        WHEN membership_type = '1' THEN '普通会员'
        WHEN membership_type = '2' THEN '高级会员'
        ELSE '未知类型'
    END as membership_name,
    COUNT(*) as teacher_count
FROM `teacher_info` 
WHERE membership_type IS NOT NULL
GROUP BY membership_type;

-- ==========================================
-- 完成提示
-- ==========================================
SELECT '🎉 教师会员表处理完成！' as final_result;
SELECT '📝 注意：如果上面显示某些字段或索引"不存在，需要添加"，请手动执行对应的ALTER语句。' as notice;

# 内容管理功能使用指南

## 功能概述

内容管理是众桥辅导平台管理后台的核心功能，用于管理教师端首页展示的轮播图、公告和筛选条件等内容。

## 主要功能

### 1. 轮播图管理

#### 功能特性
- **图片上传**: 支持直接上传图片文件（jpg、png格式，最大2MB）
- **URL输入**: 也可以直接输入图片URL地址
- **链接配置**: 支持三种链接类型
  - 无链接：纯展示，不跳转
  - 页面跳转：小程序内页面路径
  - 外部链接：外部网页URL
- **排序管理**: 支持拖拽排序和上下移动
- **实时预览**: 支持图片预览功能

#### 使用步骤
1. 登录管理后台
2. 进入"教育管理" → "内容管理"
3. 在轮播图管理区域点击"添加轮播图"
4. 填写轮播图信息：
   - 标题：轮播图的标题（必填）
   - 图片：上传图片或输入URL（必填）
   - 链接类型：选择跳转类型
   - 链接地址：根据类型填写相应链接
   - 排序权重：数字越小越靠前
5. 点击"保存"完成添加

#### 图片规范
- **尺寸建议**: 750x320 像素
- **格式要求**: jpg、png
- **大小限制**: 不超过2MB
- **内容要求**: 清晰美观，符合平台风格

### 2. 筛选条件管理

#### 支持的筛选类型
- **时间段选项**: 配置可选的上课时间段
- **授课方式选项**: 一对一、一对多等授课方式
- **年级选项**: 小学、初中、高中各年级
- **城市选项**: 平台支持的城市列表
- **科目选项**: 数学、语文、英语等科目

#### 管理功能
- **添加选项**: 新增筛选选项
- **编辑选项**: 修改选项名称和值
- **删除选项**: 移除不需要的选项
- **排序调整**: 上移下移调整显示顺序
- **批量操作**: 支持批量保存

#### 配置格式
每个筛选选项包含：
- **显示名称**: 用户看到的文字
- **选项值**: 后端处理的值
- **排序权重**: 显示顺序

## 数据同步机制

### 数据流程
```
管理后台配置 → platform_config表 → 后端API → 教师端首页
```

### 配置键说明
- `teacher.banners`: 教师端轮播图配置（JSON格式）
- `teacher.announcement`: 教师端公告配置（文本格式，每行一条）
- `cities`: 城市列表配置（JSON格式）
- `filter.grades`: 年级筛选选项（JSON格式）
- `filter.subjects`: 科目筛选选项（JSON格式）
- `filter.cities`: 城市筛选选项（JSON格式）

### 实时生效
- 管理后台保存后立即生效
- 教师端首页会自动获取最新配置
- 无需重启服务或清除缓存

## API接口说明

### 轮播图接口
```
GET /applet/config/banners
Response: {
  "code": 200,
  "data": [
    {
      "id": 1,
      "title": "轮播图标题",
      "image": "图片URL",
      "linkType": "page|web|none",
      "linkUrl": "链接地址",
      "sort": 1
    }
  ]
}
```

### 公告接口
```
GET /applet/config/announcements
Response: {
  "code": 200,
  "data": [
    {
      "id": 1,
      "title": "公告标题",
      "content": "公告内容"
    }
  ]
}
```

### 城市接口
```
GET /applet/config/cities
Response: {
  "code": 200,
  "data": [
    {
      "name": "上海",
      "value": "shanghai"
    }
  ]
}
```

## 最佳实践

### 轮播图设计建议
1. **内容相关**: 轮播图内容应与平台业务相关
2. **视觉统一**: 保持统一的设计风格和色调
3. **文字清晰**: 避免文字过小或过于复杂
4. **数量适中**: 建议3-5张轮播图，避免过多
5. **定期更新**: 根据运营需要定期更新内容

### 公告管理建议
1. **内容简洁**: 公告内容要简洁明了
2. **重要优先**: 重要公告放在前面
3. **定期清理**: 及时删除过期公告
4. **格式统一**: 保持公告格式的一致性

### 筛选条件建议
1. **用户友好**: 选项名称要通俗易懂
2. **逻辑清晰**: 选项分类要合理
3. **数量适中**: 避免选项过多造成选择困难
4. **及时维护**: 根据业务变化及时调整

## 常见问题

### Q1: 图片上传失败
- 检查图片格式是否为jpg或png
- 确认图片大小不超过2MB
- 检查网络连接是否正常
- 查看后端上传接口是否正常

### Q2: 配置不生效
- 确认已点击保存按钮
- 检查后端服务是否正常
- 查看数据库中配置是否正确保存
- 尝试刷新教师端首页

### Q3: 图片显示异常
- 检查图片URL是否可访问
- 确认图片服务器是否正常
- 验证图片格式是否正确
- 查看浏览器控制台错误信息

## 技术支持

如遇到问题，请提供：
1. 具体的操作步骤
2. 错误信息截图
3. 浏览器控制台日志
4. 后端服务日志

联系开发团队获取技术支持。

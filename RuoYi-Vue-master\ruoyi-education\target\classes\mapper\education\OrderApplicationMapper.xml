<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.OrderApplicationMapper">

    <resultMap id="OrderApplicationResult" type="com.ruoyi.education.domain.OrderApplication">
        <id property="id" column="id" />
        <result property="orderId" column="order_id" />
        <result property="teacherId" column="teacher_id" />
        <result property="applyTime" column="apply_time" />
        <result property="status" column="status" />
        <result property="expectedSalary" column="expected_salary" />
        <result property="timeNegotiation" column="time_negotiation" />
        <result property="advantage" column="advantage" />
        <result property="isRecommended" column="is_recommended" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="deleted" column="deleted" />
        <!-- 联查teacher_info表字段 -->
        <result property="teacherName" column="teacher_name" />
        <result property="teacherPhone" column="teacher_phone" />
    </resultMap>

    <select id="selectOrderApplicationById" parameterType="long" resultMap="OrderApplicationResult">
        select * from order_application where id = #{id} and deleted = 0
    </select>

    <select id="selectOrderApplicationList" parameterType="com.ruoyi.education.domain.OrderApplication" resultMap="OrderApplicationResult">
        select 
          a.id, a.order_id, a.teacher_id, a.apply_time, a.status, a.expected_salary, a.time_negotiation, a.advantage, a.is_recommended, 
          a.create_by, a.create_time, a.update_by, a.update_time, a.deleted,
          t.real_name as teacher_name, t.phone_number as teacher_phone
        from order_application a
        left join teacher_info t on a.teacher_id = t.id
        <where>
            <if test="orderId != null">and a.order_id = #{orderId}</if>
            <if test="teacherId != null">and a.teacher_id = #{teacherId}</if>
            <if test="status != null">and a.status = #{status}</if>
            <if test="isRecommended != null">and a.is_recommended = #{isRecommended}</if>
            <if test="deleted != null">and a.deleted = #{deleted}</if>
        </where>
        order by a.apply_time desc
    </select>

    <insert id="insertOrderApplication" parameterType="com.ruoyi.education.domain.OrderApplication" useGeneratedKeys="true" keyProperty="id">
        insert into order_application (
            order_id, teacher_id, apply_time, status, expected_salary, time_negotiation, advantage, is_recommended, create_by, create_time, update_by, update_time, deleted
        ) values (
            #{orderId}, #{teacherId}, #{applyTime}, #{status}, #{expectedSalary}, #{timeNegotiation}, #{advantage}, #{isRecommended}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{deleted}
        )
    </insert>

    <update id="updateOrderApplication" parameterType="com.ruoyi.education.domain.OrderApplication">
        update order_application
        <set>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="expectedSalary != null">expected_salary = #{expectedSalary},</if>
            <if test="timeNegotiation != null">time_negotiation = #{timeNegotiation},</if>
            <if test="advantage != null">advantage = #{advantage},</if>
            <if test="isRecommended != null">is_recommended = #{isRecommended},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteOrderApplicationById" parameterType="long">
        update order_application set deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteOrderApplicationByIds" parameterType="long">
        update order_application set deleted = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectApplicationsByOrderId" parameterType="long" resultMap="OrderApplicationResult">
        select 
          a.id, a.order_id, a.teacher_id, a.apply_time, a.status, a.expected_salary, a.time_negotiation, a.advantage, a.is_recommended, 
          a.create_by, a.create_time, a.update_by, a.update_time, a.deleted,
          t.real_name as teacher_name, t.phone_number as teacher_phone
        from order_application a
        left join teacher_info t on a.teacher_id = t.id
        where a.order_id = #{orderId} and a.deleted = 0
        order by a.apply_time desc
    </select>

    <select id="selectApplicationsByTeacherId" parameterType="long" resultMap="OrderApplicationResult">
        select * from order_application where teacher_id = #{teacherId} and deleted = 0 order by apply_time desc
    </select>

    <select id="countApplicationsByOrderId" parameterType="long" resultType="int">
        select count(*) from order_application where order_id = #{orderId} and deleted = 0
    </select>

    <select id="selectByOrderIdAndTeacherId" resultMap="OrderApplicationResult">
        select * from order_application
        where order_id = #{orderId} and teacher_id = #{teacherId} and deleted = 0
        limit 1
    </select>

    <update id="cancelApplication">
        update order_application
        set status = '6', update_time = now()
        where order_id = #{orderId} and teacher_id = #{teacherId} and deleted = 0
    </update>

    <update id="updateApplicationByOrderIdAndTeacherId" parameterType="com.ruoyi.education.domain.OrderApplication">
        update order_application
        <set>
            <if test="expectedSalary != null">expected_salary = #{expectedSalary},</if>
            <if test="timeNegotiation != null">time_negotiation = #{timeNegotiation},</if>
            <if test="advantage != null">advantage = #{advantage},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where order_id = #{orderId} and teacher_id = #{teacherId} and deleted = 0
    </update>

    <update id="auditApplication" parameterType="com.ruoyi.education.domain.OrderApplication">
        update order_application
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 选择教师相关方法 -->
    <update id="selectTeacherForOrder">
        update order_application
        set status = '4', select_time = now(), update_time = now()
        where order_id = #{orderId} and teacher_id = #{teacherId} and deleted = 0
    </update>

    <update id="eliminateOtherTeachers">
        update order_application
        set status = '5', update_time = now()
        where order_id = #{orderId} and teacher_id != #{teacherId} and deleted = 0
          and status in ('1', '2')  -- 只淘汰待审核和审核通过的报名
    </update>

    <!-- 其他查询方法 -->
    <select id="selectPendingAuditApplications" resultMap="OrderApplicationResult">
        select
          a.id, a.order_id, a.teacher_id, a.apply_time, a.status, a.expected_salary, a.time_negotiation, a.advantage, a.is_recommended,
          a.create_by, a.create_time, a.update_by, a.update_time, a.deleted,
          t.real_name as teacher_name, t.phone_number as teacher_phone
        from order_application a
        left join teacher_info t on a.teacher_id = t.id
        where a.status = '1' and a.deleted = 0
        order by a.apply_time desc
    </select>

    <select id="selectRecommendedTeachers" parameterType="long" resultMap="OrderApplicationResult">
        select
          a.id, a.order_id, a.teacher_id, a.apply_time, a.status, a.expected_salary, a.time_negotiation, a.advantage, a.is_recommended,
          a.create_by, a.create_time, a.update_by, a.update_time, a.deleted,
          t.real_name as teacher_name, t.phone_number as teacher_phone
        from order_application a
        left join teacher_info t on a.teacher_id = t.id
        where a.order_id = #{orderId} and a.is_recommended = '1' and a.deleted = 0
        order by a.apply_time desc
    </select>

    <update id="setRecommended">
        update order_application
        set is_recommended = #{isRecommended}, update_time = now()
        where id = #{id} and deleted = 0
    </update>

    <update id="publishContact" parameterType="com.ruoyi.education.domain.OrderApplication">
        update order_application
        <set>
            <if test="contactPublished != null">contact_published = #{contactPublished},</if>
            <if test="contactPublishTime != null">contact_publish_time = #{contactPublishTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where id = #{id}
    </update>

</mapper>
# 支付回调问题排查指南

## 问题现象
支付成功后，`feePaidStatus` 仍然等于 `0`，说明微信支付回调没有正确更新教师的缴费状态。

## 可能原因分析

### 1. 回调URL不可访问
- **原因**：微信服务器无法访问回调URL
- **检查**：确认natapp隧道是否正常工作
- **验证**：在浏览器中访问 `http://t8f63f47.natappfree.cc/applet/teacher/certification/notify`

### 2. 回调没有被调用
- **原因**：微信支付配置问题或网络问题
- **检查**：查看后端日志是否有回调日志输出
- **验证**：搜索日志中的 "=== 收到微信支付回调 ==="

### 3. openid不匹配
- **原因**：支付时的openid与数据库中的openid不一致
- **检查**：对比支付订单中的openid和数据库中的openid
- **验证**：查看回调日志中的openid查找结果

### 4. 数据库更新失败
- **原因**：SQL更新语句执行失败
- **检查**：查看数据库更新结果和异常日志
- **验证**：手动执行SQL更新语句

## 排查步骤

### 步骤1：检查natapp隧道状态
```bash
# 确认natapp隧道正常运行
# 访问 http://t8f63f47.natappfree.cc 确认能访问到应用
```

### 步骤2：检查回调URL可访问性
```bash
# 使用curl测试回调URL（GET请求测试连通性）
curl -X GET http://t8f63f47.natappfree.cc/applet/teacher/certification/notify
```

### 步骤3：查看支付订单创建日志
```
# 查找支付订单创建的日志
=== 创建认证费支付订单开始 ===
用户ID: 30
教师openid: ox1234567890abcdef
设置的回调URL: http://t8f63f47.natappfree.cc/applet/teacher/certification/notify
```

### 步骤4：检查是否收到回调
```
# 查找回调处理的日志
=== 收到微信支付回调 ===
回调数据: <xml>...</xml>
订单号: CERT_FEE_1640000000
用户openid: ox1234567890abcdef
```

### 步骤5：检查数据库中的openid
```sql
-- 检查教师表中的openid
SELECT 
    id,
    real_name,
    openid,
    certification_fee_paid,
    update_time
FROM teacher_info 
WHERE id = 30;  -- 替换为实际的教师ID
```

### 步骤6：手动测试状态更新
```
# 调用手动更新接口测试数据库更新是否正常
POST /applet/teacher/certification/update-fee-status
Headers: Authorization: Bearer {token}
```

## 修复方案

### 方案1：如果回调URL不可访问
```java
// 确保natapp隧道正常，或使用其他可访问的域名
String notifyUrl = "http://your-accessible-domain.com/applet/teacher/certification/notify";
```

### 方案2：如果openid不匹配
```sql
-- 查找正确的openid并更新
UPDATE teacher_info 
SET openid = 'correct_openid_from_wechat' 
WHERE id = 30;
```

### 方案3：如果回调逻辑有问题
```java
// 检查selectTeacherInfoByOpenid方法是否正确实现
TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoByOpenid(openid);
```

### 方案4：手动更新缴费状态（临时方案）
```sql
-- 直接更新数据库
UPDATE teacher_info 
SET certification_fee_paid = '1', 
    update_time = NOW() 
WHERE id = 30;
```

## 测试接口

### 1. 手动更新缴费状态
```
POST /applet/teacher/certification/update-fee-status
Headers: Authorization: Bearer {token}
```

### 2. 查看缴费状态
```
GET /applet/teacher/certification/fee-info
Headers: Authorization: Bearer {token}
```

### 3. 模拟回调测试
```
POST /applet/teacher/certification/notify
Content-Type: application/xml
Body: <xml><return_code><![CDATA[SUCCESS]]></return_code>...</xml>
```

## 预期日志输出

### 正常的回调处理日志
```
=== 收到微信支付回调 ===
回调数据: <xml>...</xml>
解析后的回调结果: WxPayOrderNotifyResult{...}
订单号: CERT_FEE_1640000000
用户openid: ox1234567890abcdef
查找到的教师信息: 存在，ID=30
更新前的缴费状态: 0
数据库更新结果: 1
更新后的缴费状态: 1
教师认证费缴费成功，教师ID: 30, 订单号: CERT_FEE_1640000000
=== 微信支付回调处理完成 ===
```

### 异常情况的日志
```
=== 收到微信支付回调 ===
用户openid: ox1234567890abcdef
查找到的教师信息: 不存在
根据openid未找到教师信息: ox1234567890abcdef
```

## 立即测试方案

### 1. 先手动更新状态测试
```
POST /applet/teacher/certification/update-fee-status
```

### 2. 检查状态是否更新成功
```
GET /applet/teacher/certification/fee-info
```

### 3. 如果手动更新成功，说明数据库操作正常，问题在回调
### 4. 如果手动更新失败，说明数据库操作有问题

## 紧急修复

如果需要立即修复，可以直接执行SQL：
```sql
UPDATE teacher_info 
SET certification_fee_paid = '1', 
    update_time = NOW() 
WHERE id = (
    SELECT user_id FROM sys_user WHERE user_name = 'teacher_username'
);
```

现在可以按照这个指南逐步排查问题了！

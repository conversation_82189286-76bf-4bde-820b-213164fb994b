// pages/parent/profile/edit/index.js
const parentApi = require('../../../../api/parent.js');
const { uploadFile } = require('../../../../api/common.js');

Page({
  data: {
    userInfo: {},
    originalUserInfo: {},
    saving: false
  },

  onLoad() {
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const res = await parentApi.getParentProfile();
      if (res.code === 200) {
        this.setData({
          userInfo: res.data,
          originalUserInfo: JSON.parse(JSON.stringify(res.data))
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 姓名输入
  onNameInput(e) {
    this.setData({
      'userInfo.nickName': e.detail.value
    });
  },

  // 选择头像（参考教师端头像上传逻辑）
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.uploadAvatar(tempFilePath);
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 上传头像（使用项目通用上传接口）
  async uploadAvatar(filePath) {
    try {
      console.log('开始上传头像，文件路径:', filePath);

      // 使用项目的通用上传接口
      const res = await uploadFile({
        filePath: filePath,
        name: 'file'
      });

      console.log('上传接口返回结果:', res);

      if (res.code === 200 && res.url) {
        this.setData({
          'userInfo.avatar': res.url
        });
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.msg || '上传失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('上传头像失败:', error);
      wx.showToast({
        title: '上传失败，请重试',
        icon: 'none'
      });
    }
  },

  // 保存资料
  async saveProfile() {
    const { userInfo, originalUserInfo } = this.data;
    
    // 检查是否有修改
    if (userInfo.nickName === originalUserInfo.nickName && 
        userInfo.avatar === originalUserInfo.avatar) {
      wx.showToast({
        title: '没有修改',
        icon: 'none'
      });
      return;
    }

    // 验证姓名
    if (!userInfo.nickName || !userInfo.nickName.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    try {
      const updateData = {
        nickName: userInfo.nickName.trim(),
        avatar: userInfo.avatar
      };

      const res = await parentApi.updateParentProfile(updateData);
      
      if (res.code === 200) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 更新本地存储的用户信息
        const localUserInfo = wx.getStorageSync('userInfo') || {};
        localUserInfo.nickName = updateData.nickName;
        localUserInfo.avatar = updateData.avatar;
        wx.setStorageSync('userInfo', localUserInfo);
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  }
});

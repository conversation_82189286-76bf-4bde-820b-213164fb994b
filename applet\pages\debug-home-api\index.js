// 首页API调试页面
const { getBannerList, getAnnouncementList, getCityList } = require('../../api/config.js');

Page({
  data: {
    testResults: [],
    isLoading: false
  },

  onLoad() {
    this.runTests();
  },

  async runTests() {
    this.setData({ isLoading: true, testResults: [] });
    
    const tests = [
      { name: '轮播图接口', func: getBannerList },
      { name: '公告接口', func: getAnnouncementList },
      { name: '城市接口', func: getCityList }
    ];

    for (let test of tests) {
      try {
        console.log(`测试 ${test.name}...`);
        const result = await test.func();
        
        this.addTestResult({
          name: test.name,
          status: 'success',
          code: result.code,
          data: result.data,
          message: '请求成功'
        });
        
        console.log(`${test.name} 成功:`, result);
      } catch (error) {
        this.addTestResult({
          name: test.name,
          status: 'error',
          code: error.code || 'ERROR',
          data: null,
          message: error.message || '请求失败'
        });
        
        console.error(`${test.name} 失败:`, error);
      }
    }
    
    this.setData({ isLoading: false });
  },

  addTestResult(result) {
    const results = this.data.testResults;
    results.push(result);
    this.setData({ testResults: results });
  },

  retryTest() {
    this.runTests();
  },

  copyResult(e) {
    const { index } = e.currentTarget.dataset;
    const result = this.data.testResults[index];
    
    wx.setClipboardData({
      data: JSON.stringify(result, null, 2),
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  }
});

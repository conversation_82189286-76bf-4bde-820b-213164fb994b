/* pages/parent/profile/index.wxss */

.page-container {
  background-color: #f6f7f8;
  min-height: 100vh;
}

/* 个人信息区域 */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 60rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.phone {
  font-size: 28rpx;
  opacity: 0.8;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 统计数据 */
.stats-section {
  background: white;
  margin: -40rpx 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 0;
  display: flex;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60rpx;
  background: #e5e5e5;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}

/* 功能区域 */
.section {
  margin: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.menu-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  font-size: 36rpx;
  color: white;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  margin: 30rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: white;
  border: 2rpx solid #ff4757;
  border-radius: 44rpx;
  color: #ff4757;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:active {
  background: #ff4757;
  color: white;
}

/* 图标字体 */
.iconfont {
  font-family: 'iconfont';
}

.icon-edit::before { content: '\e601'; }
.icon-order::before { content: '\e602'; }
.icon-ongoing::before { content: '\e603'; }
.icon-completed::before { content: '\e604'; }
.icon-payment::before { content: '\e605'; }
.icon-star::before { content: '\e606'; }
.icon-service::before { content: '\e607'; }
.icon-setting::before { content: '\e608'; }
.icon-arrow-right::before { content: '\e609'; }
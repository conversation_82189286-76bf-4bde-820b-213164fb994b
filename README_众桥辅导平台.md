# 众桥辅导平台 - 开发文档

## 项目概述

众桥辅导是一个三端分离的教育服务平台，包含教师端小程序、家长端小程序和管理员后台系统。

### 项目架构
```
众桥辅导/
├── RuoYi-Vue-master/     # 管理员后台系统（基于若依框架）
├── applet/               # 微信小程序前端（教师端+家长端）
├── RuoYi-Vue-master/     # 小程序后端服务
└── sql/                  # 数据库脚本
```

## 技术栈

### 管理员后台（RuoYi-Vue-master）
- **后端**: Spring Boot 2.5.15, Spring Security, MyBatis, Redis, JWT
- **前端**: Vue 2.x, Element UI, Axios
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

### 小程序前端（applet）
- **框架**: 微信小程序原生框架
- **渲染引擎**: Skyline
- **组件框架**: glass-easel
- **UI库**: WeUI / 自定义组件库

### 小程序后端（applet-backend）
- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security, JWT
- **数据层**: MyBatis-Plus, MySQL
- **缓存**: Redis
- **消息队列**: RabbitMQ（可选）
- **文件存储**: 阿里云OSS/腾讯云COS

## 开发进度

### ✅ 已完成功能

#### 1. 教师认证系统
- [x] 教师注册和登录
- [x] 教师信息完善（基本信息、教育经历、工作经验、可教科目）
- [x] 教师认证流程（证件上传、审核、缴费）
- [x] 认证费用管理
- [x] 教师工作台

#### 2. 订单系统（第一版）
- [x] 订单发布功能
- [x] 订单列表展示（管理后台）
- [x] 订单状态管理
- [x] 订单统计功能
- [x] 小程序端生源页面（教师查看订单列表）
- [x] **订单详情页面（小程序端）**
- [x] **教师报名功能**
- [x] **订单详情API接口**

#### 3. 用户管理系统
- [x] 教师信息管理
- [x] 家长信息管理
- [x] 用户状态管理（启用/禁用）

#### 4. 基础数据管理
- [x] 科目信息管理
- [x] 地区配置管理
- [x] 平台配置管理
- [x] 数据字典管理

#### 5. 报名管理系统
- [x] 订单报名功能
- [x] 报名审核流程
- [x] 报名限制配置
- [x] 报名状态管理

### 🔄 进行中功能

#### 1. 订单系统（第二版）
- [ ] 订单支付流程
- [ ] 试课管理
- [ ] 课程进度跟踪

#### 2. 收入结算系统
- [ ] 教师收入统计
- [ ] 提现申请管理
- [ ] 平台分成计算

### 📋 待开发功能

#### 1. 家长端小程序
- [x] 家长注册和认证
- [x] 家长端首页（推荐教师列表）
- [x] 推荐教师卡片展示（按照UI设计样式）
- [x] 底部导航栏
- [x] 查看教师详情功能
- [ ] 发布家教需求
- [ ] 查看报名教师
- [ ] 选择合适教师

#### 2. 消息通知系统
- [ ] 订单状态变更通知
- [ ] 报名审核结果通知
- [ ] 系统公告推送

#### 3. 评价系统
- [ ] 教师评价管理
- [ ] 服务质量评估
- [ ] 信用体系建设

## 最新开发内容

### 2025年1月27日更新 - 教师认证费用功能完整实现与报名校验优化
1. **功能实现**
   - 修复认证费用显示0.00的问题，正确从 `platform_config` 表获取费用
   - 重构deposit页面为认证费用页面，采用现代化设计风格
   - 实现认证费用缴纳流程和状态管理
   - 添加报名时的认证费用校验机制

2. **技术修复**
   - 解决Bean名称冲突错误，使用现有的 `AppletCertificationFeeController`
   - 修正配置获取方式，使用 `platformConfigService.selectConfigValueByKey()` 方法
   - 在报名逻辑中添加认证费用缴纳状态检查
   - 完善前端错误处理，支持特殊错误码识别和页面跳转

3. **报名校验机制**
   - 在 `OrderApplicationServiceImpl.applyForOrder()` 中添加认证费用检查
   - 未缴纳认证费用时返回特殊错误码 `CERTIFICATION_FEE_NOT_PAID`
   - 前端识别错误码并引导用户跳转到缴费页面
   - 支持从生源页面和订单详情页面的报名校验

4. **页面优化**
   - 现代化的加载动画和状态提示
   - 清晰的费用展示和缴费状态
   - 完善的错误处理和用户提示
   - 缴费后的后续操作引导

5. **配置管理**
   - 平台配置项：`teacher.certification.fee` 控制认证费用金额
   - 配置存储在 `platform_config` 表中
   - 提供正确的SQL脚本确保配置设置

6. **微信支付修复**
   - 修复notifyUrl必填字段错误，设置完整的回调URL
   - 修复openid获取方式，从TeacherInfo表中获取而非SysUser
   - 完善支付订单创建的错误处理和日志记录
   - 确保JSAPI支付所有必填参数正确传递

7. **报名校验优化**
   - 实现主动认证费用检查，在用户点击报名时立即验证
   - 优化用户体验，减少等待时间和网络请求
   - 修复前端错误处理逻辑，正确识别特殊错误码
   - 完善订单详情页面和生源页面的认证费用检查逻辑

8. **调试功能**
   - 在所有关键方法中添加详细的调试日志
   - 新增测试接口 `/applet/teacher/certification/test-config` 用于配置验证
   - 提供完整的SQL脚本进行数据库配置检查
   - 支持实时日志监控和问题排查

7. **相关文件**
   - `applet/pages/teacher/deposit/` - 重构的认证费用页面
   - `applet/api/deposit.js` - 更新的API接口
   - `applet/pages/teacher/order/detail/index.js` - 添加认证费用校验的报名逻辑
   - `applet/pages/teacher/student_source/index.js` - 添加认证费用检查流程
   - `RuoYi-Vue-master/ruoyi-education/src/main/java/com/ruoyi/education/controller/applet/AppletCertificationFeeController.java` - 修正的后端接口
   - `RuoYi-Vue-master/ruoyi-education/src/main/java/com/ruoyi/education/service/impl/OrderApplicationServiceImpl.java` - 添加认证费用校验
   - `RuoYi-Vue-master/ruoyi-education/src/main/java/com/ruoyi/education/service/impl/PlatformConfigServiceImpl.java` - 添加配置获取调试
   - `检查认证费用配置.sql` - 数据库配置检查脚本
   - `applet/utils/request.js` - 优化错误处理，支持特殊错误码识别
   - `修复微信支付openid问题.md` - 微信支付问题修复指南
   - `微信支付回调URL修复.md` - 支付回调修复指南
   - `支付回调问题排查指南.md` - 支付回调问题排查指南
   - `测试报名认证费用校验.md` - 报名校验功能测试指南
   - `主动认证费用检查功能测试.md` - 主动检查功能测试指南
   - `测试认证费用功能.md` - 完整的测试指南

### 2025年1月27日更新 - 小程序真机上传功能修复
1. **问题修复**
   - 修复小程序真机上传功能失败的问题
   - 统一网络配置，解决localhost无法访问的问题
   - 增强错误处理和调试信息

2. **技术改进**
   - 创建统一配置文件 `applet/config/index.js`
   - 新增网络检测工具 `applet/utils/network.js`
   - 增强上传接口的错误处理和日志输出
   - 添加服务器连接测试接口 `/common/ping`

3. **配置优化**
   - 统一使用局域网IP地址：`http://*************:8080`
   - 添加文件大小检查和网络状态检测
   - 提供详细的调试信息和错误提示

4. **相关文件**
   - `applet/config/index.js` - 统一配置文件
   - `applet/utils/network.js` - 网络检测工具
   - `applet/api/common.js` - 增强的上传接口
   - `applet/utils/request.js` - 更新的网络请求工具
   - `小程序真机上传配置指南.md` - 详细配置说明

### 2025年1月27日更新 - 管理员代发布订单生源接口修复
1. **问题修复**
   - 修复管理员代发布订单后，教师端小程序生源接口返回空的问题
   - 问题原因：MyBatis查询语句中缺少 `selected_teacher_id` 和 `select_time` 字段
   - 修复内容：更新 `TutorOrderMapper.xml` 中的查询语句和ResultMap映射

2. **技术细节**
   - 更新 `selectTutorOrderVo` 查询语句，添加缺失字段
   - 更新 `TutorOrderResult` ResultMap，添加字段映射
   - 统一所有订单查询使用相同的字段列表
   - 确保管理员发布的订单（status=2, selected_teacher_id=null）能正确显示在生源列表中

3. **相关文件**
   - `RuoYi-Vue-master/ruoyi-education/src/main/resources/mapper/education/TutorOrderMapper.xml`
   - `RuoYi-Vue-master/ruoyi-education/target/classes/mapper/education/TutorOrderMapper.xml`

### 2025年1月13日更新 - 订单详情功能
1. **小程序端订单详情页面**
   - 完善UI设计，采用绿色渐变背景
   - 卡片式布局展示订单信息
   - 包含辅导信息、教师要求、温馨提示等模块
   - 响应式设计，适配各种屏幕尺寸

2. **教师报名功能**
   - 实现教师一键报名订单
   - 支持自定义薪资设置
   - 添加确认弹窗和加载状态
   - 表单验证和错误处理

3. **后端API接口**
   - 新增小程序端订单详情接口 `/applet/teacher/order/detail/{orderId}`
   - 新增教师报名接口 `/applet/teacher/order/apply`
   - 集成报名限制检查逻辑
   - 完善异常处理和响应格式

4. **数据库优化**
   - 添加测试订单数据
   - 优化订单字段映射
   - 完善报名记录表结构

### 功能特点
- **美观的UI设计**: 采用现代化的卡片式设计，绿色主题色调
- **用户体验优化**: 流畅的交互动画，清晰的信息层次
- **数据安全**: 完善的表单验证和后端校验
- **扩展性强**: 模块化设计，易于后续功能扩展

## 快速开始

### 环境准备
1. **开发环境**
   - JDK 1.8+
   - Node.js 14+
   - MySQL 8.0+
   - Redis 6.0+
   - 微信开发者工具

2. **数据库配置**
   - 导入 `sql/` 目录下的数据库脚本
   - 按照 `sql/导入指南.md` 的顺序执行

3. **后端启动**
   ```bash
   cd RuoYi-Vue-master/ruoyi-admin
   mvn spring-boot:run
   ```

4. **前端启动**
   ```bash
   cd RuoYi-Vue-master/ruoyi-ui
   npm install
   npm run dev
   ```

5. **小程序调试**
   - 用微信开发者工具打开 `applet/` 目录
   - 配置小程序AppID
   - 修改API接口地址

### 测试数据
项目包含完整的测试数据，包括：
- 测试订单数据（ID: 1001, 1002, 1003）
- 教师报名记录
- 基础配置数据

可直接使用测试数据进行功能验证。

## 部署说明

### 生产环境部署
1. **后端部署**
   - 构建jar包：`mvn clean package`
   - 配置生产环境数据库连接
   - 启动服务：`java -jar ruoyi-admin.jar`

2. **前端部署**
   - 构建静态文件：`npm run build:prod`
   - 部署到Nginx或其他Web服务器

3. **小程序发布**
   - 在微信公众平台提交审核
   - 等待审核通过后发布

## 项目架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理员后台    │    │   教师端小程序   │    │   家长端小程序   │
│  (Vue + 若依)   │    │  (微信小程序)    │    │  (微信小程序)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端API服务   │
                    │ (Spring Boot)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库   │
                    │     + Redis     │
                    └─────────────────┘
```

## 开发规范

### 代码风格
- Java: 遵循阿里巴巴Java开发手册
- JavaScript: 使用ESLint规范
- SQL: 使用下划线命名法

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 常见问题

### 1. 小程序无法连接后端
- 检查API接口地址配置
- 确认后端服务已启动
- 验证网络连接

### 2. 数据库连接失败
- 检查数据库配置信息
- 确认数据库服务已启动
- 验证用户权限

### 3. 文件上传失败
- 检查文件存储配置
- 确认上传路径权限
- 验证文件大小限制

## 联系方式

- 项目负责人：[姓名]
- 技术支持：[邮箱]
- 问题反馈：[GitHub Issues]

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。 
- 验证文件大小限制

## 联系方式

- 项目负责人：[姓名]
- 技术支持：[邮箱]
- 问题反馈：[GitHub Issues]

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。 
#### 1. 支付系统
- [ ] 微信支付集成
- [ ] 订单支付流程
- [ ] 退款处理
- [ ] 收入管理

#### 2. 消息通知
- [ ] 微信模板消息
- [ ] 系统通知
- [ ] 订单状态通知

#### 3. 评价系统
- [ ] 教师评价
- [ ] 订单评价
- [ ] 评分统计

#### 4. 高级功能
- [ ] 智能匹配
- [ ] 推荐系统
- [ ] 数据分析

## 重要更新记录

### 2024-12-29 - 订单系统重大升级

#### 管理员后台订单更新
**去掉的字段**：
- 订单标题 (title)
- 授课方式 (teachingType)
- 每周次数 (frequency)
- 薪资 (salary)
- 薪资单位 (salaryUnit)
- 订单详情 (description)

**新增的必填项**：
- 辅导模式 (tutoringMode)
- 学生性别 (studentGender)
- 学生详情 (studentDetails)
- 可辅导时间 (availableTutoringTime)
- 每周辅导次数 (weeklySessions)
- 可辅导时间段 (tutoringTimeSlots)
- 每次辅导时长 (sessionDuration)
- 教师性别 (teacherGender)
- 教师类别 (teacherCategory)
- 教师要求 (teachingRequirements)
- 所在城市 (city)

**技术实现**：
- 更新前端表单和表格显示
- 更新后端实体类和MyBatis映射
- 添加数据库city字段
- 更新表单验证规则

**相关文件**：
- `RuoYi-Vue-master/ruoyi-ui/src/views/education/order/list/index.vue`
- `RuoYi-Vue-master/ruoyi-education/src/main/java/com/ruoyi/education/domain/TutorOrder.java`
- `RuoYi-Vue-master/ruoyi-education/src/main/resources/mapper/education/TutorOrderMapper.xml`
- `sql/add_city_field.sql`
- `README_ADMIN_ORDER_UPDATE.md`
- `apply_admin_order_updates.sh`

### 2024-12-29 - 教师认证系统完成

#### 教师端小程序
- [x] 教师注册和登录
- [x] 教师信息完善
- [x] 教师认证流程
- [x] 认证费用支付
- [x] 教师工作台

#### 管理员后台
- [x] 教师管理
- [x] 认证审核
- [x] 费用管理
- [x] 统计报表

### 2024-12-29 - 订单系统第一版完成

#### 核心功能
- [x] 订单发布
- [x] 订单列表
- [x] 订单详情
- [x] 订单报名
- [x] 教师选择

## 数据库设计

### 核心表结构

#### 1. 教师相关
- `teacher_info` - 教师信息表
- `teacher_certification` - 教师认证表
- `teacher_subject` - 教师科目关联表

#### 2. 订单相关
- `tutor_order` - 家教订单表
- `order_application` - 订单报名表
- `order_status_log` - 订单状态日志表

#### 3. 系统相关
- `platform_config` - 平台配置表
- `notification_template` - 通知模板表
- `area_config` - 地区配置表

## 部署指南

### 环境要求
- JDK 1.8+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+
- 微信开发者工具

### 快速开始

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 众桥辅导
   ```

2. **导入数据库**
   ```bash
   mysql -u root -p < sql/ry_20250522.sql
   ```

3. **配置后端**
   ```bash
   cd RuoYi-Vue-master
   # 修改 application.yml 配置数据库连接
   mvn clean compile
   ```

4. **启动管理员后台**
   ```bash
   cd ruoyi-admin
   mvn spring-boot:run
   ```

5. **配置小程序**
   ```bash
   cd applet
   # 修改 app.js 中的配置
   ```

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 前端使用ESLint + Prettier
- 小程序遵循微信开发规范

### 提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 分支管理
- `main` - 主分支
- `develop` - 开发分支
- `feature/*` - 功能分支
- `hotfix/*` - 热修复分支

## 联系方式

- 项目负责人：[姓名]
- 技术支持：[邮箱]
- 问题反馈：[GitHub Issues]

## 最新开发记录

### 2025-08-02 - 家长端首页推荐教师功能开发完成

#### 功能概述
按照提供的UI设计样式，完成了家长端首页的推荐教师列表功能开发。

#### 主要功能
1. **推荐教师列表展示**
   - 教师头像和基本信息（姓名、学校、专业）
   - 辅导年级信息展示
   - 擅长科目信息展示
   - 教学特点描述（支持多行显示，超出部分省略）
   - 查看详情按钮

2. **底部导航栏**
   - 首页、发布家教、我的三个主要入口
   - 当前页面高亮显示
   - 支持页面跳转

3. **交互功能**
   - 点击教师卡片查看详情
   - 底部导航栏页面切换

#### 技术实现

**前端修改**
- `applet/pages/parent/home/<USER>
- `applet/pages/parent/home/<USER>
- `applet/pages/parent/home/<USER>

**后端修改**
- `RuoYi-Vue-master/ruoyi-education/src/main/resources/mapper/education/TutorOrderMapper.xml` - 优化推荐教师查询，返回更详细信息

#### 样式特点
- 卡片式设计，圆角阴影效果
- 清晰的信息层级展示
- 渐变色按钮设计
- 响应式布局适配

#### 数据字段
- `name` - 教师姓名
- `university` - 所在大学
- `major` - 专业
- `grades` - 辅导年级
- `subject` - 擅长科目
- `features` - 教学特点
- `avatar` - 教师头像

#### 下一步计划
1. 完善教师详情页面
2. 实现预约教师功能
3. 开发发布家教需求功能

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
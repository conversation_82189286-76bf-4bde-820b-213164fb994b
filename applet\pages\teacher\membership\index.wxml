<view class="membership-page">
 <navigation-bar title="成为会员" back="{{true}}"></navigation-bar>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:if="{{!loading}}">

    <!-- 会员状态卡片 -->
    <view class="membership-card {{membershipInfo.isVip ? 'vip-card' : 'normal-card'}}">
      <view class="card-background">
        <view class="bg-pattern"></view>
      </view>

      <view class="card-content">
        <view class="status-section">
          <view class="status-badge {{membershipInfo.isVip ? 'vip-badge' : 'normal-badge'}}">
            <text class="badge-icon">{{membershipInfo.isVip ? '👑' : '💎'}}</text>
            <text class="badge-text">{{membershipInfo.isVip ? '高级会员' : '普通会员'}}</text>
          </view>

          <view class="status-desc">
            {{membershipInfo.isVip ? '永久享受专属权益' : '升级解锁更多权益'}}
          </view>

          <view class="member-since" wx:if="{{membershipInfo.isVip && membershipInfo.membershipDate}}">
            成为会员：{{membershipInfo.membershipDate}}
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <button
            wx:if="{{!membershipInfo.isVip}}"
            class="upgrade-button"
            bindtap="upgradeToVip"
            loading="{{upgrading}}"
            disabled="{{upgrading}}">
            <text class="button-text">立即升级</text><text class="button-price">¥{{vipFee}}</text>
          </button>

          <view wx:if="{{membershipInfo.isVip}}" class="vip-status">
            <text class="vip-icon">✨</text>
            <text class="vip-message">您已享受所有权益</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 权益对比 -->
    <view class="benefits-section">
      <view class="section-header">
        <text class="section-title">会员权益对比</text>
      </view>

      <view class="benefits-grid">
        <view
          class="benefit-card {{item.isVip ? 'vip-benefit' : 'normal-benefit'}} {{item.isVip && !membershipInfo.isVip ? 'locked' : 'unlocked'}}"
          wx:for="{{benefits}}"
          wx:key="title">
          <view class="benefit-info">
            <view class="benefit-title">{{item.title}}</view>
            <view class="benefit-desc">{{item.desc}}</view>
          </view>
           <view class="benefit-header">
            <view class="benefit-tag {{item.isVip ? 'vip-tag' : 'free-tag'}}">
              {{item.isVip ? 'VIP专享' : '免费'}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 升级说明 -->
    <view class="upgrade-info" wx:if="{{!membershipInfo.isVip}}">
      <view class="info-header">
        <text class="info-title">为什么要升级？</text>
      </view>
      <view class="info-content">
        <view class="info-point">
          <text class="point-icon">🎯</text>
          <text class="point-text">机构订单质量更高，薪资更优</text>
        </view>
        <view class="info-point">
          <text class="point-icon">💰</text>
          <text class="point-text">一次付费，永久享受所有权益</text>
        </view>
      </view>
    </view>
  </view>
</view>



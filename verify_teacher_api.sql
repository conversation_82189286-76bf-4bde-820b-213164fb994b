-- 验证推荐教师API的SQL查询
-- 这个查询应该与后端API使用的查询完全一致

SELECT
    t.id,
    t.nick_name as name,
    t.real_name,
    t.avatar,
    t.university,
    t.major,
    t.grade,
    t.certification_status,
    CASE 
        WHEN t.certification_status = '1' THEN '学生认证'
        WHEN t.certification_status = '2' THEN '实名认证'
        ELSE '未认证'
    END as certification_text,
    COALESCE(GROUP_CONCAT(DISTINCT s.subject_name), '暂未设置') as subject,
    '经验丰富' as experience,
    IFNULL(t.rating, 4.5) as rating,
    COALESCE(t.self_introduction, '该教师暂未填写自我介绍') as features,
    '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三' as grades
FROM teacher_info t
LEFT JOIN teacher_subject ts ON t.id = ts.teacher_id
LEFT JOIN subject s ON ts.subject_id = s.id
WHERE t.status = '0' AND t.certification_status IN ('1', '2')
GROUP BY t.id, t.nick_name, t.real_name, t.avatar, t.university, t.major, t.grade, t.certification_status, t.rating, t.self_introduction
ORDER BY t.certification_status DESC, t.rating DESC, t.create_time DESC
LIMIT 10;

-- 如果上面的查询没有返回结果，检查以下内容：

-- 1. 检查是否有教师数据
SELECT COUNT(*) as total_teachers FROM teacher_info;

-- 2. 检查教师的状态分布
SELECT status, COUNT(*) as count FROM teacher_info GROUP BY status;

-- 3. 检查教师的认证状态分布
SELECT certification_status, COUNT(*) as count FROM teacher_info GROUP BY certification_status;

-- 4. 检查符合条件的教师（状态为0）
SELECT COUNT(*) as active_teachers FROM teacher_info WHERE status = '0';

-- 5. 检查符合条件的教师（认证状态为1或2）
SELECT COUNT(*) as certified_teachers FROM teacher_info WHERE certification_status IN ('1', '2');

-- 6. 检查同时符合两个条件的教师
SELECT COUNT(*) as qualified_teachers FROM teacher_info WHERE status = '0' AND certification_status IN ('1', '2');

-- 7. 如果没有符合条件的数据，可以更新现有教师的状态
-- UPDATE teacher_info SET status = '0', certification_status = '1' WHERE id IN (SELECT id FROM teacher_info LIMIT 2);

-- 8. 查看所有教师的详细信息
SELECT 
    id,
    nick_name,
    real_name,
    university,
    major,
    status,
    certification_status,
    create_time
FROM teacher_info 
ORDER BY create_time DESC;

Page({
  data: {
    id: '',
    title: '',
    content: '',
    loading: true
  },

  onLoad(options) {
    const { id, title, content } = options;
    
    this.setData({
      id: id || '',
      title: decodeURIComponent(title || ''),
      content: decodeURIComponent(content || ''),
      loading: false
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '通知公告详情'
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: this.data.title,
      path: `/pages/teacher/announcement/detail/index?id=${this.data.id}&title=${encodeURIComponent(this.data.title)}&content=${encodeURIComponent(this.data.content)}`
    };
  }
});

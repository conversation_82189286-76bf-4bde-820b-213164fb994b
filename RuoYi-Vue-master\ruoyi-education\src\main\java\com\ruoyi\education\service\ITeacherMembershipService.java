package com.ruoyi.education.service;

import java.util.List;
import com.ruoyi.education.domain.TeacherMembership;

/**
 * 教师会员Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface ITeacherMembershipService 
{
    /**
     * 查询教师会员
     * 
     * @param id 教师会员主键
     * @return 教师会员
     */
    public TeacherMembership selectTeacherMembershipById(Long id);

    /**
     * 根据教师ID查询有效的会员信息
     * 
     * @param teacherId 教师ID
     * @return 教师会员
     */
    public TeacherMembership selectActiveByTeacherId(Long teacherId);

    /**
     * 查询教师会员列表
     * 
     * @param teacherMembership 教师会员
     * @return 教师会员集合
     */
    public List<TeacherMembership> selectTeacherMembershipList(TeacherMembership teacherMembership);

    /**
     * 新增教师会员
     * 
     * @param teacherMembership 教师会员
     * @return 结果
     */
    public int insertTeacherMembership(TeacherMembership teacherMembership);

    /**
     * 修改教师会员
     * 
     * @param teacherMembership 教师会员
     * @return 结果
     */
    public int updateTeacherMembership(TeacherMembership teacherMembership);

    /**
     * 批量删除教师会员
     * 
     * @param ids 需要删除的教师会员主键集合
     * @return 结果
     */
    public int deleteTeacherMembershipByIds(Long[] ids);

    /**
     * 删除教师会员信息
     * 
     * @param id 教师会员主键
     * @return 结果
     */
    public int deleteTeacherMembershipById(Long id);

    /**
     * 检查教师是否为有效VIP会员
     * 
     * @param teacherId 教师ID
     * @return 是否为VIP
     */
    public boolean isValidVip(Long teacherId);
}

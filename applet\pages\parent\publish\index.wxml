<!--pages/parent/publish/index.wxml-->
<view class="page-container tabbar-page">
  <!-- 自定义导航栏 -->
  <navigation-bar title="发布家教" />

  <!-- 表单内容 -->
  <scroll-view class="form-scroll" scroll-y="true">
    <form bindsubmit="submitOrder">

      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 订单标题 -->
        <view class="form-item">
          <view class="label required">订单标题</view>
          <input class="input" name="title" value="{{formData.title}}" placeholder="请输入订单标题" bindinput="onInputChange" data-field="title" />
        </view>

        <!-- 科目选择 -->
        <view class="form-item">
          <view class="label required">辅导科目</view>
          <picker mode="selector" range="{{subjects}}" range-key="name" value="{{subjectIndex}}" bindchange="onSubjectChange">
            <view class="picker">{{formData.subjectName || '请选择科目'}}</view>
          </picker>
        </view>

        <!-- 年级选择 -->
        <view class="form-item">
          <view class="label required">学生年级</view>
          <picker mode="multiArray" range="{{gradeOptions}}" range-key="name" value="{{gradeIndex}}" bindchange="onGradeChange" bindcolumnchange="onGradeColumnChange">
            <view class="picker">{{formData.grade || '请选择年级'}}</view>
          </picker>
        </view>

        <!-- 学生性别 -->
        <view class="form-item">
          <view class="label">学生性别</view>
          <radio-group class="radio-group" bindchange="onGenderChange">
            <label class="radio-item">
              <radio value="0" checked="{{formData.studentGender === '0'}}" />男
            </label>
            <label class="radio-item">
              <radio value="1" checked="{{formData.studentGender === '1'}}" />女
            </label>
            <label class="radio-item">
              <radio value="2" checked="{{formData.studentGender === '2'}}" />不限
            </label>
          </radio-group>
        </view>
      </view>

      <!-- 辅导要求 -->
      <view class="form-section">
        <view class="section-title">辅导要求</view>

        <!-- 辅导模式 -->
        <view class="form-item">
          <view class="label required">辅导模式</view>
          <radio-group class="radio-group" bindchange="onModeChange">
            <label class="radio-item">
              <radio value="上门辅导" checked="{{formData.tutoringMode === '上门辅导'}}" />上门辅导
            </label>
            <label class="radio-item">
              <radio value="在线辅导" checked="{{formData.tutoringMode === '在线辅导'}}" />在线辅导
            </label>
          </radio-group>
        </view>

        <!-- 授课方式 -->
        <view class="form-item">
          <view class="label">授课方式</view>
          <radio-group class="radio-group" bindchange="onTeachingTypeChange">
            <label class="radio-item">
              <radio value="1" checked="{{formData.teachingType === '1'}}" />上门授课
            </label>
            <label class="radio-item">
              <radio value="2" checked="{{formData.teachingType === '2'}}" />在线授课
            </label>
            <label class="radio-item">
              <radio value="3" checked="{{formData.teachingType === '3'}}" />指定地点
            </label>
          </radio-group>
        </view>

        <!-- 每周辅导次数 -->
        <view class="form-item">
          <view class="label">每周次数</view>
          <input class="input" name="weeklySessions" type="number" value="{{formData.weeklySessions}}" placeholder="请输入每周辅导次数" bindinput="onInputChange" data-field="weeklySessions" />
        </view>

        <!-- 每次辅导时长 -->
        <view class="form-item">
          <view class="label">每次时长</view>
          <view class="input-unit">
            <input class="input" name="sessionDuration" type="number" value="{{formData.sessionDuration}}" placeholder="请输入时长" bindinput="onInputChange" data-field="sessionDuration" />
            <text class="unit">分钟</text>
          </view>
        </view>

        <!-- 可辅导时间 -->
        <view class="form-item">
          <view class="label required">可辅导时间</view>
          <view class="weekdays-container">
            <block wx:for="{{weekdays}}" wx:key="value">
              <view class="weekday-item {{selectedWeekdays.includes(item.value) ? 'selected' : ''}}"
                    bindtap="toggleWeekday" data-value="{{item.value}}">
                {{item.name}}
              </view>
            </block>
          </view>
        </view>

        <!-- 可辅导时间段 -->
        <view class="form-item">
          <view class="label required">可辅导时间段</view>
          <view class="time-slot-container">
            <picker mode="time" value="{{formData.startTime}}" bindchange="onStartTimeChange">
              <view class="time-picker">
                <text class="time-label">开始时间</text>
                <text class="time-value">{{formData.startTime || '请选择'}}</text>
              </view>
            </picker>
            <text class="time-separator">至</text>
            <picker mode="time" value="{{formData.endTime}}" bindchange="onEndTimeChange">
              <view class="time-picker">
                <text class="time-label">结束时间</text>
                <text class="time-value">{{formData.endTime || '请选择'}}</text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 时段补充说明 -->
        <view class="form-item">
          <view class="label">时段说明</view>
          <textarea class="textarea" name="timeSlotNotes" value="{{formData.timeSlotNotes}}" placeholder="时段补充说明（可选）" bindinput="onInputChange" data-field="timeSlotNotes"></textarea>
        </view>
      </view>

      <!-- 教师要求 -->
      <view class="form-section">
        <view class="section-title">教师要求</view>

        <!-- 教师性别要求 -->
        <view class="form-item">
          <view class="label">教师性别</view>
          <radio-group class="radio-group" bindchange="onTeacherGenderChange">
            <label class="radio-item">
              <radio value="男" checked="{{formData.teacherGender === '男'}}" />男老师
            </label>
            <label class="radio-item">
              <radio value="女" checked="{{formData.teacherGender === '女'}}" />女老师
            </label>
            <label class="radio-item">
              <radio value="不限" checked="{{formData.teacherGender === '不限'}}" />不限
            </label>
          </radio-group>
        </view>

        <!-- 教师类别 -->
        <view class="form-item">
          <view class="label">教师类别</view>
          <picker mode="selector" range="{{teacherCategories}}" value="{{teacherCategoryIndex}}" bindchange="onTeacherCategoryChange">
            <view class="picker">{{formData.teacherCategory || '请选择教师类别'}}</view>
          </picker>
        </view>

        <!-- 教学要求 -->
        <view class="form-item">
          <view class="label">教学要求</view>
          <textarea class="textarea" name="teachingRequirements" value="{{formData.teachingRequirements}}" placeholder="请描述对教师的具体要求" bindinput="onInputChange" data-field="teachingRequirements"></textarea>
        </view>
      </view>

      <!-- 学生信息 -->
      <view class="form-section">
        <view class="section-title">学生信息</view>

        <!-- 学生详情 -->
        <view class="form-item">
          <view class="label">学生情况</view>
          <textarea class="textarea" name="studentDetails" value="{{formData.studentDetails}}" placeholder="请描述学生的学习情况、性格特点等" bindinput="onInputChange" data-field="studentDetails"></textarea>
        </view>
      </view>

      <!-- 薪资信息 -->
      <view class="form-section">
        <view class="section-title">薪资信息</view>

        <!-- 薪资 -->
        <view class="form-item">
          <view class="label required">薪资</view>
          <view class="salary-input">
            <input class="input" name="salary" type="digit" value="{{formData.salary}}" placeholder="请输入薪资" bindinput="onInputChange" data-field="salary" />
            <picker mode="selector" range="{{salaryUnits}}" range-key="name" value="{{salaryUnitIndex}}" bindchange="onSalaryUnitChange">
              <view class="unit-picker">{{formData.salaryUnitText || '元/小时'}}</view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="form-section">
        <view class="section-title">地址信息</view>

        <!-- 所在城市 -->
        <view class="form-item">
          <view class="label required">所在城市</view>
          <input class="input" name="city" value="{{formData.city}}" placeholder="请输入所在城市" bindinput="onInputChange" data-field="city" />
        </view>

        <!-- 详细地址 -->
        <view class="form-item">
          <view class="label">详细地址</view>
          <textarea class="textarea" name="detailedAddress" value="{{formData.detailedAddress}}" placeholder="请输入详细地址" bindinput="onInputChange" data-field="detailedAddress"></textarea>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="form-section">
        <view class="section-title">联系信息</view>

        <!-- 联系人姓名 -->
        <view class="form-item">
          <view class="label required">联系人</view>
          <view class="contact-name-container">
            <input class="contact-name-input" name="contactName" value="{{formData.contactName}}" placeholder="请输入联系人姓名" bindinput="onInputChange" data-field="contactName" />
            <picker mode="selector" range="{{titleOptions}}" range-key="name" value="{{titleIndex}}" bindchange="onTitleChange">
              <view class="title-picker">{{formData.contactTitle || '先生'}}</view>
            </picker>
          </view>
        </view>

        <!-- 联系电话 -->
        <view class="form-item">
          <view class="label required">联系电话</view>
          <input class="input" name="contactPhone" value="{{formData.contactPhone}}" placeholder="联系电话" disabled="true" />
          <text class="phone-note">默认为登录手机号</text>
        </view>

        <!-- 联系微信 -->
        <view class="form-item">
          <view class="label">微信号</view>
          <input class="input" name="contactWechat" value="{{formData.contactWechat}}" placeholder="请输入微信号（可选）" bindinput="onInputChange" data-field="contactWechat" />
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" form-type="submit" loading="{{submitting}}">
          {{submitting ? '发布中...' : '发布家教订单'}}
        </button>
      </view>

    </form>
  </scroll-view>
</view>
-- 家长信息表
CREATE TABLE `parent_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `total_orders` int(11) DEFAULT '0' COMMENT '累计发布订单数',
  `completed_orders` int(11) DEFAULT '0' COMMENT '完成订单数',
  `average_rating` decimal(3,2) DEFAULT '0.00' COMMENT '平均评分',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `blacklist_status` char(1) DEFAULT '0' COMMENT '拉黑状态（0正常 1拉黑）',
  `blacklist_reason` varchar(500) DEFAULT NULL COMMENT '拉黑原因',
  `blacklist_time` datetime DEFAULT NULL COMMENT '拉黑时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`),
  KEY `idx_blacklist_status` (`blacklist_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长信息表';

-- 插入测试数据（可选）
INSERT INTO `parent_info` (`openid`, `user_name`, `nick_name`, `avatar`, `real_name`, `phone_number`, `total_orders`, `completed_orders`, `average_rating`, `status`, `blacklist_status`, `create_by`, `create_time`) VALUES
('mock_openid_for_parent', 'parent:1', '测试家长', '/assets/images/default_avatar.png', '张家长', '13800138000', 0, 0, 0.00, '0', '0', 'system', NOW());

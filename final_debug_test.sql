-- 最终调试测试SQL

-- 1. 检查表是否存在
SHOW TABLES LIKE 'teacher_info';

-- 2. 检查表结构
DESCRIBE teacher_info;

-- 3. 查看所有教师数据（不加任何条件）
SELECT 
    id, nick_name, status, certification_status, deleted, del_flag,
    university, major, teaching_subjects
FROM teacher_info;

-- 4. 检查字段值的具体内容和长度
SELECT
    id,
    nick_name,
    status,
    LENGTH(status) as status_length,
    certification_status,
    LENGTH(certification_status) as cert_length,
    deleted,
    CASE WHEN deleted IS NULL THEN 'NULL' ELSE CAST(deleted AS CHAR) END as deleted_value
FROM teacher_info
LIMIT 5;

-- 5. 测试各种条件组合
-- 只测试status条件
SELECT COUNT(*) as count_status_0 FROM teacher_info WHERE status = '0';
SELECT COUNT(*) as count_status_0_int FROM teacher_info WHERE status = 0;

-- 只测试certification_status条件
SELECT COUNT(*) as count_cert_2 FROM teacher_info WHERE certification_status = '2';
SELECT COUNT(*) as count_cert_2_int FROM teacher_info WHERE certification_status = 2;

-- 只测试deleted条件
SELECT COUNT(*) as count_deleted_0 FROM teacher_info WHERE deleted = 0;
SELECT COUNT(*) as count_deleted_null FROM teacher_info WHERE deleted IS NULL;

-- 6. 测试组合条件
SELECT COUNT(*) as count_all_conditions 
FROM teacher_info 
WHERE status = '0' 
  AND certification_status = '2' 
  AND deleted = 0;

-- 7. 如果上面返回0，尝试不同的数据类型
SELECT COUNT(*) as count_int_conditions 
FROM teacher_info 
WHERE status = 0 
  AND certification_status = 2 
  AND deleted = 0;

-- 8. 最终的完整查询测试
SELECT
    t.id,
    t.nick_name as name,
    t.real_name,
    t.avatar,
    t.university,
    t.major,
    t.grade,
    t.certification_status,
    t.status,
    t.deleted
FROM teacher_info t
WHERE t.status = '0'
  AND t.certification_status = '2'
  AND t.deleted = 0
LIMIT 10;

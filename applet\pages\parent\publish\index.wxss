/* pages/parent/publish/index.wxss */

.page-container {
  background-color: #f6f7f8;
  min-height: 100vh;
}

.form-scroll {
   height: calc(100vh - 200rpx - env(safe-area-inset-bottom) - var(--status-bar-height, 44px));
  background-color: #f8f9fa;
}

/* 表单区域 */
.form-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #667eea;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.label.required::before {
  content: '*';
  color: #ff4757;
  margin-right: 8rpx;
}

/* 输入框 */
.input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.input:focus {
  background: white;
  border-color: #667eea;
}

.input::placeholder {
  color: #999;
}

/* 文本域 */
.textarea {
  width: 100%;
  min-height: 120rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.textarea:focus {
  background: white;
  border-color: #667eea;
}

.textarea::placeholder {
  color: #999;
}

/* 选择器 */
.picker {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.picker:active {
  background: white;
  border-color: #667eea;
}

.picker::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
  margin-left: auto;
}

/* 单选框组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.radio-item radio {
  margin-right: 12rpx;
  transform: scale(0.8);
}

/* 薪资输入 */
.salary-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.salary-input .input {
  flex: 1;
}

.unit-picker {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
  justify-content: center;
}

.unit-picker::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 6rpx solid #999;
  margin-left: 12rpx;
}

/* 输入单位 */
.input-unit {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding-right: 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.input-unit:focus-within {
  background: white;
  border-color: #667eea;
}

.input-unit .input {
  background: transparent;
  border: none;
  padding-right: 0;
}

.unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

/* 提交区域 */
.submit-section {
  padding: 40rpx 30rpx;
  background: white;
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.submit-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.submit-btn[loading] {
  opacity: 0.6;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .form-section {
    margin: 20rpx 20rpx;
    padding: 30rpx 20rpx;
  }

  .submit-section {
    padding: 30rpx 20rpx;
  }
}
/* pages/parent/publish/index.wxss */

.page-container {
  background-color: #f6f7f8;
  min-height: 100vh;
}

.form-scroll {
  height: calc(100vh - 88rpx - env(safe-area-inset-bottom));
  padding-bottom: 120rpx;
}

/* 表单区域 */
.form-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #667eea;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.label.required::before {
  content: '*';
  color: #ff4757;
  margin-right: 8rpx;
}

/* 输入框 */
.input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.input:focus {
  background: white;
  border-color: #667eea;
}

.input::placeholder {
  color: #999;
}

/* 文本域 */
.textarea {
  width: 100%;
  min-height: 120rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.textarea:focus {
  background: white;
  border-color: #667eea;
}

.textarea::placeholder {
  color: #999;
}

/* 选择器 */
.picker {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.picker:active {
  background: white;
  border-color: #667eea;
}

.picker::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
  margin-left: auto;
}

/* 单选框组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.radio-item radio {
  margin-right: 12rpx;
  transform: scale(0.8);
}

/* 薪资输入 */
.salary-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.salary-input .input {
  flex: 1;
}

.unit-picker {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
  justify-content: center;
}

.unit-picker::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 6rpx solid #999;
  margin-left: 12rpx;
}

/* 输入单位 */
.input-unit {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding-right: 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.input-unit:focus-within {
  background: white;
  border-color: #667eea;
}

.input-unit .input {
  background: transparent;
  border: none;
  padding-right: 0;
}

.unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

/* 提交区域 */
.submit-section {
  padding: 40rpx 30rpx;
  background: white;
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.submit-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.submit-btn[loading] {
  opacity: 0.6;
}

/* 年级选择器样式 */
.grade-selector {
  display: flex;
  gap: 20rpx;
}

.category-picker, .grade-picker {
  flex: 1;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  font-size: 28rpx;
  color: #495057;
}

.grade-picker.disabled {
  background: #f1f3f4;
  color: #9aa0a6;
}

/* 科目选择器样式 */
.subject-selector {
  min-height: 80rpx;
}

.selected-subjects {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.subject-tag {
  display: flex;
  align-items: center;
  background: #e3f2fd;
  color: #1976d2;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.remove-btn {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
}

.add-subject-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: #f8f9fa;
  border: 2rpx dashed #dee2e6;
  border-radius: 12rpx;
  color: #6c757d;
  font-size: 28rpx;
}

.add-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 时间选择器样式 */
.time-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.time-slot-selector {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.time-slot-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.time-label {
  font-size: 26rpx;
  color: #495057;
  min-width: 160rpx;
}

.time-picker {
  flex: 1;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  font-size: 28rpx;
  color: #495057;
}

/* 联系人样式 */
.contact-name-selector {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.contact-name-input {
  flex: 1;
}

.gender-suffix-picker {
  min-width: 120rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  font-size: 28rpx;
  color: #495057;
  text-align: center;
}

.phone-display {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.phone-input {
  background: #f1f3f4 !important;
  color: #9aa0a6 !important;
}

.phone-note {
  font-size: 22rpx;
  color: #9aa0a6;
}

/* 科目选择弹窗样式 */
.subject-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.subject-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f3f4;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
}

.modal-close {
  font-size: 48rpx;
  color: #9aa0a6;
  line-height: 1;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
}

.subject-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.subject-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  min-width: 200rpx;
  transition: all 0.2s ease;
}

.subject-option.selected {
  background: #e3f2fd;
  border-color: #1976d2;
  color: #1976d2;
}

.subject-name {
  font-size: 28rpx;
}

.check-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 16rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f1f3f4;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
}

.confirm-btn {
  background: #007bff;
  color: white;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .form-section {
    margin: 20rpx 20rpx;
    padding: 30rpx 20rpx;
  }

  .submit-section {
    padding: 30rpx 20rpx;
  }
}
# 主动认证费用检查功能测试指南

## 功能优化说明

### 优化前的流程
```
用户点击报名 → 发送报名请求 → 后端检查认证费用 → 
返回错误 → 前端处理错误 → 显示提示弹窗
```

### 优化后的流程
```
用户点击报名 → 前端主动检查认证费用 → 
如果未缴费：直接显示提示弹窗
如果已缴费：继续报名流程
```

## 优化优势

1. **响应更快**：无需等待后端返回错误，立即显示提示
2. **减少网络请求**：避免不必要的报名请求
3. **用户体验更好**：减少等待时间，提示更及时
4. **逻辑更清晰**：前端主动控制流程，而不是被动处理错误

## 实现方案

### 1. 订单详情页面 (pages/teacher/order/detail/index.js)

**新增方法**：
```javascript
// 检查认证费用缴纳状态
checkCertificationFeeStatus() {
  return new Promise((resolve) => {
    getCertificationFeeInfo().then(res => {
      if (res.code === 200) {
        const feePaidStatus = res.feePaidStatus;
        
        if (feePaidStatus === '0') {
          // 未缴纳，显示引导弹窗
          wx.showModal({
            title: '需要缴纳认证费用',
            content: '您还未缴纳认证费用，需要先缴纳认证费用才能报名订单。是否前往缴纳？',
            confirmText: '去缴费',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.navigateTo({
                  url: '/pages/teacher/deposit/index'
                });
              }
              resolve(false); // 未缴费，不能继续
            }
          });
        } else {
          resolve(true); // 已缴费，可以继续
        }
      } else {
        wx.showToast({
          title: '获取认证费用状态失败',
          icon: 'none'
        });
        resolve(false);
      }
    }).catch(err => {
      wx.showToast({
        title: '网络错误，请稍后重试',
        icon: 'none'
      });
      resolve(false);
    });
  });
}
```

**修改报名方法**：
```javascript
doApply() {
  if (this.data.isApplying) return;
  
  // 先检查认证费用缴纳状态
  this.checkCertificationFeeStatus().then((canProceed) => {
    if (!canProceed) {
      return; // 认证费用未缴纳，已显示引导弹窗
    }
    
    // 认证费用已缴纳，继续报名流程
    this.setData({ isApplying: true });
    // ... 原有的报名逻辑
  });
}
```

### 2. 生源页面 (pages/teacher/student_source/index.js)

生源页面已经实现了主动检查的逻辑：
```javascript
startApplyProcess(orderId) {
  // 首先检查认证费用缴纳状态
  this.checkCertificationFeeStatus().then((feePaid) => {
    if (!feePaid) {
      return; // 认证费用未缴纳，已引导到缴费页面
    }
    
    // 认证费用已缴纳，继续后续流程
    // ...
  });
}
```

## 测试步骤

### 步骤1：准备测试环境
```sql
-- 设置教师为未缴费状态
UPDATE teacher_info 
SET certification_fee_paid = '0' 
WHERE id = 30;  -- 替换为实际的教师ID

-- 验证设置
SELECT id, real_name, certification_fee_paid FROM teacher_info WHERE id = 30;
```

### 步骤2：测试订单详情页面
1. 登录教师账号
2. 进入任意订单详情页面
3. 点击"立即报名"按钮
4. **预期结果**：立即显示认证费用提示弹窗（无需等待）

### 步骤3：测试生源页面
1. 进入生源页面
2. 点击任意订单的"报名"按钮
3. **预期结果**：立即显示认证费用提示弹窗（无需等待）

### 步骤4：测试跳转功能
1. 在弹窗中点击"去缴费"按钮
2. **预期结果**：跳转到 `/pages/teacher/deposit/index` 页面

### 步骤5：测试已缴费状态
```sql
-- 设置为已缴费状态
UPDATE teacher_info 
SET certification_fee_paid = '1' 
WHERE id = 30;
```
1. 重新测试报名功能
2. **预期结果**：直接进入报名流程，无认证费用提示

## 性能对比

### 优化前的网络请求
```
1. 点击报名按钮
2. 发送 POST /applet/teacher/order/apply 请求
3. 后端返回错误：CERTIFICATION_FEE_NOT_PAID
4. 前端处理错误并显示弹窗
总耗时：网络往返时间 + 后端处理时间
```

### 优化后的网络请求
```
1. 点击报名按钮
2. 发送 GET /applet/teacher/certification/fee-info 请求
3. 前端根据结果决定是否继续
4. 如果已缴费，再发送报名请求
总耗时：GET请求时间（通常比POST更快）
```

## 用户体验改进

### 1. 响应速度
- **优化前**：需要等待完整的报名请求处理
- **优化后**：只需要等待轻量级的状态查询

### 2. 错误提示
- **优化前**：显示通用的"报名失败"，然后才显示具体原因
- **优化后**：直接显示具体的认证费用提示

### 3. 用户操作
- **优化前**：用户可能会疑惑为什么报名失败
- **优化后**：用户清楚知道需要先缴费才能报名

## 调试信息

### 前端日志
```javascript
console.log('认证费用状态检查结果:', res);
// 输出：{code: 200, feeAmount: "99.00", feePaidStatus: "0"}

console.log('认证费用未缴纳，显示引导弹窗');
```

### 预期的用户交互流程
```
1. 用户点击报名
2. 立即显示loading（很短暂）
3. 显示认证费用提示弹窗
4. 用户点击"去缴费"
5. 跳转到缴费页面
```

## 故障排除

### 问题1：仍然显示"报名失败"
**原因**：可能是缓存问题或API返回格式不正确
**解决**：检查 `getCertificationFeeInfo()` 的返回数据格式

### 问题2：弹窗显示延迟
**原因**：网络请求较慢
**解决**：可以添加loading提示

### 问题3：跳转失败
**原因**：页面路径不正确
**解决**：确认路径为 `/pages/teacher/deposit/index`

## 后续优化建议

1. **添加缓存**：缓存认证费用状态，避免重复请求
2. **预加载**：在页面加载时就获取认证费用状态
3. **状态同步**：缴费成功后自动更新页面状态
4. **离线处理**：网络异常时的友好提示

现在用户体验应该更加流畅了！

# 教师详情功能测试指南

## 1. 数据库准备

首先执行SQL脚本添加字段：

```sql
-- 添加接课成功经历字段
ALTER TABLE teacher_info 
ADD COLUMN success_experience TEXT COMMENT '接课成功经历（JSON格式存储）' AFTER success_orders;

-- 为测试教师添加示例数据
UPDATE teacher_info 
SET success_experience = JSON_ARRAY(
    JSON_OBJECT(
        'orderId', 1001,
        'orderNo', 'ORDER_20240101001',
        'subjectName', '数学',
        'grade', '初三',
        'completedTime', '2024-01-15 18:00:00',
        'duration', '2个月',
        'improvement', '成绩从70分提升到85分',
        'feedback', '老师教学认真负责，孩子很喜欢',
        'rating', 5.0
    ),
    JSON_OBJECT(
        'orderId', 1002,
        'orderNo', 'ORDER_20240201002',
        'subjectName', '英语',
        'grade', '高一',
        'completedTime', '2024-02-20 19:30:00',
        'duration', '1个月',
        'improvement', '英语口语明显提升',
        'feedback', '老师很有耐心，教学方法很好',
        'rating', 4.8
    )
)
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 1) as temp);
```

## 2. 后端测试

### 测试推荐教师接口
```bash
curl -X GET "http://t8f63f47.natappfree.cc/applet/parent/teachers/recommend" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 测试教师详情接口
```bash
# 替换{teacherId}为实际的教师ID
curl -X GET "http://t8f63f47.natappfree.cc/applet/parent/teachers/{teacherId}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 3. 前端测试

### 修复内容
- ✅ 修复了API导入问题：从ES6 import改为CommonJS require
- ✅ 添加了接课成功经历的展示
- ✅ 实现了时间格式化显示

### 测试步骤
1. 重启后端服务
2. 打开小程序家长端
3. 进入首页，查看推荐教师列表
4. 点击教师卡片，进入详情页面
5. 检查是否显示接课成功经历

## 4. 预期效果

### 推荐教师列表
- 显示教师姓名格式：X老师
- 显示认证状态徽章
- 显示教授科目和自我介绍

### 教师详情页面
- 头部：头像、姓名、年龄、性别、认证徽章、评分
- 教学信息：科目、学校、专业、年级
- 统计数据：累计接单、成功完成、平均评分
- 背景信息：教育背景、经验背景、自我介绍
- 接课成功经历：每条经历包含科目、时间、效果、反馈、评分

## 5. 常见问题

### API调用失败
- 检查网络连接
- 确认后端服务正在运行
- 检查token是否有效

### 数据显示异常
- 确认数据库字段已添加
- 检查后端日志是否有错误
- 验证JSON数据格式是否正确

### 样式显示问题
- 检查wxss文件是否正确加载
- 确认组件路径是否正确
- 验证数据绑定是否正确

## 6. 调试信息

在小程序开发者工具的控制台中查看：
- API请求和响应日志
- 数据处理过程
- 错误信息详情

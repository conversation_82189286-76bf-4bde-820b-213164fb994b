// pages/parent/profile/index.js
const parentApi = require('../../../api/parent.js');
const app = getApp();

Page({
  data: {
    userInfo: {},
    stats: {
      publishedOrders: 0,
      ongoingOrders: 0,
      completedOrders: 0
    }
  },

  /**
   * 页面加载
   */
  onLoad() {

  },

  /**
   * 页面显示
   */
  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 2
      });
    }

    this.loadUserInfo();
    this.loadStats();
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const res = await parentApi.getParentProfile();
      if (res.code === 200) {
        this.setData({
          userInfo: res.data
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 使用本地存储的用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ userInfo });
      }
    }
  },

  /**
   * 加载统计数据
   */
  async loadStats() {
    try {
      const res = await parentApi.getParentStats();
      if (res.code === 200) {
        this.setData({
          stats: res.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  /**
   * 编辑个人信息
   */
  editProfile() {
    wx.navigateTo({
      url: '/pages/parent/profile/edit/index'
    });
  },

  /**
   * 查看所有订单
   */
  viewAllOrders() {
    wx.navigateTo({
      url: '/pages/parent/order/list/index?type=all'
    });
  },

  /**
   * 查看进行中订单
   */
  viewOngoingOrders() {
    wx.navigateTo({
      url: '/pages/parent/order/list/index?type=ongoing'
    });
  },

  /**
   * 查看已完成订单
   */
  viewCompletedOrders() {
    wx.navigateTo({
      url: '/pages/parent/order/list/index?type=completed'
    });
  },



  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.clearStorageSync();
          
          // 清除全局数据
          app.globalData.token = null;
          app.globalData.userInfo = null;
          app.globalData.userType = 'teacher';
          
          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '众桥辅导 - 找到最适合的家教老师',
      path: '/pages/parent/home/<USER>',
      imageUrl: '/assets/images/share_logo.png'
    };
  }
});

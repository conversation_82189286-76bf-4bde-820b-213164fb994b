// pages/parent/profile/index.js
const { request } = require('../../../utils/request.js');
const app = getApp();

Page({
  data: {
    userInfo: null,
    menuItems: [
      {
        icon: '📋',
        title: '我的订单',
        desc: '查看发布的家教需求',
        url: '/pages/parent/order/list/index'
      },
      {
        icon: '💰',
        title: '支付记录',
        desc: '查看支付历史',
        url: '/pages/parent/payment/list/index'
      },
      {
        icon: '⭐',
        title: '我的评价',
        desc: '查看给教师的评价',
        url: '/pages/parent/review/list/index'
      },
      {
        icon: '👨‍🏫',
        title: '收藏教师',
        desc: '我收藏的优秀教师',
        url: '/pages/parent/teacher/favorite/index'
      },
      {
        icon: '🔔',
        title: '消息通知',
        desc: '系统消息和订单通知',
        url: '/pages/parent/message/list/index'
      },
      {
        icon: '⚙️',
        title: '设置',
        desc: '账户设置和隐私设置',
        url: '/pages/parent/setting/index'
      }
    ]
  },

  /**
   * 页面加载
   */
  onLoad() {
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 2
      });
    }

    this.loadUserInfo();
  },

  /**
   * 初始化页面
   */
  initPage() {
    wx.setNavigationBarTitle({
      title: '我的'
    });
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const res = await request({
        url: '/applet/parent/profile',
        method: 'GET'
      });
      
      if (res.code === 200) {
        this.setData({
          userInfo: res.data
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 使用本地存储的用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ userInfo });
      }
    }
  },

  /**
   * 菜单项点击
   */
  onMenuItemTap(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({
        url: url
      });
    }
  },

  /**
   * 编辑个人信息
   */
  editProfile() {
    wx.navigateTo({
      url: '/pages/parent/profile/edit/index'
    });
  },

  /**
   * 客服联系
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 关于我们
   */
  aboutUs() {
    wx.navigateTo({
      url: '/pages/common/about/index'
    });
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.clearStorageSync();
          
          // 清除全局数据
          app.globalData.token = null;
          app.globalData.userInfo = null;
          app.globalData.userType = 'teacher';
          
          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '众桥辅导 - 找到最适合的家教老师',
      path: '/pages/parent/home/<USER>',
      imageUrl: '/assets/images/share_logo.png'
    };
  }
});

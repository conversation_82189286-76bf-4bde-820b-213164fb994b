package com.ruoyi.education.controller.applet;

import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.education.domain.TeacherInfo;
import com.ruoyi.education.domain.TeacherMembership;
import com.ruoyi.education.mapper.TeacherInfoMapper;
import com.ruoyi.education.service.ITeacherMembershipService;
import com.ruoyi.education.service.IPlatformConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 小程序教师会员管理Controller
 * 
 * <AUTHOR>
 * @date 2024-12-30
 */
@RestController
@RequestMapping("/applet/teacher/membership")
public class AppletMembershipController extends BaseController
{
    @Autowired
    private ITeacherMembershipService teacherMembershipService;

    @Autowired
    private WxPayService wxPayService;
    
    @Autowired
    private TeacherInfoMapper teacherInfoMapper;
    
    @Autowired
    private IPlatformConfigService platformConfigService;

    /**
     * 获取当前教师的会员信息
     */
    @GetMapping("/info")
    public AjaxResult getMembershipInfo()
    {
        try {
            LoginUser loginUser = getLoginUser();
            Long teacherId = loginUser.getUserId();

            Map<String, Object> result = new HashMap<>();

            // 简化逻辑：默认为普通会员，避免复杂的数据库查询
            boolean isVip = false;
            String membershipDate = null;

            // 尝试检查会员状态，如果失败则默认为普通会员
            try {
                if (teacherMembershipService != null) {
                    isVip = teacherMembershipService.isValidVip(teacherId);
                    if (isVip) {
                        TeacherMembership membership = teacherMembershipService.selectActiveByTeacherId(teacherId);
                        if (membership != null && membership.getMembershipDate() != null) {
                            membershipDate = membership.getMembershipDate().toString();
                        }
                    }
                }
            } catch (Exception membershipException) {
                logger.warn("检查会员状态失败，默认为普通会员: " + membershipException.getMessage());
                isVip = false;
            }

            result.put("isVip", isVip);
            result.put("membershipType", isVip ? "2" : "1");
            if (membershipDate != null) {
                result.put("membershipDate", membershipDate);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取会员信息失败", e);
            // 返回默认的普通会员状态，避免前端报错
            Map<String, Object> defaultResult = new HashMap<>();
            defaultResult.put("isVip", false);
            defaultResult.put("membershipType", "1");
            return AjaxResult.success(defaultResult);
        }
    }

    /**
     * 升级为高级会员
     */
    @PostMapping("/upgrade")
    @Log(title = "升级会员", businessType = BusinessType.INSERT)
    public AjaxResult upgradeToVip(@RequestBody Map<String, Object> params)
    {
        try {
            LoginUser loginUser = getLoginUser();
            Long teacherId = loginUser.getUserId();
            
            String membershipType = (String) params.get("membershipType");
            BigDecimal paymentAmount = new BigDecimal(params.get("paymentAmount").toString());
            Boolean isRenewal = (Boolean) params.getOrDefault("isRenewal", false);
            
            // 获取VIP费用配置
            String vipFeeConfig = platformConfigService.selectConfigValueByKey("teacher.vip.fee", "0");
            BigDecimal configFee = new BigDecimal(vipFeeConfig);
            
            // 验证支付金额
            if (paymentAmount.compareTo(configFee) != 0) {
                return AjaxResult.error("支付金额不正确");
            }
            
            // 获取教师信息
            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(teacherId);
            if (teacherInfo == null) {
                return AjaxResult.error("教师信息不存在");
            }
            
            // 检查是否已是高级会员
            if (!isRenewal && "2".equals(teacherInfo.getMembershipType())) {
                return AjaxResult.error("您已是高级会员");
            }

            // 创建会员记录
            TeacherMembership membership = new TeacherMembership();
            membership.setTeacherId(teacherId);
            membership.setMembershipType(membershipType);
            membership.setMembershipDate(LocalDateTime.now());
            membership.setPaymentAmount(paymentAmount);
            membership.setPaymentStatus("1"); // 已支付
            membership.setPaymentTime(LocalDateTime.now());
            membership.setCreateBy(loginUser.getUsername());
            membership.setCreateTime(new Date());

            // 保存会员记录
            int result = teacherMembershipService.insertTeacherMembership(membership);
            if (result > 0) {
                // 更新教师会员状态
                teacherInfo.setMembershipType(membershipType);
                teacherInfo.setMembershipDate(new Date());
                teacherInfo.setUpdateBy(loginUser.getUsername());
                teacherInfo.setUpdateTime(new Date());
                teacherInfoMapper.updateTeacherInfo(teacherInfo);

                return AjaxResult.success("升级成功");
            } else {
                return AjaxResult.error("升级失败");
            }
        } catch (Exception e) {
            logger.error("升级会员失败", e);
            return AjaxResult.error("升级失败：" + e.getMessage());
        }
    }

    /**
     * 获取会员权益说明
     */
    @GetMapping("/benefits")
    public AjaxResult getMembershipBenefits()
    {
        try {
            Map<String, Object> benefits = new HashMap<>();
            benefits.put("vipBenefits", new String[]{
                "查看机构订单详情",
                "获取机构联系方式",
                "优先推荐展示",
                "专属客服支持"
            });
            benefits.put("normalBenefits", new String[]{
                "查看个人订单",
                "基础数据统计",
                "普通客服支持"
            });
            
            return AjaxResult.success(benefits);
        } catch (Exception e) {
            logger.error("获取会员权益失败", e);
            return AjaxResult.error("获取会员权益失败");
        }
    }

    /**
     * 获取会员订单记录
     */
    @GetMapping("/orders")
    public AjaxResult getMembershipOrders()
    {
        try {
            LoginUser loginUser = getLoginUser();
            Long teacherId = loginUser.getUserId();
            
            TeacherMembership query = new TeacherMembership();
            query.setTeacherId(teacherId);
            
            return AjaxResult.success(teacherMembershipService.selectTeacherMembershipList(query));
        } catch (Exception e) {
            logger.error("获取会员订单记录失败", e);
            return AjaxResult.error("获取会员订单记录失败");
        }
    }

    /**
     * 创建会员支付订单
     */
    @PostMapping("/createPayOrder")
    @Log(title = "创建会员支付订单", businessType = BusinessType.INSERT)
    public AjaxResult createPaymentOrder(@RequestBody Map<String, Object> params)
    {
        try {
            LoginUser loginUser = getLoginUser();
            Long teacherId = loginUser.getUserId();

            // 获取VIP费用配置
            String vipFeeConfig = platformConfigService.selectConfigValueByKey("teacher.vip.fee", "0");
            BigDecimal fee = new BigDecimal(vipFeeConfig);

            if (fee.compareTo(BigDecimal.ZERO) <= 0) {
                return AjaxResult.error("VIP费用未配置，请联系管理员");
            }

            // 检查是否已经是VIP
            try {
                if (teacherMembershipService != null && teacherMembershipService.isValidVip(teacherId)) {
                    return AjaxResult.error("您已经是高级会员");
                }
            } catch (Exception e) {
                logger.warn("检查VIP状态失败，继续创建支付订单: " + e.getMessage());
            }

            // 获取教师信息和openid
            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoById(teacherId);
            if (teacherInfo == null) {
                return AjaxResult.error("教师信息不存在");
            }

            String openid = teacherInfo.getOpenid();
            if (openid == null || openid.trim().isEmpty()) {
                return AjaxResult.error("用户openid不存在，请重新登录");
            }

            // 转换金额为分
            int feeInCent = fee.multiply(new BigDecimal("100")).intValue();

            // 构建微信支付请求
            WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
            request.setBody("教师会员升级 - 高级会员费用");
            request.setOutTradeNo("VIP_FEE_" + System.currentTimeMillis()); // 生成唯一订单号
            request.setTotalFee(feeInCent); // 金额，单位为分
            request.setSpbillCreateIp("127.0.0.1"); // 终端IP

            // 设置回调URL
            String notifyUrl = "http://t8f63f47.natappfree.cc/applet/teacher/membership/notify";
            request.setNotifyUrl(notifyUrl);

            request.setTradeType("JSAPI");
            request.setOpenid(openid);

            WxPayMpOrderResult result = wxPayService.createOrder(request);
            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("创建会员支付订单失败", e);
            return AjaxResult.error("创建支付订单失败：" + e.getMessage());
        }
    }

    /**
     * 会员支付回调
     */
    @PostMapping("/notify")
    public String handleMembershipPayNotify(@RequestBody String xmlData) throws Exception {
        try {
            logger.info("=== 收到会员支付回调 ===");
            logger.info("回调数据: " + xmlData);

            final WxPayOrderNotifyResult notifyResult = this.wxPayService.parseOrderNotifyResult(xmlData);
            logger.info("解析后的回调结果: " + notifyResult);

            // 处理业务逻辑：升级会员
            String outTradeNo = notifyResult.getOutTradeNo();
            String openid = notifyResult.getOpenid();
            Integer totalFee = notifyResult.getTotalFee();

            logger.info("订单号: " + outTradeNo);
            logger.info("用户openid: " + openid);
            logger.info("支付金额: " + totalFee + "分");

            // 根据openid查找教师并升级会员
            TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoByOpenid(openid);
            if (teacherInfo != null) {
                logger.info("查找到教师信息，ID: " + teacherInfo.getId());

                // 获取VIP费用配置
                String vipFeeConfig = platformConfigService.selectConfigValueByKey("teacher.vip.fee", "0");
                BigDecimal paymentAmount = new BigDecimal(vipFeeConfig);

                // 创建会员记录
                TeacherMembership membership = new TeacherMembership();
                membership.setTeacherId(teacherInfo.getId());
                membership.setMembershipType("2"); // 高级会员
                membership.setMembershipDate(LocalDateTime.now());
                membership.setPaymentAmount(paymentAmount);
                membership.setPaymentStatus("1"); // 已支付
                membership.setPaymentTime(LocalDateTime.now());
                membership.setOrderNo(outTradeNo);
                membership.setCreateBy("system");
                membership.setCreateTime(new Date());

                int result = teacherMembershipService.insertTeacherMembership(membership);

                if (result > 0) {
                    // 更新教师信息表的会员状态
                    try {
                        teacherInfo.setMembershipType("2");
                        teacherInfo.setMembershipDate(new Date());
                        teacherInfo.setUpdateBy("system");
                        teacherInfo.setUpdateTime(new Date());
                        teacherInfoMapper.updateTeacherInfo(teacherInfo);

                        logger.info("教师会员升级成功，教师ID: {}, 订单号: {}", teacherInfo.getId(), outTradeNo);
                    } catch (Exception e) {
                        logger.warn("更新教师会员状态失败，但会员记录已创建: " + e.getMessage());
                    }
                } else {
                    logger.error("创建会员记录失败，教师ID: {}", teacherInfo.getId());
                }
            } else {
                logger.error("未找到对应的教师信息，openid: " + openid);
            }

            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        } catch (Exception e) {
            logger.error("处理会员支付回调失败", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>";
        }
    }
}

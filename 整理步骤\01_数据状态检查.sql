-- 第一步：确认当前数据库状态
-- 执行这个脚本来了解当前的数据情况

-- 1. 检查teacher_info表结构
SELECT '=== teacher_info表结构 ===' as info;
DESCRIBE teacher_info;

-- 2. 检查实际的认证状态分布
SELECT '=== 认证状态分布 ===' as info;
SELECT 
    certification_status,
    COUNT(*) as count,
    CASE 
        WHEN certification_status = '0' THEN '未认证'
        WHEN certification_status = '1' THEN '学生认证'
        WHEN certification_status = '2' THEN '已认证'
        WHEN certification_status = '4' THEN '已实名认证'
        ELSE CONCAT('未知状态:', certification_status)
    END as status_desc
FROM teacher_info 
GROUP BY certification_status
ORDER BY certification_status;

-- 3. 检查删除字段情况
SELECT '=== 删除字段检查 ===' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME IN ('deleted', 'del_flag');

-- 4. 检查当前可用的教师数据
SELECT '=== 可用教师数据 ===' as info;
SELECT 
    id,
    real_name,
    nick_name,
    certification_status,
    status,
    CASE WHEN deleted IS NOT NULL THEN deleted ELSE 'NULL' END as deleted_value,
    university,
    major
FROM teacher_info 
WHERE status = '0'
ORDER BY id;

-- 5. 检查parent_info表是否存在
SELECT '=== parent_info表检查 ===' as info;
SELECT COUNT(*) as parent_count FROM parent_info;

-- 6. 检查订单表状态
SELECT '=== 订单表检查 ===' as info;
SELECT COUNT(*) as order_count FROM tutor_order;

SELECT '=== 数据检查完成 ===' as result;

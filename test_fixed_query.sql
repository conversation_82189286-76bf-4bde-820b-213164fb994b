-- 测试修复后的推荐教师查询
-- 这个查询不依赖subject表，直接使用teacher_info表中的teaching_subjects字段

-- 1. 测试完整的推荐教师查询（与API相同）
SELECT
    t.id,
    t.nick_name as name,
    t.real_name,
    t.avatar,
    t.university,
    t.major,
    t.grade,
    t.certification_status,
    CASE 
        WHEN t.certification_status = '1' THEN '学生认证'
        WHEN t.certification_status = '2' THEN '实名认证'
        ELSE '未认证'
    END as certification_text,
    COALESCE(t.teaching_subjects, '暂未设置') as subject,
    '经验丰富' as experience,
    IFNULL(t.rating, 4.5) as rating,
    COALESCE(t.self_introduction, '该教师暂未填写自我介绍') as features,
    '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三' as grades
FROM teacher_info t
WHERE t.status = '0' AND t.certification_status IN ('1', '2')
ORDER BY t.certification_status DESC, t.rating DESC, t.create_time DESC
LIMIT 10;

-- 2. 如果上面查询没有结果，检查数据
SELECT COUNT(*) as total_teachers FROM teacher_info;

-- 3. 检查认证状态分布
SELECT 
    certification_status,
    CASE 
        WHEN certification_status = '0' THEN '未认证'
        WHEN certification_status = '1' THEN '学生认证'
        WHEN certification_status = '2' THEN '实名认证'
        ELSE '未知'
    END as status_text,
    COUNT(*) as count
FROM teacher_info 
GROUP BY certification_status;

-- 4. 检查状态分布
SELECT 
    status,
    CASE 
        WHEN status = '0' THEN '正常'
        WHEN status = '1' THEN '停用'
        ELSE '未知'
    END as status_text,
    COUNT(*) as count
FROM teacher_info 
GROUP BY status;

-- 5. 查看所有教师的基本信息
SELECT 
    id,
    nick_name,
    real_name,
    university,
    major,
    status,
    certification_status,
    teaching_subjects,
    self_introduction,
    create_time
FROM teacher_info 
ORDER BY create_time DESC
LIMIT 10;

-- 6. 如果需要，可以更新现有教师的状态使其符合条件
-- UPDATE teacher_info SET status = '0', certification_status = '1' WHERE id = 1;
-- UPDATE teacher_info SET status = '0', certification_status = '2' WHERE id = 2;

package com.ruoyi.education.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.education.mapper.TeacherInfoMapper;
import com.ruoyi.education.domain.TeacherInfo;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.service.ITeacherInfoService;
import com.ruoyi.education.domain.Subject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ruoyi.education.domain.EducationExperience;
import com.ruoyi.education.domain.TeachingExperience;
import com.ruoyi.education.service.IEducationExperienceService;
import com.ruoyi.education.service.ITeachingExperienceService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.List;

/**
 * 教师信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TeacherInfoServiceImpl implements ITeacherInfoService
{
    @Autowired
    private TeacherInfoMapper teacherInfoMapper;

    @Autowired
    private IEducationExperienceService educationExperienceService;

    @Autowired
    private ITeachingExperienceService teachingExperienceService;

    /**
     * 查询教师信息
     * 
     * @param id 教师信息主键
     * @return 教师信息
     */
    @Override
    public TeacherInfo selectTeacherInfoById(Long id)
    {
        return teacherInfoMapper.selectTeacherInfoById(id);
    }

    /**
     * 根据OpenID查询教师信息
     * 
     * @param openid 微信openid
     * @return 教师信息
     */
    @Override
    public TeacherInfo selectTeacherInfoByOpenid(String openid)
    {
        return teacherInfoMapper.selectTeacherInfoByOpenid(openid);
    }

    /**
     * 查询教师信息列表
     *
     * @param teacherInfo 教师信息
     * @return 教师信息
     */
    @Override
    public List<TeacherInfo> selectTeacherInfoList(TeacherInfo teacherInfo)
    {
        List<TeacherInfo> list = teacherInfoMapper.selectTeacherInfoList(teacherInfo);

        // 为每个教师加载关联数据
        for (TeacherInfo teacher : list) {
            // 加载教授科目
            List<Subject> subjectList = teacherInfoMapper.selectSubjectsByTeacherId(teacher.getId());
            if (subjectList != null && !subjectList.isEmpty()) {
                // 将科目名称拼接成字符串
                StringBuilder subjects = new StringBuilder();
                for (int i = 0; i < subjectList.size(); i++) {
                    if (i > 0) subjects.append(",");
                    subjects.append(subjectList.get(i).getName());
                }
                teacher.setTeachingSubjects(subjects.toString());
            }

            // 加载教育背景
            EducationExperience educationQuery = new EducationExperience();
            educationQuery.setTeacherId(teacher.getId());
            List<EducationExperience> educationList = educationExperienceService.selectEducationExperienceList(educationQuery);
            if (educationList != null && !educationList.isEmpty()) {
                // 将教育背景拼接成字符串，包含时间信息
                StringBuilder education = new StringBuilder();
                for (int i = 0; i < educationList.size(); i++) {
                    if (i > 0) education.append("; ");
                    EducationExperience edu = educationList.get(i);
                    education.append(edu.getSchool()).append("-").append(edu.getDegree()).append("-").append(edu.getMajor());
                    // 添加时间信息
                    if (edu.getStartDate() != null || edu.getEndDate() != null) {
                        education.append("-");
                        if (edu.getStartDate() != null) {
                            education.append(edu.getStartDate());
                        }
                        education.append("至");
                        if (edu.getEndDate() != null) {
                            education.append(edu.getEndDate());
                        }
                    }
                }
                teacher.setEducationBackground(education.toString());
            }

            // 加载经验背景
            TeachingExperience experienceQuery = new TeachingExperience();
            experienceQuery.setTeacherId(teacher.getId());
            List<TeachingExperience> experienceList = teachingExperienceService.selectTeachingExperienceList(experienceQuery);
            if (experienceList != null && !experienceList.isEmpty()) {
                // 将经验背景拼接成字符串，包含时间和详细内容
                StringBuilder experience = new StringBuilder();
                for (int i = 0; i < experienceList.size(); i++) {
                    if (i > 0) experience.append("; ");
                    TeachingExperience exp = experienceList.get(i);
                    experience.append(exp.getTeachingTarget());
                    // 添加时间信息
                    if (exp.getStartDate() != null || exp.getEndDate() != null) {
                        experience.append("-");
                        if (exp.getStartDate() != null) {
                            experience.append(exp.getStartDate());
                        }
                        experience.append("至");
                        if (exp.getEndDate() != null) {
                            experience.append(exp.getEndDate());
                        }
                    }
                    // 添加详细内容
                    if (exp.getContent() != null && !exp.getContent().trim().isEmpty()) {
                        experience.append("-").append(exp.getContent());
                    }
                }
                teacher.setExperienceBackground(experience.toString());
            }
        }

        return list;
    }

    /**
     * 新增教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    @Override
    public int insertTeacherInfo(TeacherInfo teacherInfo)
    {
        teacherInfo.setCreateTime(DateUtils.getNowDate());
        calculateAndSetAge(teacherInfo); // 在插入前计算年龄
        return teacherInfoMapper.insertTeacherInfo(teacherInfo);
    }

    /**
     * 修改教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    @Override
    public int updateTeacherInfo(TeacherInfo teacherInfo)
    {
        teacherInfo.setUpdateTime(DateUtils.getNowDate());
        calculateAndSetAge(teacherInfo); // 在更新前计算年龄
        return teacherInfoMapper.updateTeacherInfo(teacherInfo);
    }

    @Override
    public List<Subject> selectSubjectsByTeacherId(Long teacherId) {
        // 这个方法需要一个新的mapper方法来实现
        return teacherInfoMapper.selectSubjectsByTeacherId(teacherId);
    }

    @Override
    @Transactional
    public int updateTeacherSubjects(Long teacherId, List<Long> subjectIds) {
        // 1. 删除该教师已有的所有科目关联
        teacherInfoMapper.deleteTeacherSubjectByTeacherId(teacherId);

        if (subjectIds == null || subjectIds.isEmpty()) {
            return 1; // 如果新的科目列表为空，则删除后直接返回
        }
        
        // 2. 批量插入新的科目关联
        return teacherInfoMapper.batchInsertTeacherSubject(teacherId, subjectIds);
    }


    /**
     * 批量删除教师信息
     * 
     * @param ids 需要删除的教师信息主键
     * @return 结果
     */
    @Override
    public int deleteTeacherInfoByIds(Long[] ids)
    {
        return teacherInfoMapper.deleteTeacherInfoByIds(ids);
    }

    /**
     * 删除教师信息信息
     * 
     * @param id 教师信息主键
     * @return 结果
     */
    @Override
    public int deleteTeacherInfoById(Long id)
    {
        return teacherInfoMapper.deleteTeacherInfoById(id);
    }

    /**
     * 根据手机号查询教师信息
     * 
     * @param phoneNumber 手机号
     * @return 教师信息
     */
    @Override
    public TeacherInfo selectTeacherInfoByPhoneNumber(String phoneNumber)
    {
        return teacherInfoMapper.selectTeacherInfoByPhoneNumber(phoneNumber);
    }

    /**
     * 批量拉黑教师
     * 
     * @param ids 教师ID集合
     * @param blacklistReason 拉黑原因
     * @return 结果
     */
    @Override
    public int blacklistTeacherByIds(Long[] ids, String blacklistReason)
    {
        String updateBy = SecurityUtils.getUsername();
        return teacherInfoMapper.blacklistTeacherByIds(ids, blacklistReason, updateBy);
    }

    /**
     * 批量解除拉黑
     * 
     * @param ids 教师ID集合
     * @return 结果
     */
    @Override
    public int unblacklistTeacherByIds(Long[] ids)
    {
        String updateBy = SecurityUtils.getUsername();
        return teacherInfoMapper.unblacklistTeacherByIds(ids, updateBy);
    }

    /**
     * 更新教师接课成功统计和经历
     */
    @Override
    public int updateTeacherSuccessStats(Long teacherId, TutorOrder order) {
        try {
            // 1. 获取当前教师信息
            TeacherInfo teacher = teacherInfoMapper.selectTeacherInfoById(teacherId);
            if (teacher == null) {
                return 0;
            }

            // 2. 更新统计数据
            Integer currentSuccessOrders = teacher.getSuccessOrders() != null ? teacher.getSuccessOrders() : 0;
            teacher.setSuccessOrders(currentSuccessOrders + 1);

            // 3. 构建新的成功经历记录
            ObjectMapper objectMapper = new ObjectMapper();
            java.util.Map<String, Object> newExperience = new java.util.HashMap<>();
            newExperience.put("orderId", order.getId());
            newExperience.put("orderNo", order.getOrderNo());
            newExperience.put("subjectName", order.getSubjectName());
            newExperience.put("grade", order.getGrade());
            newExperience.put("completedTime", DateUtils.getTime());
            newExperience.put("duration", "已完成");
            newExperience.put("improvement", "教学任务圆满完成");
            newExperience.put("feedback", "家长满意");
            newExperience.put("rating", 5.0);

            // 4. 更新成功经历JSON
            java.util.List<java.util.Map<String, Object>> experienceList = new java.util.ArrayList<>();

            // 解析现有经历
            if (teacher.getSuccessExperience() != null && !teacher.getSuccessExperience().trim().isEmpty()) {
                try {
                    experienceList = objectMapper.readValue(teacher.getSuccessExperience(),
                        new TypeReference<java.util.List<java.util.Map<String, Object>>>() {});
                } catch (Exception e) {
                    // 如果解析失败，使用空列表
                    experienceList = new java.util.ArrayList<>();
                }
            }

            // 添加新经历到列表开头
            experienceList.add(0, newExperience);

            // 限制最多保存10条经历
            if (experienceList.size() > 10) {
                experienceList = experienceList.subList(0, 10);
            }

            // 转换为JSON字符串
            String updatedExperience = objectMapper.writeValueAsString(experienceList);
            teacher.setSuccessExperience(updatedExperience);

            // 5. 更新教师信息
            teacher.setUpdateTime(DateUtils.getNowDate());
            teacher.setUpdateBy(SecurityUtils.getUsername());

            return teacherInfoMapper.updateTeacherInfo(teacher);

        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 如果生日不为空，则根据生日计算并设置年龄
     * @param teacherInfo 教师信息对象
     */
    private void calculateAndSetAge(TeacherInfo teacherInfo) {
        if (teacherInfo != null && teacherInfo.getBirthday() != null) {
            LocalDate birthDate = teacherInfo.getBirthday().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            int age = Period.between(birthDate, currentDate).getYears();
            teacherInfo.setAge(age);
        }
    }
}
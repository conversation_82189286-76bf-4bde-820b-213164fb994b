<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherBankInfoMapper">
    
    <resultMap type="TeacherBankInfo" id="TeacherBankInfoResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="accountHolder"    column="account_holder"    />
        <result property="bankBranch"    column="bank_branch"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTeacherBankInfoVo">
        select id, teacher_id, teacher_name, bank_name, bank_account, account_holder, bank_branch, status, del_flag, create_by, create_time, update_by, update_time, remark from teacher_bank_info
    </sql>

    <select id="selectTeacherBankInfoList" parameterType="TeacherBankInfo" resultMap="TeacherBankInfoResult">
        <include refid="selectTeacherBankInfoVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="bankAccount != null  and bankAccount != ''"> and bank_account = #{bankAccount}</if>
            <if test="accountHolder != null  and accountHolder != ''"> and account_holder like concat('%', #{accountHolder}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectTeacherBankInfoById" parameterType="Long" resultMap="TeacherBankInfoResult">
        <include refid="selectTeacherBankInfoVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectTeacherBankInfoByTeacherId" parameterType="Long" resultMap="TeacherBankInfoResult">
        <include refid="selectTeacherBankInfoVo"/>
        where teacher_id = #{teacherId} and del_flag = '0'
        limit 1
    </select>
        
    <insert id="insertTeacherBankInfo" parameterType="TeacherBankInfo" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_bank_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="accountHolder != null">account_holder,</if>
            <if test="bankBranch != null">bank_branch,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="accountHolder != null">#{accountHolder},</if>
            <if test="bankBranch != null">#{bankBranch},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTeacherBankInfo" parameterType="TeacherBankInfo">
        update teacher_bank_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="accountHolder != null">account_holder = #{accountHolder},</if>
            <if test="bankBranch != null">bank_branch = #{bankBranch},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherBankInfoById" parameterType="Long">
        update teacher_bank_info set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteTeacherBankInfoByIds" parameterType="String">
        update teacher_bank_info set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>

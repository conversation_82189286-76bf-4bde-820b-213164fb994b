<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherIncomeMapper">
    
    <resultMap type="TeacherIncome" id="TeacherIncomeResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="incomeType"    column="income_type"    />
        <result property="incomeAmount"    column="income_amount"    />
        <result property="platformFee"    column="platform_fee"    />
        <result property="actualAmount"    column="actual_amount"    />
        <result property="incomeDate"    column="income_date"    />
        <result property="incomeStatus"    column="income_status"    />
        <result property="settlementStatus"    column="settlement_status"    />
        <result property="settlementDate"    column="settlement_date"    />
        <result property="withdrawableTime"    column="withdrawable_time"    />
        <result property="remarks"    column="remarks"    />
        <result property="violationFlag"    column="violation_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTeacherIncomeVo">
        select id, teacher_id, teacher_name, order_id, order_no, income_type, income_amount, platform_fee, actual_amount, income_date, income_status, settlement_status, settlement_date, withdrawable_time, remarks, violation_flag, create_by, create_time, update_by, update_time from teacher_income
    </sql>

    <select id="selectTeacherIncomeList" parameterType="TeacherIncome" resultMap="TeacherIncomeResult">
        <include refid="selectTeacherIncomeVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="incomeType != null  and incomeType != ''"> and income_type = #{incomeType}</if>
            <if test="incomeStatus != null  and incomeStatus != ''"> and income_status = #{incomeStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTeacherIncomeById" parameterType="Long" resultMap="TeacherIncomeResult">
        <include refid="selectTeacherIncomeVo"/>
        where id = #{id}
    </select>

    <select id="selectTeacherIncomeByTeacherId" parameterType="Long" resultMap="TeacherIncomeResult">
        <include refid="selectTeacherIncomeVo"/>
        where teacher_id = #{teacherId}
        order by create_time desc
    </select>

    <select id="selectTeacherIncomeByOrderId" parameterType="Long" resultMap="TeacherIncomeResult">
        <include refid="selectTeacherIncomeVo"/>
        where order_id = #{orderId}
        order by create_time desc
    </select>

    <select id="selectAvailableAmountByTeacherId" parameterType="Long" resultType="java.math.BigDecimal">
        select COALESCE(sum(actual_amount), 0) from teacher_income
        where teacher_id = #{teacherId}
        and income_status = 'settled'
    </select>

    <select id="selectTotalIncomeByTeacherId" parameterType="Long" resultType="java.math.BigDecimal">
        select COALESCE(sum(income_amount), 0) from teacher_income
        where teacher_id = #{teacherId}
        and income_status != 'cancelled'
    </select>

    <select id="selectIncomeByTypeAndTeacherId" resultType="java.math.BigDecimal">
        select COALESCE(sum(income_amount), 0) from teacher_income
        where teacher_id = #{teacherId}
        and income_type = #{incomeType}
        and income_status != 'cancelled'
    </select>

    <select id="selectWithdrawnAmountByTeacherId" parameterType="Long" resultType="java.math.BigDecimal">
        select COALESCE(sum(apply_amount), 0) from withdrawal_application
        where teacher_id = #{teacherId}
        and apply_status = '3'
    </select>

    <select id="selectPendingAmountByTeacherId" parameterType="Long" resultType="java.math.BigDecimal">
        select COALESCE(sum(income_amount), 0) from teacher_income
        where teacher_id = #{teacherId}
        and income_status = 'pending'
    </select>
        
    <insert id="insertTeacherIncome" parameterType="TeacherIncome" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="incomeAmount != null">income_amount,</if>
            <if test="platformFee != null">platform_fee,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="incomeDate != null">income_date,</if>
            <if test="incomeStatus != null">income_status,</if>
            <if test="settlementDate != null">settlement_date,</if>
            <if test="withdrawableTime != null">withdrawable_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="incomeAmount != null">#{incomeAmount},</if>
            <if test="platformFee != null">#{platformFee},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="incomeDate != null">#{incomeDate},</if>
            <if test="incomeStatus != null">#{incomeStatus},</if>
            <if test="settlementDate != null">#{settlementDate},</if>
            <if test="withdrawableTime != null">#{withdrawableTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTeacherIncome" parameterType="TeacherIncome">
        update teacher_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="incomeAmount != null">income_amount = #{incomeAmount},</if>
            <if test="platformFee != null">platform_fee = #{platformFee},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="incomeDate != null">income_date = #{incomeDate},</if>
            <if test="incomeStatus != null">income_status = #{incomeStatus},</if>
            <if test="settlementDate != null">settlement_date = #{settlementDate},</if>
            <if test="withdrawableTime != null">withdrawable_time = #{withdrawableTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherIncomeById" parameterType="Long">
        delete from teacher_income where id = #{id}
    </delete>

    <delete id="deleteTeacherIncomeByIds" parameterType="String">
        delete from teacher_income where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>

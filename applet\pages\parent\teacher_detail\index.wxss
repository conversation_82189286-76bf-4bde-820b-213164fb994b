/* 教师详情页面样式 */
.teacher-detail-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 教师信息卡片 */
.teacher-info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.teacher-header {
  display: flex;
  align-items: flex-start;
}

.teacher-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  border: 4rpx solid #f0f0f0;
}

.teacher-basic {
  flex: 1;
}

.teacher-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.teacher-meta {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  margin-bottom: 8rpx;
}

.certification-badge {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  margin-left: 8rpx;
}

.certification-badge.verified {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.certification-badge.normal {
  background: #52c41a;
}

.teacher-rating {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 12rpx;
}

.star {
  font-size: 28rpx;
  color: #ddd;
  margin-right: 4rpx;
}

.star.filled {
  color: #ffa940;
}

.rating-text {
  font-size: 28rpx;
  color: #666;
}

/* 信息区块 */
.info-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.section-content {
  line-height: 1.6;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 统计区块 */
.stats-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 操作区块 */
.action-section {
  padding: 40rpx 0;
}

.book-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.book-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 教师详情页面 - 简单高级样式 */
.teacher-detail-page {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

.page-content {
  height: calc(100vh - 200rpx - env(safe-area-inset-bottom) - var(--status-bar-height, 44px));
  background-color: #f8f9fa;
}

/* 教师档案卡片 */
.teacher-profile-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
}

.avatar-section {
  position: relative;
  margin-right: 32rpx;
}

.teacher-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  border: 6rpx solid #f8fafc;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.certification-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  color: white;
  font-weight: 600;
  border: 3rpx solid white;
}

.certification-badge.verified {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.certification-badge.normal {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.basic-info {
  flex: 1;
}

.teacher-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.teacher-meta {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.meta-tag {
  background: #f7fafc;
  color: #4a5568;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.rating-section {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 12rpx;
}

.star {
  font-size: 32rpx;
  color: #e2e8f0;
  margin-right: 4rpx;
}

.star.filled {
  color: #fbbf24;
}

.rating-text {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 600;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 600;
  line-height: 1.4;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 内容卡片 */
.content-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.card-content {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.8;
  text-align: justify;
}

/* 操作区块 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx;
  border-top: 1rpx solid #e2e8f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.book-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
}

.book-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.book-btn:active::before {
  left: 100%;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  letter-spacing: 2rpx;
}

.book-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

/* 接课成功经历样式 */
.experience-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.experience-item {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  border-left: 6rpx solid #667eea;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.subject-grade {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
}

.completed-time {
  font-size: 24rpx;
  color: #718096;
}

.experience-content {
  margin-bottom: 16rpx;
}

.improvement {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
  line-height: 1.5;
}

.feedback {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}

.experience-rating {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.experience-rating .stars {
  display: flex;
  margin-right: 8rpx;
}

.experience-rating .star {
  font-size: 24rpx;
  color: #e2e8f0;
  margin-right: 2rpx;
}

.experience-rating .star.filled {
  color: #fbbf24;
}

.experience-rating .rating-text {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 600;
}

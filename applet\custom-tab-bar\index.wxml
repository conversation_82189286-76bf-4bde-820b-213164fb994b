<!--custom-tab-bar/index.wxml-->
<view class="tab-bar">
  <view
    wx:for="{{userType === 'teacher' ? teacherList : parentList}}"
    wx:key="index"
    class="tab-bar-item {{selected === index ? 'active' : ''}}"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <image
      class="tab-bar-icon"
      src="{{selected === index ? item.selectedIconPath : item.iconPath}}"
    />
    <text class="tab-bar-text">{{item.text}}</text>
  </view>
</view>

{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\babel-loader@8.4.1_@babel+core@7.27.7_webpack@4.47.0\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\education\\contentManagement\\components\\BannerManagement.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\education\\contentManagement\\components\\BannerManagement.vue", "mtime": 1753722814422}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1746002959099}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\babel-loader@8.4.1_@babel+core@7.27.7_webpack@4.47.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1746002951712}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1746002959099}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1746002957679}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750315669000}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1746002959099}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\babel-loader@8.4.1_@babel+core@7.27.7_webpack@4.47.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1746002951712}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1746002959099}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1746002957679}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "name", "props", "banners", "type", "Array", "default", "tabName", "String", "data", "localBanners", "dialogVisible", "imagePreviewVisible", "dialogTitle", "currentBanner", "title", "image", "linkType", "linkUrl", "sort", "uploadAction", "process", "env", "VUE_APP_BASE_API", "editingIndex", "bannerRules", "required", "message", "trigger", "min", "max", "computed", "uploadHeaders", "Authorization", "getToken", "watch", "immediate", "handler", "newVal", "JSON", "parse", "stringify", "methods", "addBanner", "length", "editBanner", "index", "deleteBanner", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "splice", "updateSort", "$message", "success", "catch", "moveBanner", "direction", "temp", "$set", "$forceUpdate", "for<PERSON>ach", "banner", "submitBanner", "_this2", "$refs", "bannerForm", "validate", "valid", "push", "resetBanners", "_this3", "saveBanners", "warning", "$emit", "previewImage", "handleImageError", "e", "target", "src", "beforeUpload", "file", "console", "log", "action", "headers", "isImage", "indexOf", "isLt2M", "size", "error", "info", "handleUploadSuccess", "response", "code", "url", "msg", "handleUploadError", "err"], "sources": ["src/views/education/contentManagement/components/BannerManagement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"banner-management\">\r\n    <div class=\"banner-header\">\r\n      <span>{{ tabName }}轮播图配置</span>\r\n      <el-button type=\"primary\" size=\"small\" @click=\"addBanner\">\r\n        <i class=\"el-icon-plus\"></i> 添加轮播图\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 轮播图列表 -->\r\n    <div class=\"banner-list\" v-if=\"localBanners.length > 0\">\r\n      <el-row :gutter=\"16\">\r\n        <el-col :span=\"8\" v-for=\"(banner, index) in localBanners\" :key=\"index\">\r\n          <el-card class=\"banner-item\" shadow=\"hover\">\r\n            <div class=\"banner-preview\">\r\n              <img :src=\"banner.image\" :alt=\"banner.title\" @error=\"handleImageError\" />\r\n            </div>\r\n            <div class=\"banner-info\">\r\n              <div class=\"banner-title\">{{ banner.title }}</div>\r\n              <div class=\"banner-sort\">排序: {{ banner.sort }}</div>\r\n              <div class=\"banner-actions\">\r\n                <el-button type=\"text\" size=\"small\" @click=\"editBanner(index)\">\r\n                  <i class=\"el-icon-edit\"></i> 编辑\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"small\" @click=\"deleteBanner(index)\" style=\"color: #f56c6c;\">\r\n                  <i class=\"el-icon-delete\"></i> 删除\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"small\" @click=\"moveBanner(index, 'up')\" :disabled=\"index === 0\">\r\n                  <i class=\"el-icon-top\"></i> 上移\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"small\" @click=\"moveBanner(index, 'down')\" :disabled=\"index === localBanners.length - 1\">\r\n                  <i class=\"el-icon-bottom\"></i> 下移\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 空状态 -->\r\n    <div v-else class=\"empty-state\">\r\n      <i class=\"el-icon-picture-outline\"></i>\r\n      <p>暂无轮播图配置</p>\r\n      <el-button type=\"primary\" @click=\"addBanner\">添加第一张轮播图</el-button>\r\n    </div>\r\n\r\n    <!-- 保存按钮 -->\r\n    <div class=\"save-actions\" v-if=\"localBanners.length > 0\">\r\n      <el-button type=\"primary\" @click=\"saveBanners\">保存轮播图配置</el-button>\r\n      <el-button @click=\"resetBanners\">重置</el-button>\r\n    </div>\r\n\r\n    <!-- 编辑轮播图对话框 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"bannerForm\" :model=\"currentBanner\" :rules=\"bannerRules\" label-width=\"100px\">\r\n        <el-form-item label=\"轮播图标题\" prop=\"title\">\r\n          <el-input v-model=\"currentBanner.title\" placeholder=\"请输入轮播图标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"图片上传\" prop=\"image\">\r\n          <div class=\"upload-container\">\r\n            <el-upload\r\n              class=\"banner-uploader\"\r\n              :action=\"uploadAction\"\r\n              :headers=\"uploadHeaders\"\r\n              :show-file-list=\"false\"\r\n              :on-success=\"handleUploadSuccess\"\r\n              :on-error=\"handleUploadError\"\r\n              :before-upload=\"beforeUpload\"\r\n              accept=\"image/*\"\r\n              :disabled=\"false\"\r\n            >\r\n              <div class=\"upload-content\">\r\n                <img v-if=\"currentBanner.image\" :src=\"currentBanner.image\" class=\"banner-image\" />\r\n                <div v-else class=\"upload-placeholder\">\r\n                  <i class=\"el-icon-plus banner-uploader-icon\"></i>\r\n                  <div class=\"upload-text\">点击上传图片</div>\r\n                </div>\r\n              </div>\r\n            </el-upload>\r\n            <div class=\"upload-tips\">\r\n              <p>建议图片尺寸：750x320 像素，格式：jpg、png，大小不超过2MB</p>\r\n              <el-input\r\n                v-model=\"currentBanner.image\"\r\n                placeholder=\"或直接输入图片URL地址\"\r\n                style=\"margin-top: 8px;\"\r\n              >\r\n                <template slot=\"append\">\r\n                  <el-button @click=\"previewImage\">预览</el-button>\r\n                </template>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"链接类型\" prop=\"linkType\">\r\n          <el-radio-group v-model=\"currentBanner.linkType\">\r\n            <el-radio label=\"none\">无链接</el-radio>\r\n            <el-radio label=\"page\">页面跳转</el-radio>\r\n            <el-radio label=\"web\">外部链接</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"currentBanner.linkType !== 'none'\"\r\n          label=\"链接地址\"\r\n          prop=\"linkUrl\"\r\n        >\r\n          <el-input\r\n            v-model=\"currentBanner.linkUrl\"\r\n            :placeholder=\"currentBanner.linkType === 'page' ? '请输入页面路径，如：/pages/teacher/certification/index' : '请输入完整的URL地址'\"\r\n          />\r\n          <div class=\"form-tips\">\r\n            {{ currentBanner.linkType === 'page' ? '小程序内页面路径' : '外部网页链接地址' }}\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序权重\" prop=\"sort\">\r\n          <el-input-number v-model=\"currentBanner.sort\" :min=\"1\" :max=\"99\" controls-position=\"right\" />\r\n          <div class=\"form-tips\">数字越小越靠前显示</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitBanner\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog title=\"图片预览\" :visible.sync=\"imagePreviewVisible\" width=\"400px\" append-to-body>\r\n      <div class=\"image-preview\">\r\n        <img :src=\"currentBanner.image\" alt=\"预览图片\" @error=\"handleImageError\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: 'BannerManagement',\r\n  props: {\r\n    banners: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    tabName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localBanners: [],\r\n      dialogVisible: false,\r\n      imagePreviewVisible: false,\r\n      dialogTitle: '',\r\n      currentBanner: {\r\n        title: '',\r\n        image: '',\r\n        linkType: 'none',\r\n        linkUrl: '',\r\n        sort: 1\r\n      },\r\n      // 上传配置\r\n      uploadAction: process.env.VUE_APP_BASE_API + \"/common/uploadSftp\",\r\n      editingIndex: -1,\r\n      bannerRules: {\r\n        title: [\r\n          { required: true, message: '请输入轮播图标题', trigger: 'blur' },\r\n          { min: 1, max: 50, message: '标题长度在 1 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        image: [\r\n          { required: true, message: '请上传图片或输入图片地址', trigger: 'blur' }\r\n        ],\r\n        linkUrl: [\r\n          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }\r\n        ],\r\n        sort: [\r\n          { required: true, message: '请输入排序权重', trigger: 'blur' },\r\n          { type: 'number', min: 1, max: 99, message: '排序权重在 1 到 99 之间', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    uploadHeaders() {\r\n      return {\r\n        Authorization: \"Bearer \" + this.getToken()\r\n      };\r\n    }\r\n  },\r\n  watch: {\r\n    banners: {\r\n      immediate: true,\r\n      handler(newVal) {\r\n        this.localBanners = JSON.parse(JSON.stringify(newVal || []));\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /** 添加轮播图 */\r\n    addBanner() {\r\n      this.dialogTitle = '添加轮播图';\r\n      this.currentBanner = {\r\n        title: '',\r\n        image: '',\r\n        linkType: 'none',\r\n        linkUrl: '',\r\n        sort: this.localBanners.length + 1\r\n      };\r\n      this.editingIndex = -1;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    /** 编辑轮播图 */\r\n    editBanner(index) {\r\n      this.dialogTitle = '编辑轮播图';\r\n      this.currentBanner = JSON.parse(JSON.stringify(this.localBanners[index]));\r\n      this.editingIndex = index;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    /** 删除轮播图 */\r\n    deleteBanner(index) {\r\n      this.$confirm('确认删除这张轮播图吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.localBanners.splice(index, 1);\r\n        this.updateSort();\r\n        this.$message.success('删除成功');\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 移动轮播图 */\r\n    moveBanner(index, direction) {\r\n      if (direction === 'up' && index > 0) {\r\n        // 使用Vue.set确保响应式更新\r\n        const temp = this.localBanners[index];\r\n        this.$set(this.localBanners, index, this.localBanners[index - 1]);\r\n        this.$set(this.localBanners, index - 1, temp);\r\n        this.$message.success('上移成功');\r\n      } else if (direction === 'down' && index < this.localBanners.length - 1) {\r\n        // 使用Vue.set确保响应式更新\r\n        const temp = this.localBanners[index];\r\n        this.$set(this.localBanners, index, this.localBanners[index + 1]);\r\n        this.$set(this.localBanners, index + 1, temp);\r\n        this.$message.success('下移成功');\r\n      }\r\n      this.updateSort();\r\n      // 强制更新视图\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    /** 更新排序 */\r\n    updateSort() {\r\n      this.localBanners.forEach((banner, index) => {\r\n        banner.sort = index + 1;\r\n      });\r\n    },\r\n\r\n    /** 提交轮播图 */\r\n    submitBanner() {\r\n      this.$refs.bannerForm.validate((valid) => {\r\n        if (valid) {\r\n          if (this.editingIndex >= 0) {\r\n            // 编辑模式\r\n            this.$set(this.localBanners, this.editingIndex, JSON.parse(JSON.stringify(this.currentBanner)));\r\n          } else {\r\n            // 新增模式\r\n            this.localBanners.push(JSON.parse(JSON.stringify(this.currentBanner)));\r\n          }\r\n          this.updateSort();\r\n          this.dialogVisible = false;\r\n          this.$message.success(this.editingIndex >= 0 ? '编辑成功' : '添加成功');\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 重置轮播图 */\r\n    resetBanners() {\r\n      this.$confirm('确认重置轮播图配置吗？所有未保存的修改将丢失。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.localBanners = JSON.parse(JSON.stringify(this.banners || []));\r\n        this.$message.success('重置成功');\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 保存轮播图 */\r\n    saveBanners() {\r\n      if (this.localBanners.length === 0) {\r\n        this.$message.warning('请至少添加一张轮播图');\r\n        return;\r\n      }\r\n      this.$emit('save', JSON.parse(JSON.stringify(this.localBanners)));\r\n    },\r\n\r\n    /** 预览图片 */\r\n    previewImage() {\r\n      if (!this.currentBanner.image) {\r\n        this.$message.warning('请先输入图片地址');\r\n        return;\r\n      }\r\n      this.imagePreviewVisible = true;\r\n    },\r\n\r\n    /** 图片加载错误处理 */\r\n    handleImageError(e) {\r\n      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkFGQUZBIi8+CjxwYXRoIGQ9Ik0yMCAzMEM5IDE5IDkgOSAyMCA5QzMxIDkgMzEgMTkgMjAgMzBaIiBmaWxsPSIjRTZFNkU2Ii8+Cjwvc3ZnPgo=';\r\n    },\r\n\r\n    /** 获取token */\r\n    getToken() {\r\n      return getToken();\r\n    },\r\n\r\n    /** 上传前检查 */\r\n    beforeUpload(file) {\r\n      console.log('开始上传文件:', file);\r\n      console.log('上传配置:', {\r\n        action: this.uploadAction,\r\n        headers: this.uploadHeaders\r\n      });\r\n\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt2M = file.size / 1024 / 1024 < 2;\r\n\r\n      if (!isImage) {\r\n        this.$message.error('只能上传图片文件!');\r\n        return false;\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 2MB!');\r\n        return false;\r\n      }\r\n\r\n      this.$message.info('正在上传图片，请稍候...');\r\n      return true;\r\n    },\r\n\r\n    /** 上传成功回调 */\r\n    handleUploadSuccess(response, file) {\r\n      console.log('SFTP上传成功响应:', response);\r\n      console.log('上传的文件:', file);\r\n\r\n      if (response.code === 200) {\r\n        // SFTP上传返回的是完整的URL\r\n        this.currentBanner.image = response.url;\r\n        this.$message.success('图片上传成功');\r\n      } else {\r\n        this.$message.error('图片上传失败：' + (response.msg || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 上传失败回调 */\r\n    handleUploadError(err, file) {\r\n      console.error('上传失败:', err);\r\n      console.log('失败的文件:', file);\r\n      this.$message.error('图片上传失败，请重试');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.banner-management {\r\n  padding: 16px 0;\r\n}\r\n\r\n.banner-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.banner-list {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.banner-item {\r\n  margin-bottom: 16px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.banner-preview {\r\n  height: 120px;\r\n  overflow: hidden;\r\n  background: #f5f5f5;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.banner-preview img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.banner-info {\r\n  padding: 12px;\r\n}\r\n\r\n.banner-title {\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.banner-sort {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.banner-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.banner-actions .el-button {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #999;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-state p {\r\n  margin-bottom: 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.save-actions {\r\n  text-align: center;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.form-tips {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-preview {\r\n  text-align: center;\r\n}\r\n\r\n.image-preview img {\r\n  max-width: 100%;\r\n  max-height: 300px;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.el-card__body) {\r\n  padding: 0;\r\n}\r\n\r\n:deep(.el-form-item) {\r\n  margin-bottom: 18px;\r\n}\r\n\r\n/* 上传组件样式 */\r\n.upload-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.banner-uploader :deep(.el-upload) {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  width: 200px;\r\n  height: 100px;\r\n  display: block;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.banner-uploader :deep(.el-upload:hover) {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.banner-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 12px;\r\n  color: #8c939d;\r\n}\r\n\r\n.banner-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.upload-tips {\r\n  margin-top: 8px;\r\n}\r\n\r\n.upload-tips p {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin: 0 0 8px 0;\r\n  line-height: 1.4;\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;AAuIA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,aAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;MACA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,YAAA;MACAC,WAAA;QACAV,KAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,KAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,OAAA,GACA;UAAAd,IAAA;UAAAuB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,IAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAxB,IAAA;UAAAyB,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA;QACAC,aAAA,mBAAAC,QAAA;MACA;IACA;EACA;EACAC,KAAA;IACAhC,OAAA;MACAiC,SAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAA5B,YAAA,GAAA6B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,MAAA;MACA;IACA;EACA;EACAI,OAAA;IACA,YACAC,SAAA,WAAAA,UAAA;MACA,KAAA9B,WAAA;MACA,KAAAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,OAAA;QACAC,IAAA,OAAAT,YAAA,CAAAkC,MAAA;MACA;MACA,KAAApB,YAAA;MACA,KAAAb,aAAA;IACA;IAEA,YACAkC,UAAA,WAAAA,WAAAC,KAAA;MACA,KAAAjC,WAAA;MACA,KAAAC,aAAA,GAAAyB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA/B,YAAA,CAAAoC,KAAA;MACA,KAAAtB,YAAA,GAAAsB,KAAA;MACA,KAAAnC,aAAA;IACA;IAEA,YACAoC,YAAA,WAAAA,aAAAD,KAAA;MAAA,IAAAE,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/C,IAAA;MACA,GAAAgD,IAAA;QACAJ,KAAA,CAAAtC,YAAA,CAAA2C,MAAA,CAAAP,KAAA;QACAE,KAAA,CAAAM,UAAA;QACAN,KAAA,CAAAO,QAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;IACA;IAEA,YACAC,UAAA,WAAAA,WAAAZ,KAAA,EAAAa,SAAA;MACA,IAAAA,SAAA,aAAAb,KAAA;QACA;QACA,IAAAc,IAAA,QAAAlD,YAAA,CAAAoC,KAAA;QACA,KAAAe,IAAA,MAAAnD,YAAA,EAAAoC,KAAA,OAAApC,YAAA,CAAAoC,KAAA;QACA,KAAAe,IAAA,MAAAnD,YAAA,EAAAoC,KAAA,MAAAc,IAAA;QACA,KAAAL,QAAA,CAAAC,OAAA;MACA,WAAAG,SAAA,eAAAb,KAAA,QAAApC,YAAA,CAAAkC,MAAA;QACA;QACA,IAAAgB,KAAA,QAAAlD,YAAA,CAAAoC,KAAA;QACA,KAAAe,IAAA,MAAAnD,YAAA,EAAAoC,KAAA,OAAApC,YAAA,CAAAoC,KAAA;QACA,KAAAe,IAAA,MAAAnD,YAAA,EAAAoC,KAAA,MAAAc,KAAA;QACA,KAAAL,QAAA,CAAAC,OAAA;MACA;MACA,KAAAF,UAAA;MACA;MACA,KAAAQ,YAAA;IACA;IAEA,WACAR,UAAA,WAAAA,WAAA;MACA,KAAA5C,YAAA,CAAAqD,OAAA,WAAAC,MAAA,EAAAlB,KAAA;QACAkB,MAAA,CAAA7C,IAAA,GAAA2B,KAAA;MACA;IACA;IAEA,YACAmB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,UAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAJ,MAAA,CAAA1C,YAAA;YACA;YACA0C,MAAA,CAAAL,IAAA,CAAAK,MAAA,CAAAxD,YAAA,EAAAwD,MAAA,CAAA1C,YAAA,EAAAe,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAyB,MAAA,CAAApD,aAAA;UACA;YACA;YACAoD,MAAA,CAAAxD,YAAA,CAAA6D,IAAA,CAAAhC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAyB,MAAA,CAAApD,aAAA;UACA;UACAoD,MAAA,CAAAZ,UAAA;UACAY,MAAA,CAAAvD,aAAA;UACAuD,MAAA,CAAAX,QAAA,CAAAC,OAAA,CAAAU,MAAA,CAAA1C,YAAA;QACA;MACA;IACA;IAEA,YACAgD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/C,IAAA;MACA,GAAAgD,IAAA;QACAqB,MAAA,CAAA/D,YAAA,GAAA6B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAgC,MAAA,CAAAtE,OAAA;QACAsE,MAAA,CAAAlB,QAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;IACA;IAEA,YACAiB,WAAA,WAAAA,YAAA;MACA,SAAAhE,YAAA,CAAAkC,MAAA;QACA,KAAAW,QAAA,CAAAoB,OAAA;QACA;MACA;MACA,KAAAC,KAAA,SAAArC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA/B,YAAA;IACA;IAEA,WACAmE,YAAA,WAAAA,aAAA;MACA,UAAA/D,aAAA,CAAAE,KAAA;QACA,KAAAuC,QAAA,CAAAoB,OAAA;QACA;MACA;MACA,KAAA/D,mBAAA;IACA;IAEA,eACAkE,gBAAA,WAAAA,iBAAAC,CAAA;MACAA,CAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;IAEA,cACA/C,QAAA,WAAAA,SAAA;MACA,WAAAA,cAAA;IACA;IAEA,YACAgD,YAAA,WAAAA,aAAAC,IAAA;MACAC,OAAA,CAAAC,GAAA,YAAAF,IAAA;MACAC,OAAA,CAAAC,GAAA;QACAC,MAAA,OAAAlE,YAAA;QACAmE,OAAA,OAAAvD;MACA;MAEA,IAAAwD,OAAA,GAAAL,IAAA,CAAA/E,IAAA,CAAAqF,OAAA;MACA,IAAAC,MAAA,GAAAP,IAAA,CAAAQ,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAjC,QAAA,CAAAqC,KAAA;QACA;MACA;MACA,KAAAF,MAAA;QACA,KAAAnC,QAAA,CAAAqC,KAAA;QACA;MACA;MAEA,KAAArC,QAAA,CAAAsC,IAAA;MACA;IACA;IAEA,aACAC,mBAAA,WAAAA,oBAAAC,QAAA,EAAAZ,IAAA;MACAC,OAAA,CAAAC,GAAA,gBAAAU,QAAA;MACAX,OAAA,CAAAC,GAAA,WAAAF,IAAA;MAEA,IAAAY,QAAA,CAAAC,IAAA;QACA;QACA,KAAAlF,aAAA,CAAAE,KAAA,GAAA+E,QAAA,CAAAE,GAAA;QACA,KAAA1C,QAAA,CAAAC,OAAA;MACA;QACA,KAAAD,QAAA,CAAAqC,KAAA,cAAAG,QAAA,CAAAG,GAAA;MACA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAAC,GAAA,EAAAjB,IAAA;MACAC,OAAA,CAAAQ,KAAA,UAAAQ,GAAA;MACAhB,OAAA,CAAAC,GAAA,WAAAF,IAAA;MACA,KAAA5B,QAAA,CAAAqC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
-- VIP费用配置SQL
-- 确保平台配置表中有VIP费用配置项

-- 检查是否已存在VIP费用配置
SELECT * FROM platform_config WHERE config_key = 'teacher.vip.fee';

-- 如果不存在，则插入VIP费用配置
INSERT INTO platform_config (
    config_key, 
    config_value, 
    config_name, 
    config_desc, 
    config_type, 
    create_time, 
    update_time,
    create_by,
    update_by
) 
SELECT 
    'teacher.vip.fee', 
    '199.00', 
    '教师VIP费用', 
    '教师升级为高级会员需要缴纳的费用金额',
    'number',
    NOW(), 
    NOW(),
    'admin',
    'admin'
WHERE NOT EXISTS (
    SELECT 1 FROM platform_config WHERE config_key = 'teacher.vip.fee'
);

-- 如果已存在但值为0，则更新为199.00
UPDATE platform_config
SET config_value = '199.00',
    update_time = NOW(),
    update_by = 'admin'
WHERE config_key = 'teacher.vip.fee'
  AND (config_value = '0' OR config_value = '0.00' OR config_value IS NULL OR config_value = '');

-- 验证配置是否正确插入
SELECT 
    id,
    config_key,
    config_value,
    config_name,
    config_desc,
    create_time,
    update_time
FROM platform_config 
WHERE config_key = 'teacher.vip.fee';

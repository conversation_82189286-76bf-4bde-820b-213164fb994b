<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherCertificationMapper">

    <resultMap type="TeacherCertification" id="TeacherCertificationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="idCard"    column="id_card"    />
        <result property="idCardFront"    column="id_card_front"    />
        <result property="idCardBack"    column="id_card_back"    />
        <result property="education"    column="education"    />
        <result property="educationCert"    column="education_cert"    />
        <result property="teacherCert"    column="teacher_cert"    />
        <result property="otherCerts"    column="other_certs"    />
        <result property="certificationStatus"    column="certification_status"    />
        <result property="certificationTime"    column="certification_time"    />
        <result property="auditReason"    column="audit_reason"    />
        <result property="feeStatus"    column="fee_status"    />
        <result property="feeAmount"    column="fee_amount"    />
        <result property="feeTime"    column="fee_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="qualificationStatus"    column="qualification_status"    />
        <result property="studentCardUrl"    column="student_card_url"    />
        <result property="identityStatus"    column="identity_status"    />
    </resultMap>

    <sql id="selectTeacherCertificationVo">
        select id, teacher_id, id_card, id_card_front, id_card_back, education, education_cert, teacher_cert, other_certs, certification_status, certification_time, audit_reason, fee_status, fee_amount, fee_time, create_by, create_time, update_by, update_time, remark, qualification_status, student_card_url, identity_status from teacher_certification
    </sql>

    <select id="selectTeacherCertificationList" parameterType="TeacherCertification" resultMap="TeacherCertificationResult">
        <include refid="selectTeacherCertificationVo"/>
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="certificationStatus != null  and certificationStatus != ''"> and certification_status = #{certificationStatus}</if>
        </where>
    </select>

    <select id="selectTeacherCertificationById" parameterType="Long" resultMap="TeacherCertificationResult">
        <include refid="selectTeacherCertificationVo"/>
        where id = #{id}
    </select>
    
    <select id="selectTeacherCertificationByTeacherId" parameterType="Long" resultMap="TeacherCertificationResult">
        <include refid="selectTeacherCertificationVo"/>
        where teacher_id = #{teacherId}
    </select>

    <insert id="insertTeacherCertification" parameterType="TeacherCertification" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_certification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="idCard != null">id_card,</if>
            <if test="idCardFront != null">id_card_front,</if>
            <if test="idCardBack != null">id_card_back,</if>
            <if test="education != null">education,</if>
            <if test="educationCert != null">education_cert,</if>
            <if test="teacherCert != null">teacher_cert,</if>
            <if test="otherCerts != null">other_certs,</if>
            <if test="certificationStatus != null">certification_status,</if>
            <if test="certificationTime != null">certification_time,</if>
            <if test="auditReason != null">audit_reason,</if>
            <if test="feeStatus != null">fee_status,</if>
            <if test="feeAmount != null">fee_amount,</if>
            <if test="feeTime != null">fee_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="qualificationStatus != null">qualification_status,</if>
            <if test="studentCardUrl != null">student_card_url,</if>
            <if test="identityStatus != null">identity_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="idCardFront != null">#{idCardFront},</if>
            <if test="idCardBack != null">#{idCardBack},</if>
            <if test="education != null">#{education},</if>
            <if test="educationCert != null">#{educationCert},</if>
            <if test="teacherCert != null">#{teacherCert},</if>
            <if test="otherCerts != null">#{otherCerts},</if>
            <if test="certificationStatus != null">#{certificationStatus},</if>
            <if test="certificationTime != null">#{certificationTime},</if>
            <if test="auditReason != null">#{auditReason},</if>
            <if test="feeStatus != null">#{feeStatus},</if>
            <if test="feeAmount != null">#{feeAmount},</if>
            <if test="feeTime != null">#{feeTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="qualificationStatus != null">#{qualificationStatus},</if>
            <if test="studentCardUrl != null">#{studentCardUrl},</if>
            <if test="identityStatus != null">#{identityStatus},</if>
         </trim>
    </insert>

    <update id="updateTeacherCertification" parameterType="TeacherCertification">
        update teacher_certification
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="idCardFront != null">id_card_front = #{idCardFront},</if>
            <if test="idCardBack != null">id_card_back = #{idCardBack},</if>
            <if test="education != null">education = #{education},</if>
            <if test="educationCert != null">education_cert = #{educationCert},</if>
            <if test="teacherCert != null">teacher_cert = #{teacherCert},</if>
            <if test="otherCerts != null">other_certs = #{otherCerts},</if>
            <if test="certificationStatus != null">certification_status = #{certificationStatus},</if>
            <if test="certificationTime != null">certification_time = #{certificationTime},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="feeStatus != null">fee_status = #{feeStatus},</if>
            <if test="feeAmount != null">fee_amount = #{feeAmount},</if>
            <if test="feeTime != null">fee_time = #{feeTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="qualificationStatus != null">qualification_status = #{qualificationStatus},</if>
            <if test="studentCardUrl != null">student_card_url = #{studentCardUrl},</if>
            <if test="identityStatus != null">identity_status = #{identityStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherCertificationById" parameterType="Long">
        delete from teacher_certification where id = #{id}
    </delete>

    <delete id="deleteTeacherCertificationByIds" parameterType="String">
        delete from teacher_certification where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
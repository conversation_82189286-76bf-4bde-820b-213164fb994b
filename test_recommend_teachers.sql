-- 测试推荐教师查询
-- 检查是否有符合条件的教师数据

-- 1. 检查教师总数
SELECT COUNT(*) as total_teachers FROM teacher_info;

-- 2. 检查认证状态为2的教师数量
SELECT COUNT(*) as certified_teachers FROM teacher_info WHERE certification_status = '2';

-- 3. 检查状态为0且认证状态为2的教师数量
SELECT COUNT(*) as active_certified_teachers FROM teacher_info WHERE status = '0' AND certification_status = '2';

-- 4. 查看前5个符合条件的教师
SELECT 
    id,
    nick_name,
    university,
    major,
    grade,
    status,
    certification_status,
    self_introduction
FROM teacher_info 
WHERE status = '0' AND certification_status = '2'
LIMIT 5;

-- 5. 测试完整的推荐教师查询
SELECT
    t.id,
    t.nick_name as name,
    t.avatar,
    t.university,
    t.major,
    t.grade,
    GROUP_CONCAT(DISTINCT s.subject_name) as subject,
    '经验丰富' as experience,
    IFNULL(t.rating, 4.5) as rating,
    t.self_introduction as features,
    '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三' as grades
FROM teacher_info t
LEFT JOIN teacher_subject ts ON t.id = ts.teacher_id
LEFT JOIN subject s ON ts.subject_id = s.id
WHERE t.status = '0' AND t.certification_status = '2'
GROUP BY t.id, t.nick_name, t.avatar, t.university, t.major, t.grade, t.rating, t.self_introduction
ORDER BY t.rating DESC, t.create_time DESC
LIMIT 10;

/* pages/parent/home/<USER>/
.home-container {
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为TabBar留出空间 */
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 顶部用户信息 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.greeting {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.quick-publish {
  margin-left: 20rpx;
}

.publish-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  color: white;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10rpx);
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 统计数据 */
.stats-section {
  margin-bottom: 20rpx;
}

.stats-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  text-align: center;
  cursor: pointer;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: #eee;
  margin: 0 20rpx;
}

/* 通用区块样式 */
.section {
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.section-more {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #667eea;
  cursor: pointer;
}

.arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 订单列表 */
.order-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.order-item {
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  cursor: pointer;
}

.order-item:last-child {
  border-bottom: none;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
}

.status-招募中 {
  background: #52c41a;
}

.status-进行中 {
  background: #1890ff;
}

.status-已完成 {
  background: #722ed1;
}

.order-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #666;
}

.info-divider {
  margin: 0 12rpx;
  color: #ccc;
}

.order-time {
  font-size: 22rpx;
  color: #999;
}

/* 教师列表 */
.teacher-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  cursor: pointer;
}

.teacher-item:last-child {
  border-bottom: none;
}

.teacher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.teacher-subject {
  font-size: 24rpx;
  color: #667eea;
  margin-bottom: 8rpx;
}

.teacher-experience {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.teacher-rating {
  display: flex;
  align-items: center;
  font-size: 22rpx;
}

.rating-icon {
  color: #faad14;
  margin-right: 4rpx;
}

.rating-score {
  color: #666;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
  display: block;
}

.empty-btn {
  background: #667eea;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 24rpx;
  border: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* pages/parent/home/<USER>/
.home-container {
  height: calc(100vh - 88rpx - 120rpx - env(safe-area-inset-bottom));
  background-color: #f5f7fa;
  box-sizing: border-box;
}

/* 轮播图 */
.banner-section {
  margin: 20rpx;
  margin-bottom: 0;
}

.banner-swiper {
  height: 300rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 通知公告区域 */
.announcement-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title-with-icon {
  display: flex;
  align-items: center;
}

.announcement-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.announcement-list {
  padding: 0;
}

.announcement-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-item:active {
  background-color: #f8f9fa;
}

.announcement-title {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.announcement-arrow {
  font-size: 28rpx;
  color: #999;
  font-weight: bold;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333;
}

/* 通用区块样式 */
.section {
  margin: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.section-more {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #667eea;
  cursor: pointer;
}

.arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 订单列表 */
.order-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.order-item {
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  cursor: pointer;
}

.order-item:last-child {
  border-bottom: none;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.order-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
}

.status-1 { background: #ff6b35; } /* 待支付 */
.status-2 { background: #f39c12; } /* 待确认 */
.status-3 { background: #3498db; } /* 已确认 */
.status-4 { background: #9b59b6; } /* 试课中 */
.status-5 { background: #27ae60; } /* 试课通过 */
.status-6 { background: #e74c3c; } /* 试课未通过 */
.status-7 { background: #667eea; } /* 进行中 */
.status-8 { background: #2ecc71; } /* 已完成 */
.status-9 { background: #95a5a6; } /* 已取消 */

.order-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #666;
}

.info-divider {
  margin: 0 12rpx;
  color: #ccc;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time {
  font-size: 22rpx;
  color: #999;
}

.order-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: none;
  background: #667eea;
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e5e5e5;
}

/* 推荐教师 */
.recommend-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.recommend-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.recommend-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.recommend-subtitle {
  font-size: 24rpx;
  color: #999;
}

.teacher-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.teacher-item:active {
  background: #f0f0f0;
}

.teacher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.teacher-subject {
  font-size: 24rpx;
  color: #667eea;
  margin-bottom: 8rpx;
}

.teacher-experience {
  font-size: 22rpx;
  color: #999;
}

.teacher-rating {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #ff6b35;
  margin-right: 20rpx;
}

.rating-star {
  margin-right: 8rpx;
}

.book-btn {
  background: #667eea;
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #667eea;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 24rpx;
  border: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

# 第三步：后端代码整理清单

## 🎯 目标
- 统一Mapper查询方法
- 规范接口命名
- 清理重复代码
- 同步src和target目录

## 📋 整理任务清单

### 1. Mapper层整理
- [ ] **TutorOrderMapper.xml** 
  - 统一教师查询方法
  - 使用标准化视图或统一字段名
  - 删除测试用的临时方法

- [ ] **TeacherInfoMapper.xml**
  - 检查是否有重复的查询方法
  - 统一字段映射

### 2. Service层整理
- [ ] **ITutorOrderService.java**
  - 检查方法命名规范
  - 删除未使用的方法

- [ ] **TutorOrderServiceImpl.java**
  - 实现标准化
  - 错误处理统一

### 3. Controller层整理
- [ ] **AppletParentHomeController.java**
  - 接口路径规范化
  - 响应格式统一
  - 错误处理标准化

- [ ] **AppletTeacherController.java**
  - 同上

### 4. 实体类整理
- [ ] **TeacherInfo.java**
  - 字段注释更新
  - getter/setter检查

- [ ] **TutorOrder.java**
  - 字段映射检查

## 🔧 具体执行步骤

### 步骤1：备份当前工作版本
```bash
# 创建备份分支
git checkout -b backup-before-cleanup
git add .
git commit -m "备份：整理前的工作版本"
git checkout main
```

### 步骤2：清理Mapper XML
1. 检查 `src/main/resources/mapper/education/TutorOrderMapper.xml`
2. 删除测试方法：`getSimpleTeachers`
3. 标准化 `getRecommendTeachers` 方法
4. 标准化 `getTeacherDetail` 方法

### 步骤3：同步target目录
```bash
# 重新编译确保同步
mvn clean compile
```

### 步骤4：测试验证
1. 启动后端服务
2. 测试推荐教师接口
3. 测试教师详情接口
4. 确认功能正常

## ⚠️ 注意事项

1. **每次只修改一个文件**
2. **修改后立即测试**
3. **保留备份**
4. **记录修改内容**

## 📝 修改记录模板

```
文件：TutorOrderMapper.xml
修改内容：
- 删除getSimpleTeachers方法
- 统一getRecommendTeachers查询字段
- 使用标准deleted字段替代del_flag

测试结果：
- ✅ 推荐教师接口正常
- ✅ 教师详情接口正常
- ✅ 前端显示正常
```

## 🎯 预期效果

整理完成后：
- 查询逻辑统一
- 接口响应一致
- 代码结构清晰
- 便于后续维护

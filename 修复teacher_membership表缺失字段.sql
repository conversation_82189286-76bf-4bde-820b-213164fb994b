-- 修复teacher_membership表缺失字段

-- 1. 检查当前表结构
SELECT '=== 当前teacher_membership表结构 ===' as info;
DESCRIBE teacher_membership;

-- 2. 检查缺失的字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'order_no字段已存在'
        ELSE 'order_no字段不存在，需要添加'
    END as order_no_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership' 
  AND COLUMN_NAME = 'order_no';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'status字段已存在'
        ELSE 'status字段不存在，需要添加'
    END as status_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership' 
  AND COLUMN_NAME = 'status';

-- 3. 添加order_no字段（如果不存在）
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'teacher_membership' 
                   AND COLUMN_NAME = 'order_no');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE teacher_membership ADD COLUMN order_no varchar(64) DEFAULT NULL COMMENT ''支付订单号'' AFTER payment_time',
              'SELECT ''order_no字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加status字段（如果不存在）
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'teacher_membership' 
                   AND COLUMN_NAME = 'status');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE teacher_membership ADD COLUMN status char(1) DEFAULT ''1'' COMMENT ''状态（1正常 0停用）'' AFTER order_no',
              'SELECT ''status字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 验证字段添加结果
SELECT '=== 验证字段添加结果 ===' as info;

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ order_no字段存在'
        ELSE '❌ order_no字段仍然不存在'
    END as order_no_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership' 
  AND COLUMN_NAME = 'order_no';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ status字段存在'
        ELSE '❌ status字段仍然不存在'
    END as status_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership' 
  AND COLUMN_NAME = 'status';

-- 6. 显示更新后的表结构
SELECT '=== 更新后的teacher_membership表结构 ===' as info;
DESCRIBE teacher_membership;

-- 7. 更新现有数据的status字段为正常状态
UPDATE teacher_membership 
SET status = '1' 
WHERE status IS NULL;

SELECT '🎉 teacher_membership表字段修复完成！' as result;

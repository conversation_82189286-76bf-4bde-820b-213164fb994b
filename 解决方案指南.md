# TutorOrderMapper错误解决方案

## 🎯 问题分析

**错误信息**：
```
Unknown column 'o.area_name' in 'field list'
```

**根本原因**：
数据库表`tutor_order`中缺少`area_name`字段，但Java实体类和XML映射中都定义了这个字段。

## 🚀 解决步骤

### 第一步：检查数据库表结构
```bash
# 执行检查脚本
mysql -u root -p your_database_name < 检查tutor_order表结构.sql
```

### 第二步：添加缺失字段
```bash
# 执行修复脚本
mysql -u root -p your_database_name < 修复tutor_order表字段.sql
```

### 第三步：重新编译项目
```bash
# 清理并重新编译
mvn clean compile
```

### 第四步：重启应用
重启Spring Boot应用

## 📋 手动执行SQL（如果脚本执行失败）

### 1. 检查字段是否存在
```sql
-- 检查area_name字段
SELECT COUNT(*) as area_name_exists
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'area_name';

-- 检查view_count字段
SELECT COUNT(*) as view_count_exists
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'view_count';
```

### 2. 添加缺失字段
```sql
-- 如果area_name不存在，执行：
ALTER TABLE tutor_order 
ADD COLUMN area_name varchar(100) DEFAULT NULL COMMENT '地区名称' AFTER city;

-- 如果view_count不存在，执行：
ALTER TABLE tutor_order 
ADD COLUMN view_count int(11) DEFAULT 0 COMMENT '浏览次数';
```

### 3. 验证字段添加
```sql
-- 查看表结构
DESCRIBE tutor_order;

-- 测试查询
SELECT o.id, o.city, o.area_name, o.view_count 
FROM tutor_order o 
LIMIT 1;
```

## 🔧 已修复的文件

### 1. TutorOrderMapper.xml
**文件位置**：`src/main/resources/mapper/education/TutorOrderMapper.xml`

**修复内容**：在`selectTutorOrderVo`中添加了`o.area_name`字段

**修复后的SQL**：
```sql
<sql id="selectTutorOrderVo">
    select o.id, o.order_no, ..., o.city, o.area_name, o.detailed_address, ...,
           COALESCE(o.view_count, 0) as view_count,
           COALESCE((select count(*) from order_application a where a.order_id = o.id), 0) as apply_count
    from tutor_order o
</sql>
```

## 📊 字段说明

### area_name字段
- **类型**：varchar(100)
- **默认值**：NULL
- **注释**：地区名称
- **位置**：在city字段之后

### view_count字段
- **类型**：int(11)
- **默认值**：0
- **注释**：浏览次数
- **用途**：统计订单浏览次数

## ⚠️ 注意事项

1. **备份数据库**：在执行ALTER TABLE之前，请备份数据库
2. **检查依赖**：确保order_application表存在（用于子查询）
3. **权限确认**：确保数据库用户有ALTER TABLE权限
4. **重启应用**：修改数据库结构后必须重启应用

## 🧪 测试验证

### 1. 数据库层面测试
```sql
-- 测试基本查询
SELECT o.id, o.order_no, o.city, o.area_name, o.view_count
FROM tutor_order o 
WHERE o.status = '2' 
LIMIT 5;
```

### 2. 应用层面测试
```bash
# 访问接口测试
curl -X GET "http://localhost:8080/applet/tutorOrder/new"
```

### 3. 预期结果
- ✅ 不再出现"Unknown column 'o.area_name'"错误
- ✅ 不再出现"Invalid bound statement"错误
- ✅ 接口正常返回数据

## 🔍 故障排查

### 如果仍然报错：

1. **检查字段是否真的添加成功**：
   ```sql
   DESCRIBE tutor_order;
   ```

2. **检查应用是否重启**：
   确保Spring Boot应用已重启

3. **检查编译是否成功**：
   确保target目录下的XML文件已更新

4. **检查其他可能的问题**：
   - 数据库连接是否正常
   - 是否有其他表结构问题
   - 是否有权限问题

## 📞 如果问题仍然存在

请提供以下信息：
1. `DESCRIBE tutor_order;` 的完整输出
2. 应用启动日志中的错误信息
3. 是否成功执行了ALTER TABLE语句

---

**总结**：主要问题是数据库表缺少字段，通过添加缺失字段并重新编译应用即可解决。

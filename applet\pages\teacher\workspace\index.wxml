<navigation-bar title="工作台" back="{{false}}"></navigation-bar>

<view class="container tabbar-page">
  <!-- 订单列表 -->
  <scroll-view scroll-y="true" class="scroll-view tabbar-scroll-view" bindscrolltolower="loadMoreOrders" bindrefresherrefresh="onPullDownRefresh" refresher-enabled="{{true}}">
    <view wx:if="{{orders.length === 0 && !loading}}" class="empty-state">
      <view class="empty-icon"><image class="menu-icon" src="/assets/images/notdd.png"></image></view>
      <view class="empty-title">暂时没有指派给您的订单</view>
      <view class="empty-desc">已完成的订单，家长的联系方式请点击 生源 — 已完成 查看</view>
    </view>

    <block wx:for="{{orders}}" wx:key="id">
      <view class="order-card" data-id="{{item.id}}" bindtap="onOrderCardTap">
        <view class="orderTop-flex">
          <view class="order-title">{{item.grade}} {{item.subjectName}}</view>
          <view class="order-top-right">
            <view class="order-status">{{item.statusText || '进行中'}}</view>
            <view class="{{item.tutoringMode=='在线辅导'?'apply-orBtn':'apply-btn'}}">{{item.tutoringMode}}</view>
          </view>
        </view>
        <view class="order-info">
          <view class="info-item">
            <text class="label">教师性别：</text>
            <text class="value">{{item.teacherGender}}</text>
          </view>
          <view class="info-item">
            <text class="label">教师身份：</text>
            <text class="value">{{item.teacherCategory}}</text>
          </view>
          <view class="info-item">
            <text class="label">辅导日期：</text>
            <text class="value">{{item.availableTutoringTime}}中的某{{item.weeklySessions}}天</text>
          </view>
          <view class="info-item">
            <text class="label">辅导时间：</text>
            <text class="value">{{item.tutoringTimeSlots}}中的某{{item.sessionDuration}}分钟</text>
          </view>
          <view class="info-item">
            <text class="label">学生情况：</text>
            <text class="value">{{item.studentDetails}}</text>
          </view>
          <view class="info-item">
            <text class="label">授课地址：</text>
            <text class="value">{{item.city}}-{{item.detailedAddress}}</text>
          </view>
          <view class="info-item" wx:if="{{item.contactName}}">
            <text class="label">联系人：</text>
            <text class="value">{{item.contactName}}</text>
          </view>
          <view class="info-item" wx:if="{{item.contactPhone}}">
            <text class="label">联系电话：</text>
            <text class="value">{{item.contactPhone}}</text>
          </view>
        </view>
        <view class="order-footer-flex">
          <view class="publish-time">开始时间：{{item.selectTime || item.createTime}}</view>
          <view class="action-buttons">
            <button
              class="action-btn trial-btn"
              wx:if="{{item.showTrialButton}}"
              data-id="{{item.id}}"
              data-action="finishTrial"
              catchtap="onActionTap"
            >完成试课</button>
            <button
              class="action-btn week-btn"
              wx:if="{{item.showWeekButton}}"
              data-id="{{item.id}}"
              data-action="finishWeek"
              catchtap="onActionTap"
            >完成讲授一周</button>
          </view>
        </view>
      </view>
    </block>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{orders.length > 0}}">
      <view class="load-more-text" wx:if="{{loading}}">加载中...</view>
      <view class="load-more-text" wx:elif="{{!hasMore}}">没有更多了</view>
      <view class="load-more-text" wx:else>上拉加载更多</view>
    </view>
  </scroll-view>
</view>
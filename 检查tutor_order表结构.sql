-- 检查tutor_order表结构和缺失字段

-- 1. 查看当前表结构
SELECT '=== 当前tutor_order表结构 ===' as info;
DESCRIBE tutor_order;

-- 2. 检查是否存在area_name字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'area_name字段已存在'
        ELSE 'area_name字段不存在，需要添加'
    END as area_name_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'area_name';

-- 3. 如果area_name字段不存在，执行以下语句添加：
-- ALTER TABLE tutor_order ADD COLUMN area_name varchar(100) DEFAULT NULL COMMENT '地区名称' AFTER city;

-- 4. 检查其他可能缺失的字段
SELECT '=== 检查其他可能缺失的字段 ===' as info;

-- 检查view_count字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'view_count字段已存在'
        ELSE 'view_count字段不存在，需要添加'
    END as view_count_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'view_count';

-- 如果view_count字段不存在，执行以下语句：
-- ALTER TABLE tutor_order ADD COLUMN view_count int(11) DEFAULT 0 COMMENT '浏览次数';

-- 5. 查看完整的字段列表
SELECT '=== tutor_order表所有字段 ===' as info;
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order'
ORDER BY ORDINAL_POSITION;

-- 6. 检查order_application表是否存在（用于子查询）
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'order_application表已存在'
        ELSE 'order_application表不存在，可能影响子查询'
    END as order_application_check
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'order_application';

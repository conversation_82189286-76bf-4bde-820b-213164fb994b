/* pages/parent/announcement/detail/index.wxss */
.page-container {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.announcement-detail {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.announcement-header {
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.announcement-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.announcement-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.announcement-content {
  padding: 30rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

/* rich-text 内容样式 */
.content-text >>> p {
  margin: 20rpx 0;
  line-height: 1.6;
}

.content-text >>> strong {
  font-weight: bold;
  color: #333;
}

.content-text >>> em {
  font-style: italic;
}

.content-text >>> h1,
.content-text >>> h2,
.content-text >>> h3,
.content-text >>> h4,
.content-text >>> h5,
.content-text >>> h6 {
  font-weight: bold;
  margin: 30rpx 0 20rpx 0;
  color: #333;
}

.content-text >>> h1 { font-size: 40rpx; }
.content-text >>> h2 { font-size: 36rpx; }
.content-text >>> h3 { font-size: 32rpx; }
.content-text >>> h4 { font-size: 30rpx; }
.content-text >>> h5 { font-size: 28rpx; }
.content-text >>> h6 { font-size: 26rpx; }

.content-text >>> ul,
.content-text >>> ol {
  margin: 20rpx 0;
  padding-left: 40rpx;
}

.content-text >>> li {
  margin: 10rpx 0;
  line-height: 1.6;
}

.content-text >>> a {
  color: #667eea;
  text-decoration: underline;
}

.content-text >>> img {
  max-width: 100%;
  height: auto;
  margin: 20rpx 0;
  border-radius: 8rpx;
}

.content-text >>> blockquote {
  border-left: 4rpx solid #667eea;
  padding-left: 20rpx;
  margin: 20rpx 0;
  color: #666;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
}

/* pages/parent/announcement/detail/index.wxss */
.page-container {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.announcement-detail {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.announcement-header {
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.announcement-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.announcement-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.announcement-content {
  padding: 30rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

<navigation-bar title="生源" back="{{false}}"></navigation-bar>

<view class="container tabbar-page">
  <!-- Tabs -->
  <view class="tabs">
    <view class="tab-item {{activeTab === 0 ? 'active' : ''}}" data-index="0" bindtap="handleTabClick">新家教</view>
    <view class="tab-item {{activeTab === 1 ? 'active' : ''}}" data-index="1" bindtap="handleTabClick">已报名</view>
    <view class="tab-item {{activeTab === 2 ? 'active' : ''}}" data-index="2" bindtap="handleTabClick">已完成</view>
  </view>

  <!-- Filters -->
  <view class="filters">
    <view class="filter-item" bindtap="showCityFilter">
      城市: {{getCityDisplayText()}}
      <text class="arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showGradeFilter">
      年级: {{getGradeDisplayText()}}
      <text class="arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showSubjectFilter">
      科目: {{getSubjectDisplayText()}}
      <text class="arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showFilterPopup">
      <text class="arrow">☰</text>
    </view>
  </view>

  <!-- Content -->
  <swiper class="swiper" current="{{activeTab}}" bindchange="handleSwiperChange">
    <!-- 新家教 -->
    <swiper-item>
      <scroll-view scroll-y="true" class="scroll-view" bindscrolltolower="loadMoreNewOrders">
        <view wx:if="{{newOrders.length === 0}}" class="empty-state">暂无新的家教信息</view>
        <block wx:for="{{newOrders}}" wx:key="id">
          <view class="order-card">
            <view class="orderTop-flex">
              <view class="order-title">{{item.grade}} {{item.subjectName}}</view>
              <view class="{{item.tutoringMode=='在线辅导'?'apply-orBtn':'apply-btn'}}">{{item.tutoringMode}}</view>
            </view>
            <view class="order-info">
            <view class="order-top-right">
                <view class="apply-count">已报名{{item.applyCount}}人</view>
              </view>
              <view class="info-item">
                <text class="label">教师性别：</text>
                <text class="value">{{item.teacherGender}}</text>
              </view>
              <view class="info-item">
                <text class="label">教师身份：</text>
                <text class="value">{{item.teacherCategory}}</text>
              </view>
              <view class="info-item">
                <text class="label">辅导日期：</text>
                <text class="value">{{item.availableTutoringTime}}中的某{{item.weeklySessions}}天</text>
              </view>
              <view class="info-item">
                <text class="label">辅导时间：</text>
                <text class="value">{{item.tutoringTimeSlots}}中的某{{item.sessionDuration}}分钟</text>
              </view>
              <view class="info-item">
                <text class="label">学生情况：</text>
                <text class="value">{{item.studentDetails}}</text>
              </view>
              <view class="info-item">
                <text class="label">授课地址：</text>
                <text class="value">{{item.city}}-{{item.detailedAddress}}</text>
              </view>
            </view>
            <view class="order-footer-flex">
              <view class="publish-time">发布时间：{{item.createTime}}</view>
              <button class="detail-btn" data-id="{{item.id}}" bindtap="goDetail">了解详情</button>
            </view>
          </view>
        </block>
      </scroll-view>
    </swiper-item>
    <!-- 已报名 -->
    <swiper-item>
      <scroll-view scroll-y="true" class="scroll-view" bindscrolltolower="loadMoreAppliedOrders">
        <view wx:if="{{appliedOrders.length === 0}}" class="empty-state">暂无已报名的家教信息</view>
        <block wx:for="{{appliedOrders}}" wx:key="id">
          <view class="order-card">
            <view class="orderTop-flex">
              <view class="order-title">{{item.grade}} {{item.subjectName}}</view>
              <view class="order-top-right">
                <view class="apply-count">已报名{{item.applyCount}}人</view>
                <view class="apply-btn">{{item.tutoringMode}}</view>
              </view>
            </view>
            <view class="order-info">
              <view class="info-item">
                <text class="label">教师性别：</text>
                <text class="value">{{item.teacherGender}}</text>
              </view>
              <view class="info-item">
                <text class="label">教师身份：</text>
                <text class="value">{{item.teacherCategory}}</text>
              </view>
              <view class="info-item">
                <text class="label">辅导日期：</text>
                <text class="value">{{item.availableTutoringTime}}中的某{{item.weeklySessions}}天</text>
              </view>
              <view class="info-item">
                <text class="label">辅导时间：</text>
                <text class="value">{{item.tutoringTimeSlots}}中的某{{item.sessionDuration}}分钟</text>
              </view>
              <view class="info-item">
                <text class="label">学生情况：</text>
                <text class="value">{{item.studentDetails}}</text>
              </view>
              <view class="info-item">
                <text class="label">授课地址：</text>
                <text class="value">{{item.city}}-{{item.detailedAddress}}</text>
              </view>
            </view>
            <view class="order-footer-flex">
              <view class="publish-time">发布时间：{{item.createTime}}</view>
              <button class="detail-btn" data-id="{{item.id}}" bindtap="goDetail">了解详情</button>
            </view>
          </view>
        </block>
      </scroll-view>
    </swiper-item>
    <!-- 已完成 -->
    <swiper-item>
      <scroll-view scroll-y="true" class="scroll-view" bindscrolltolower="loadMoreCompletedOrders">
        <view wx:if="{{completedOrders.length === 0}}" class="empty-state">暂无已完成的家教信息</view>
        <block wx:for="{{completedOrders}}" wx:key="id">
          <view class="order-card">
            <view class="orderTop-flex">
              <view class="order-title">{{item.grade}} {{item.subjectName}}</view>
              <view class="order-top-right">
                <view class="apply-count">已报名{{item.applyCount}}人</view>
                <view class="apply-btn">{{item.tutoringMode}}</view>
              </view>
            </view>
            <view class="order-info">
              <view class="info-item">
                <text class="label">教师性别：</text>
                <text class="value">{{item.teacherGender}}</text>
              </view>
              <view class="info-item">
                <text class="label">教师身份：</text>
                <text class="value">{{item.teacherCategory}}</text>
              </view>
              <view class="info-item">
                <text class="label">辅导日期：</text>
                <text class="value">{{item.availableTutoringTime}}中的某{{item.weeklySessions}}天</text>
              </view>
              <view class="info-item">
                <text class="label">辅导时间：</text>
                <text class="value">{{item.tutoringTimeSlots}}中的某{{item.sessionDuration}}分钟</text>
              </view>
              <view class="info-item">
                <text class="label">学生情况：</text>
                <text class="value">{{item.studentDetails}}</text>
              </view>
              <view class="info-item">
                <text class="label">授课地址：</text>
                <text class="value">{{item.city}}-{{item.detailedAddress}}</text>
              </view>
            </view>
            <view class="order-footer-flex">
              <view class="publish-time">完成时间：{{item.updateTime}}</view>
              <button class="detail-btn" data-id="{{item.id}}" bindtap="goDetail">了解详情</button>
            </view>
          </view>
        </block>
      </scroll-view>
    </swiper-item>
  </swiper>

  <!-- 平台承诺书弹窗 -->
  <platform-agreement
    show="{{showAgreement}}"
    bind:close="onAgreementClose"
    bind:agree="onAgreementAgree">
  </platform-agreement>

  <!-- 电子签名弹窗 -->
  <electronic-signature
    show="{{showSignature}}"
    bind:close="onSignatureClose"
    bind:signature="onSignatureComplete">
  </electronic-signature>

  <!-- 筛选弹窗 -->
  <filter-popup
    show="{{showFilterPopup}}"
    filter-options="{{filterOptions}}"
    selected-filters="{{selectedFilters}}"
    bind:filter="onFilterChange"
    bind:close="onFilterClose">
  </filter-popup>
</view>
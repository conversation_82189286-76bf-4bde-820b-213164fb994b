<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TutorOrderMapper">
    
    <resultMap type="TutorOrder" id="TutorOrderResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderType"    column="order_type"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="grade"    column="grade"    />
        <result property="studentGender"    column="student_gender"    />
        <result property="tutoringMode"    column="tutoring_mode"    />
        <result property="studentDetails"    column="student_details"    />
        <result property="availableTutoringTime"    column="available_tutoring_time"    />
        <result property="weeklySessions"    column="weekly_sessions"    />
        <result property="tutoringTimeSlots"    column="tutoring_time_slots"    />
        <result property="sessionDuration"    column="session_duration"    />
        <result property="timeSlotNotes"    column="time_slot_notes"    />
        <result property="teacherGender"    column="teacher_gender"    />
        <result property="teacherCategory"    column="teacher_category"    />
        <result property="teachingType"    column="teaching_type"    />
        <result property="salary"    column="salary"    />
        <result property="salaryUnit"    column="salary_unit"    />
        <result property="city"    column="city"    />
        <result property="detailedAddress"    column="detailed_address"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="teachingRequirements"    column="teaching_requirements"    />
        <result property="publisherType"    column="publisher_type"    />
        <result property="status"    column="status"    />
        <result property="selectedTeacherId"    column="selected_teacher_id"    />
        <result property="selectTime"    column="select_time"    />
        <result property="isTop"    column="is_top"    />
        <result property="viewCount"    column="view_count"    />
        <result property="applyCount"    column="apply_count"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTutorOrderVo">
        select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode, o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots, o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type, o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone, o.teaching_requirements, o.publisher_type, o.status, o.selected_teacher_id, o.select_time, o.is_top, o.deleted, o.create_time,
               COALESCE(o.view_count, 0) as view_count,
               COALESCE((select count(*) from order_application a where a.order_id = o.id), 0) as apply_count
        from tutor_order o
    </sql>

    <select id="selectTutorOrderList" parameterType="TutorOrder" resultMap="TutorOrderResult">
        <include refid="selectTutorOrderVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and o.order_no = #{orderNo}</if>
            <if test="orderType != null  and orderType != ''"> and o.order_type = #{orderType}</if>
            <if test="tutoringMode != null  and tutoringMode != ''"> and o.tutoring_mode = #{tutoringMode}</if>
            <if test="subjectName != null  and subjectName != ''"> and o.subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="status != null  and status != ''"> and o.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTutorOrderById" parameterType="Long" resultMap="TutorOrderResult">
        <include refid="selectTutorOrderVo"/>
        where o.id = #{id}
    </select>
        
    <insert id="insertTutorOrder" parameterType="TutorOrder" useGeneratedKeys="true" keyProperty="id">
        insert into tutor_order (
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="orderType != null and orderType != ''">order_type,</if>
            <if test="subjectName != null and subjectName != ''">subject_name,</if>
            <if test="grade != null and grade != ''">grade,</if>
            <if test="studentGender != null and studentGender != ''">student_gender,</if>
            <if test="tutoringMode != null and tutoringMode != ''">tutoring_mode,</if>
            <if test="studentDetails != null and studentDetails != ''">student_details,</if>
            <if test="availableTutoringTime != null and availableTutoringTime != ''">available_tutoring_time,</if>
            <if test="weeklySessions != null">weekly_sessions,</if>
            <if test="tutoringTimeSlots != null and tutoringTimeSlots != ''">tutoring_time_slots,</if>
            <if test="sessionDuration != null">session_duration,</if>
            <if test="timeSlotNotes != null and timeSlotNotes != ''">time_slot_notes,</if>
            <if test="teacherGender != null and teacherGender != ''">teacher_gender,</if>
            <if test="teacherCategory != null and teacherCategory != ''">teacher_category,</if>
            <if test="teachingRequirements != null and teachingRequirements != ''">teaching_requirements,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="detailedAddress != null and detailedAddress != ''">detailed_address,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="publisherType != null and publisherType != ''">publisher_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        )values(
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="orderType != null and orderType != ''">#{orderType},</if>
            <if test="subjectName != null and subjectName != ''">#{subjectName},</if>
            <if test="grade != null and grade != ''">#{grade},</if>
            <if test="studentGender != null and studentGender != ''">#{studentGender},</if>
            <if test="tutoringMode != null and tutoringMode != ''">#{tutoringMode},</if>
            <if test="studentDetails != null and studentDetails != ''">#{studentDetails},</if>
            <if test="availableTutoringTime != null and availableTutoringTime != ''">#{availableTutoringTime},</if>
            <if test="weeklySessions != null">#{weeklySessions},</if>
            <if test="tutoringTimeSlots != null and tutoringTimeSlots != ''">#{tutoringTimeSlots},</if>
            <if test="sessionDuration != null">#{sessionDuration},</if>
            <if test="timeSlotNotes != null and timeSlotNotes != ''">#{timeSlotNotes},</if>
            <if test="teacherGender != null and teacherGender != ''">#{teacherGender},</if>
            <if test="teacherCategory != null and teacherCategory != ''">#{teacherCategory},</if>
            <if test="teachingRequirements != null and teachingRequirements != ''">#{teachingRequirements},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="detailedAddress != null and detailedAddress != ''">#{detailedAddress},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="publisherType != null and publisherType != ''">#{publisherType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
        )
    </insert>

    <update id="updateTutorOrder" parameterType="TutorOrder">
        update tutor_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="orderType != null and orderType != ''">order_type = #{orderType},</if>
            <if test="subjectName != null and subjectName != ''">subject_name = #{subjectName},</if>
            <if test="grade != null and grade != ''">grade = #{grade},</if>
            <if test="studentGender != null and studentGender != ''">student_gender = #{studentGender},</if>
            <if test="tutoringMode != null and tutoringMode != ''">tutoring_mode = #{tutoringMode},</if>
            <if test="studentDetails != null and studentDetails != ''">student_details = #{studentDetails},</if>
            <if test="availableTutoringTime != null and availableTutoringTime != ''">available_tutoring_time = #{availableTutoringTime},</if>
            <if test="weeklySessions != null">weekly_sessions = #{weeklySessions},</if>
            <if test="tutoringTimeSlots != null and tutoringTimeSlots != ''">tutoring_time_slots = #{tutoringTimeSlots},</if>
            <if test="sessionDuration != null">session_duration = #{sessionDuration},</if>
            <if test="timeSlotNotes != null and timeSlotNotes != ''">time_slot_notes = #{timeSlotNotes},</if>
            <if test="teacherGender != null and teacherGender != ''">teacher_gender = #{teacherGender},</if>
            <if test="teacherCategory != null and teacherCategory != ''">teacher_category = #{teacherCategory},</if>
            <if test="teachingRequirements != null and teachingRequirements != ''">teaching_requirements = #{teachingRequirements},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="detailedAddress != null and detailedAddress != ''">detailed_address = #{detailedAddress},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="publisherType != null and publisherType != ''">publisher_type = #{publisherType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTutorOrderById" parameterType="Long">
        delete from tutor_order where id = #{id}
    </delete>

    <delete id="deleteTutorOrderByIds" parameterType="String">
        delete from tutor_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectNewTutorOrderList" resultMap="TutorOrderResult">
        select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode,
               o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots,
               o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type,
               o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone,
               o.teaching_requirements, o.publisher_type, o.status, o.selected_teacher_id, o.select_time,
               o.is_top, o.create_time,
               COALESCE(o.view_count, 0) as view_count,
               COALESCE((select count(*) from order_application a where a.order_id = o.id), 0) as apply_count
        from tutor_order o
        where o.status = '2'
          and o.selected_teacher_id is null
          and (o.deleted is null or o.deleted = 0)
        order by o.is_top desc, o.create_time desc
    </select>

    <select id="selectAppliedTutorOrderListByTeacherId" resultMap="TutorOrderResult" parameterType="long">
        select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode, o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots, o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type, o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone, o.teaching_requirements, o.publisher_type, o.status, o.selected_teacher_id, o.select_time, o.is_top, o.create_time,
               COALESCE(o.view_count, 0) as view_count,
               COALESCE((select count(*) from order_application a2 where a2.order_id = o.id), 0) as apply_count
        from tutor_order o
        inner join order_application a on o.id = a.order_id
        where a.teacher_id = #{teacherId}
          and a.status = '2'  -- 审核通过状态
          and o.status = '2'  -- 订单状态为报名中
          and o.selected_teacher_id is null  -- 订单还未选择教师
        order by a.apply_time desc
    </select>

    <select id="selectCompletedTutorOrderListByTeacherId" resultMap="TutorOrderResult" parameterType="long">
        select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode, o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots, o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type, o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone, o.teaching_requirements, o.publisher_type, o.status, o.is_top, o.create_time,
               COALESCE(o.view_count, 0) as view_count,
               COALESCE((select count(*) from order_application a2 where a2.order_id = o.id), 0) as apply_count,
               -- 添加教师在该订单中的状态标识
               CASE
                   WHEN o.selected_teacher_id = #{teacherId} AND o.status = '8' THEN '已完成'
                   WHEN a.status = '5' THEN '未选中'
                   ELSE '其他'
               END as teacher_order_status
        from tutor_order o
        inner join order_application a on o.id = a.order_id
        where a.teacher_id = #{teacherId}
          and (
              -- 情况1：被选中教师的已完成订单
              (o.selected_teacher_id = #{teacherId} and o.status = '8')
              or
              -- 情况2：未被选中的教师（订单已选择其他教师）
              (a.status = '5' and o.selected_teacher_id is not null and o.selected_teacher_id != #{teacherId} and o.status in ('5', '6', '7', '8'))
          )
        order by o.update_time desc
    </select>

    <!-- 查询教师未选中的订单列表（已报名但未被选中） -->
    <select id="selectUnselectedTutorOrderListByTeacherId" resultMap="TutorOrderResult" parameterType="long">
        select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode, o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots, o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type, o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone, o.teaching_requirements, o.publisher_type, o.status, o.selected_teacher_id, o.select_time, o.is_top, o.create_time,
               COALESCE(o.view_count, 0) as view_count,
               COALESCE((select count(*) from order_application a2 where a2.order_id = o.id), 0) as apply_count
        from tutor_order o
        inner join order_application a on o.id = a.order_id
        where a.teacher_id = #{teacherId}
          and a.status = '5'  -- 已淘汰状态
          and o.status in ('5', '6', '7', '8')  -- 订单已选择教师或后续状态
        order by a.update_time desc
    </select>

    <select id="selectTopOrders" resultMap="TutorOrderResult">
        <include refid="selectTutorOrderVo"/>
        where o.is_top = '1'
    </select>
    
    <select id="selectPendingAuditOrders" resultMap="TutorOrderResult">
        <include refid="selectTutorOrderVo"/>
        where o.status = '1'
    </select>

    <select id="selectTutorOrderByOrderNo" parameterType="String" resultMap="TutorOrderResult">
        <include refid="selectTutorOrderVo"/>
        where o.order_no = #{orderNo}
    </select>

    <select id="countOrdersByStatus" parameterType="String" resultType="Integer">
        select count(*) from tutor_order where status = #{status}
    </select>
    
    <update id="setTop" parameterType="TutorOrder">
        update tutor_order set is_top = #{isTop}, top_time = #{topTime}, update_time = #{updateTime}, update_by = #{updateBy} where id = #{id}
    </update>

    <select id="selectOngoingTutorOrderListByTeacherId" resultMap="TutorOrderResult" parameterType="long">
        <include refid="selectTutorOrderVo"/>
        where o.selected_teacher_id = #{teacherId} and o.status = '7'
        order by o.update_time desc
    </select>

    <update id="updateOrderStatusToOngoing" parameterType="long">
        update tutor_order set status = '7', update_time = now() where id = #{orderId}
    </update>

    <update id="updateOrderStatusToCompleted" parameterType="long">
        update tutor_order set status = '8', update_time = now() where id = #{orderId}
    </update>

    <!-- 选择教师方法 -->
    <update id="selectTeacher" parameterType="TutorOrder">
        update tutor_order
        <set>
            <if test="selectedTeacherId != null">selected_teacher_id = #{selectedTeacherId},</if>
            <if test="selectTime != null">select_time = #{selectTime},</if>
            <if test="contactPublishedTime != null">contact_published_time = #{contactPublishedTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 审核订单方法 -->
    <update id="auditTutorOrder" parameterType="TutorOrder">
        update tutor_order
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 家长相关查询 -->
    <select id="countOrdersByParentId" parameterType="long" resultType="int">
        select count(*) from tutor_order where publisher_id = #{parentId} and publisher_type = '1'
    </select>

    <select id="countOngoingOrdersByParentId" parameterType="long" resultType="int">
        select count(*) from tutor_order
        where publisher_id = #{parentId} and publisher_type = '1' and status = '7'
    </select>

    <select id="countCompletedOrdersByParentId" parameterType="long" resultType="int">
        select count(*) from tutor_order
        where publisher_id = #{parentId} and publisher_type = '1' and status = '8'
    </select>

    <select id="selectRecentOrdersByParentId" resultMap="TutorOrderResult">
        <include refid="selectTutorOrderVo"/>
        where o.publisher_id = #{parentId} and o.publisher_type = '1'
        order by o.create_time desc
        limit #{limit}
    </select>

    <select id="getRecommendTeachers" resultType="map">
        select
            t.id,
            t.nick_name as name,
            t.avatar,
            GROUP_CONCAT(DISTINCT s.subject_name) as subject,
            CONCAT(IFNULL(t.teaching_years, 0), '年教学经验') as experience,
            IFNULL(t.rating, 4.5) as rating
        from teacher_info t
        left join teacher_subject ts on t.id = ts.teacher_id
        left join subject s on ts.subject_id = s.id
        where t.status = '0' and t.certification_status = '2'
        group by t.id, t.nick_name, t.avatar, t.teaching_years, t.rating
        order by t.rating desc, t.create_time desc
        limit #{limit}
    </select>

</mapper>
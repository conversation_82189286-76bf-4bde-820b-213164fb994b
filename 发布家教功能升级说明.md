# 发布家教功能升级说明

## 🎯 升级内容

### 1. 年级二级选项
- **修改前**：单级选择器，所有年级平铺显示
- **修改后**：二级选择器，第一级选择学段，第二级选择具体年级
- **学段分类**：
  - 小学：一年级~六年级
  - 初中：一年级~三年级
  - 高中：一年级~三年级
  - 大学：大一~大四
  - 其他：成人教育、其他

### 2. 科目后台配置
- **修改前**：前端写死科目列表
- **修改后**：从后台API动态获取科目列表
- **接口地址**：`GET /applet/config/subjects`
- **降级策略**：API失败时使用默认科目列表

### 3. 可辅导时间多选
- **修改前**：文本输入框
- **修改后**：周一到周日多选按钮
- **交互方式**：点击切换选中状态
- **显示效果**：选中的按钮高亮显示

### 4. 可辅导时间段
- **修改前**：文本输入框
- **修改后**：起始时间和结束时间选择器
- **时间格式**：HH:MM（24小时制）
- **选择方式**：系统时间选择器

### 5. 手机号优化
- **修改前**：可编辑输入框
- **修改后**：不可编辑，默认显示登录手机号
- **说明文字**：添加"默认为登录手机号"提示

### 6. 联系人称谓
- **修改前**：只有姓名输入框
- **修改后**：姓名输入框 + 称谓选择器
- **称谓选项**：先生、女士
- **默认值**：先生

## 📋 数据结构

### 年级选择数据
```javascript
gradeOptions: [
  [
    {name: '小学', value: 'primary'},
    {name: '初中', value: 'middle'},
    {name: '高中', value: 'high'},
    {name: '大学', value: 'university'},
    {name: '其他', value: 'other'}
  ],
  [
    {name: '一年级', value: '1'},
    {name: '二年级', value: '2'},
    // ...动态变化
  ]
]
```

### 周几选择数据
```javascript
weekdays: [
  {name: '周一', value: '1'},
  {name: '周二', value: '2'},
  // ...
  {name: '周日', value: '7'}
]
```

### 科目API响应
```json
{
  "code": 200,
  "data": [
    {"id": 1, "name": "语文"},
    {"id": 2, "name": "数学"}
  ]
}
```

## 🎨 界面效果

### 年级选择
```
┌─────────────────────────────────┐
│ 学生年级 *                       │
│ ┌─────────────────────────────┐ │
│ │ 小学 ▼    │ 三年级 ▼        │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

### 可辅导时间
```
┌─────────────────────────────────┐
│ 可辅导时间 *                     │
│ [周一] [周二] [周三] [周四]       │
│ [周五] [周六] [周日]             │
└─────────────────────────────────┘
```

### 可辅导时间段
```
┌─────────────────────────────────┐
│ 可辅导时间段 *                   │
│ ┌─────────┐    ┌─────────┐     │
│ │开始时间  │ 至 │结束时间  │     │
│ │ 09:00  │    │ 17:00  │     │
│ └─────────┘    └─────────┘     │
└─────────────────────────────────┘
```

### 联系人信息
```
┌─────────────────────────────────┐
│ 联系人 *                        │
│ ┌─────────────┐ ┌─────────┐    │
│ │ 张三        │ │ 先生 ▼  │    │
│ └─────────────┘ └─────────┘    │
│                                │
│ 联系电话 *                      │
│ ┌─────────────────────────────┐ │
│ │ 138****8888 (不可编辑)      │ │
│ └─────────────────────────────┘ │
│ 默认为登录手机号                 │
└─────────────────────────────────┘
```

## 🔧 技术实现

### 前端关键方法
```javascript
// 年级列变化处理
onGradeColumnChange(e) {
  const { column, value } = e.detail;
  if (column === 0) {
    // 更新第二列选项
    this.updateGradeSecondColumn(value);
  }
}

// 周几切换
toggleWeekday(e) {
  const { value } = e.currentTarget.dataset;
  // 切换选中状态
}

// 时间选择
onStartTimeChange(e) {
  this.setData({
    'formData.startTime': e.detail.value
  });
}
```

### 后端接口
```java
@GetMapping("/subjects")
public AjaxResult getSubjects() {
    Subject subject = new Subject();
    subject.setStatus("0");
    List<Subject> list = subjectService.selectSubjectList(subject);
    return AjaxResult.success(list);
}
```

## 🧪 测试要点

### 功能测试
1. **年级选择**：
   - 选择不同学段，第二列选项正确更新
   - 最终显示格式正确（如"小学三年级"）

2. **科目选择**：
   - 从后台正确加载科目列表
   - API失败时使用默认列表

3. **时间选择**：
   - 周几可以多选/取消选择
   - 时间段选择器正常工作
   - 选中状态正确显示

4. **联系人信息**：
   - 手机号不可编辑，显示登录手机号
   - 称谓选择器正常工作

### 兼容性测试
- 不同机型的时间选择器显示
- 多选按钮的触摸响应
- 二级选择器的滚动体验

## 📁 修改文件清单

### 前端文件
- `pages/parent/publish/index.wxml` - 界面结构
- `pages/parent/publish/index.js` - 逻辑处理
- `pages/parent/publish/index.wxss` - 样式定义
- `api/parent.js` - API接口

### 后端文件
- `AppletConfigController.java` - 配置接口控制器

## ✅ 验收标准

1. **年级选择**：二级选择器正常工作，显示格式正确
2. **科目配置**：从后台动态加载，有降级方案
3. **时间选择**：周几多选，时间段选择器正常
4. **手机号**：不可编辑，显示登录手机号
5. **联系人**：姓名+称谓组合输入
6. **样式美观**：所有新增组件样式协调统一
7. **交互流畅**：选择操作响应及时，状态反馈清晰

现在发布家教页面已经按照要求完成升级，提供了更好的用户体验和更灵活的配置能力！

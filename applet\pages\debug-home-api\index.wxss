.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 10rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.retry-btn {
  background: #6c7ae0;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
}

.test-results {
  margin-bottom: 30rpx;
}

.test-item {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #ddd;
}

.test-item.success {
  border-left-color: #52c41a;
}

.test-item.error {
  border-left-color: #ff4d4f;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.test-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.test-status {
  font-size: 32rpx;
}

.test-details {
  margin-bottom: 15rpx;
}

.detail-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.data-section {
  margin-top: 15rpx;
}

.data-title {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.data-content {
  background: #f8f9fa;
  border-radius: 6rpx;
  padding: 15rpx;
  max-height: 300rpx;
  overflow-y: auto;
}

.data-text {
  font-size: 22rpx;
  color: #666;
  word-break: break-all;
  white-space: pre-wrap;
}

.copy-btn {
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.loading, .empty {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 26rpx;
}

.instructions {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 30rpx;
}

.instruction-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.instruction-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  padding-left: 20rpx;
}

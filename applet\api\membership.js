import { request } from '../utils/request.js';

// 获取会员信息
export function getMembershipInfo() {
  return request({
    url: '/applet/teacher/membership/info',
    method: 'get'
  });
}

// 获取VIP费用配置
export function getVipFeeConfig() {
  return request({
    url: '/applet/config/value',
    method: 'get',
    data: { configKey: 'teacher.vip.fee' }
  });
}

// 创建会员支付订单
export function createMembershipPayOrder(data) {
  return request({
    url: '/applet/teacher/membership/createPayOrder',
    method: 'post',
    data: data
  });
}

// 升级为高级会员（保留原有接口）
export function upgradeToVip(data) {
  return request({
    url: '/applet/teacher/membership/upgrade',
    method: 'post',
    data: data
  });
}

// 获取会员权益说明
export function getMembershipBenefits() {
  return request({
    url: '/applet/teacher/membership/benefits',
    method: 'get'
  });
}

// 获取会员订单记录
export function getMembershipOrders() {
  return request({
    url: '/applet/teacher/membership/orders',
    method: 'get'
  });
}

// pages/parent/announcement/detail/index.js
Page({
  data: {
    announcement: {}
  },

  onLoad(options) {
    // 从页面参数中获取公告信息
    if (options.title && options.content) {
      this.setData({
        announcement: {
          title: decodeURIComponent(options.title),
          content: decodeURIComponent(options.content),
          publishTime: options.publishTime ? decodeURIComponent(options.publishTime) : '刚刚'
        }
      });
    }
  }
});

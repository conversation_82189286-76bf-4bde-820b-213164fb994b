package com.ruoyi.education.mapper;

import java.util.List;
import com.ruoyi.education.domain.TutorOrder;

/**
 * 家教订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
public interface TutorOrderMapper 
{
    /**
     * 查询家教订单
     * 
     * @param id 家教订单主键
     * @return 家教订单
     */
    public TutorOrder selectTutorOrderById(Long id);

    /**
     * 查询家教订单列表
     * 
     * @param tutorOrder 家教订单
     * @return 家教订单集合
     */
    public List<TutorOrder> selectTutorOrderList(TutorOrder tutorOrder);

    /**
     * 新增家教订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int insertTutorOrder(TutorOrder tutorOrder);

    /**
     * 修改家教订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int updateTutorOrder(TutorOrder tutorOrder);

    /**
     * 删除家教订单
     * 
     * @param id 家教订单主键
     * @return 结果
     */
    public int deleteTutorOrderById(Long id);

    /**
     * 批量删除家教订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTutorOrderByIds(Long[] ids);

    /**
     * 订单审核
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int auditTutorOrder(TutorOrder tutorOrder);

    /**
     * 选择教师
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int selectTeacher(TutorOrder tutorOrder);

    /**
     * 设置置顶
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int setTop(TutorOrder tutorOrder);

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 家教订单
     */
    public TutorOrder selectTutorOrderByOrderNo(String orderNo);

    /**
     * 统计订单数量（按状态）
     * 
     * @param status 订单状态
     * @return 订单数量
     */
    public int countOrdersByStatus(String status);

    /**
     * 查询待审核订单列表
     * 
     * @return 待审核订单集合
     */
    public List<TutorOrder> selectPendingAuditOrders();

    /**
     * 查询置顶订单列表
     * 
     * @return 置顶订单集合
     */
    public List<TutorOrder> selectTopOrders();

    /**
     * 查询小程序端-新家教订单列表
     * @return
     */
    public List<TutorOrder> selectNewTutorOrderList();

    /**
     * 查询小程序端-教师已报名的订单列表
     * @param teacherId
     * @return
     */
    public List<TutorOrder> selectAppliedTutorOrderListByTeacherId(@org.apache.ibatis.annotations.Param("teacherId") Long teacherId);

    /**
     * 查询小程序端-教师已完成的订单列表
     * @param teacherId
     * @return
     */
    public List<TutorOrder> selectCompletedTutorOrderListByTeacherId(@org.apache.ibatis.annotations.Param("teacherId") Long teacherId);

    /**
     * 查询小程序端-教师进行中的订单列表
     * @param teacherId
     * @return
     */
    public List<TutorOrder> selectOngoingTutorOrderListByTeacherId(@org.apache.ibatis.annotations.Param("teacherId") Long teacherId);

    /**
     * 将订单状态流转为进行中
     * @param orderId
     * @return
     */
    public int updateOrderStatusToOngoing(@org.apache.ibatis.annotations.Param("orderId") Long orderId);

    /**
     * 将订单状态流转为已完成
     * @param orderId
     * @return
     */
    public int updateOrderStatusToCompleted(@org.apache.ibatis.annotations.Param("orderId") Long orderId);

    /**
     * 查询教师未选中的订单列表（已报名但未被选中）
     * @param teacherId 教师ID
     * @return 未选中订单集合
     */
    public List<TutorOrder> selectUnselectedTutorOrderListByTeacherId(@org.apache.ibatis.annotations.Param("teacherId") Long teacherId);

    /**
     * 统计家长发布的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    public int countOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId);

    /**
     * 统计家长进行中的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    public int countOngoingOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId);

    /**
     * 统计家长已完成的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    public int countCompletedOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId);

    /**
     * 查询家长最近的订单
     * @param parentId 家长ID
     * @param limit 限制数量
     * @return 订单列表
     */
    public List<TutorOrder> selectRecentOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId, @org.apache.ibatis.annotations.Param("limit") int limit);

    /**
     * 获取推荐教师
     * @param limit 限制数量
     * @return 推荐教师列表
     */
    public List<java.util.Map<String, Object>> getRecommendTeachers(@org.apache.ibatis.annotations.Param("limit") int limit);

    /**
     * 简单测试查询
     * @return 教师列表
     */
    public List<java.util.Map<String, Object>> getSimpleTeachers();

    /**
     * 查询家长发布的所有订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    public List<TutorOrder> selectOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId);

    /**
     * 查询家长进行中的订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    public List<TutorOrder> selectOngoingOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId);

    /**
     * 查询家长已完成的订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    public List<TutorOrder> selectCompletedOrdersByParentId(@org.apache.ibatis.annotations.Param("parentId") Long parentId);
}
-- 调试生源接口查询的SQL

-- 1. 查看所有订单的基本信息
SELECT 
    id,
    order_no,
    subject_name,
    status,
    selected_teacher_id,
    publisher_type,
    deleted,
    create_time
FROM tutor_order 
ORDER BY create_time DESC 
LIMIT 10;

-- 2. 查看符合生源条件的订单（这是实际的查询条件）
SELECT 
    id,
    order_no,
    subject_name,
    status,
    selected_teacher_id,
    publisher_type,
    deleted,
    create_time
FROM tutor_order 
WHERE status = '2'  -- 审核通过，报名中状态
  AND selected_teacher_id IS NULL  -- 还未选择教师
  AND (deleted IS NULL OR deleted = 0)  -- 未删除的订单
ORDER BY is_top DESC, create_time DESC;

-- 3. 分别检查每个条件
-- 3.1 检查状态为2的订单
SELECT COUNT(*) as status_2_count FROM tutor_order WHERE status = '2';

-- 3.2 检查selected_teacher_id为null的订单
SELECT COUNT(*) as null_teacher_count FROM tutor_order WHERE selected_teacher_id IS NULL;

-- 3.3 检查未删除的订单
SELECT COUNT(*) as not_deleted_count FROM tutor_order WHERE (deleted IS NULL OR deleted = 0);

-- 3.4 检查同时满足所有条件的订单
SELECT COUNT(*) as final_count FROM tutor_order 
WHERE status = '2' 
  AND selected_teacher_id IS NULL 
  AND (deleted IS NULL OR deleted = 0);

-- 4. 查看管理员发布的订单
SELECT 
    id,
    order_no,
    subject_name,
    status,
    selected_teacher_id,
    publisher_type,
    deleted,
    create_time
FROM tutor_order 
WHERE publisher_type = '2'  -- 管理员发布
ORDER BY create_time DESC;

-- 5. 检查字段是否存在（如果字段不存在会报错）
DESCRIBE tutor_order;

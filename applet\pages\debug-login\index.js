// pages/debug-login/index.js
const { request } = require('../../utils/request.js');

Page({
  data: {
    testResults: []
  },

  onLoad() {
    this.testLoginAPI();
  },

  /**
   * 测试登录接口
   */
  async testLoginAPI() {
    const results = [];
    
    // 测试1: 检查基础连通性
    try {
      results.push('开始测试登录接口...');
      
      const loginData = {
        code: 'mock_code',
        userType: 'teacher',
        nickName: '测试用户',
        avatar: ''
      };
      
      results.push('请求数据: ' + JSON.stringify(loginData));
      results.push('请求URL: /applet/auth/login');
      
      const res = await request({
        url: '/applet/auth/login',
        method: 'POST',
        data: loginData
      });
      
      results.push('✅ 登录接口调用成功');
      results.push('响应数据: ' + JSON.stringify(res));
      
    } catch (error) {
      results.push('❌ 登录接口调用失败');
      results.push('错误信息: ' + JSON.stringify(error));
      
      // 详细错误分析
      if (error.statusCode) {
        results.push('HTTP状态码: ' + error.statusCode);
      }
      if (error.errMsg) {
        results.push('错误消息: ' + error.errMsg);
      }
    }
    
    this.setData({ testResults: results });
  },

  /**
   * 测试基础连通性
   */
  async testConnectivity() {
    const results = [...this.data.testResults];
    
    try {
      results.push('\n--- 测试基础连通性 ---');
      
      // 尝试访问一个简单的接口
      const res = await request({
        url: '/applet/config/value?configKey=system.name',
        method: 'GET'
      });
      
      results.push('✅ 基础连通性正常');
      results.push('配置接口响应: ' + JSON.stringify(res));
      
    } catch (error) {
      results.push('❌ 基础连通性失败');
      results.push('错误: ' + JSON.stringify(error));
    }
    
    this.setData({ testResults: results });
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({ testResults: [] });
  },

  /**
   * 重新测试
   */
  retestLogin() {
    this.clearResults();
    this.testLoginAPI();
  }
});

/* pages/parent/profile/edit/index.wxss */

.page-container {
  background-color: #f6f7f8;
  min-height: 100vh;
}

/* 表单容器 */
.form-container {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #999;
}

/* 输入框 */
.input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.input:focus {
  background: white;
  border-color: #667eea;
}

.input::placeholder {
  color: #999;
}

/* 只读文本 */
.readonly-text {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
}

/* 保存按钮区域 */
.save-section {
  padding: 40rpx 30rpx;
  background: white;
  margin: 40rpx 30rpx 0;
  border-radius: 20rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.save-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.save-btn[loading] {
  opacity: 0.6;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .form-container,
  .save-section {
    margin: 20rpx 20rpx;
    padding: 30rpx 20rpx;
  }
}

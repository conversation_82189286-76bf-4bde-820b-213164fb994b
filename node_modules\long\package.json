{"_from": "long@^5.2.1", "_id": "long@5.3.2", "_inBundle": false, "_integrity": "sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==", "_location": "/long", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "long@^5.2.1", "name": "long", "escapedName": "long", "rawSpec": "^5.2.1", "saveSpec": null, "fetchSpec": "^5.2.1"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmmirror.com/long/-/long-5.3.2.tgz", "_shasum": "1d84463095999262d7d7b7f8bfd4a8cc55167f83", "_spec": "long@^5.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\node_modules\\mysql2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A Long class for representing a 64-bit two's-complement integer value.", "devDependencies": {"esm2umd": "^0.3.1", "prettier": "^3.5.0", "typescript": "^5.7.3"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "files": ["index.js", "index.d.ts", "types.d.ts", "umd/index.js", "umd/index.d.ts", "umd/types.d.ts", "umd/package.json", "LICENSE", "README.md"], "homepage": "https://github.com/dcodeIO/long.js#readme", "keywords": ["math", "long", "int64"], "license": "Apache-2.0", "main": "umd/index.js", "name": "long", "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "scripts": {"build": "node scripts/build.js", "format": "prettier --write .", "lint": "prettier --check .", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json", "test:unit": "node tests"}, "type": "module", "types": "umd/index.d.ts", "version": "5.3.2"}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.PlatformConfigMapper">

    <resultMap type="PlatformConfig" id="PlatformConfigResult">
        <result property="id"    column="id"    />
        <result property="configKey"    column="config_key"    />
        <result property="configValue"    column="config_value"    />
        <result property="configType"    column="config_type"    />
        <result property="configName"    column="config_name"    />
        <result property="configDesc"    column="config_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlatformConfigVo">
        select id, config_key, config_value, config_type, config_name, config_desc, create_by, create_time, update_by, update_time from platform_config
    </sql>

    <select id="selectPlatformConfigList" parameterType="PlatformConfig" resultMap="PlatformConfigResult">
        <include refid="selectPlatformConfigVo"/>
        <where>
            <if test="configKey != null  and configKey != ''"> and config_key like concat('%', #{configKey}, '%')</if>
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
        </where>
    </select>

    <select id="selectPlatformConfigById" parameterType="Long" resultMap="PlatformConfigResult">
        <include refid="selectPlatformConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectPlatformConfigByKey" parameterType="String" resultMap="PlatformConfigResult">
        <include refid="selectPlatformConfigVo"/>
        where config_key = #{configKey}
    </select>

    <select id="checkConfigKeyUnique" parameterType="String" resultMap="PlatformConfigResult">
        <include refid="selectPlatformConfigVo"/>
        where config_key = #{configKey} limit 1
    </select>

    <insert id="insertPlatformConfig" parameterType="PlatformConfig" useGeneratedKeys="true" keyProperty="id">
        insert into platform_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configKey != null and configKey != ''">config_key,</if>
            <if test="configValue != null">config_value,</if>
            <if test="configType != null and configType != ''">config_type,</if>
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="configDesc != null">config_desc,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configKey != null and configKey != ''">#{configKey},</if>
            <if test="configValue != null">#{configValue},</if>
            <if test="configType != null and configType != ''">#{configType},</if>
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="configDesc != null">#{configDesc},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updatePlatformConfig" parameterType="PlatformConfig">
        update platform_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configKey != null and configKey != ''">config_key = #{configKey},</if>
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="configType != null and configType != ''">config_type = #{configType},</if>
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="configDesc != null">config_desc = #{configDesc},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlatformConfigById" parameterType="Long">
        delete from platform_config where id = #{id}
    </delete>

    <delete id="deletePlatformConfigByIds" parameterType="String">
        delete from platform_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 
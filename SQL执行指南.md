# 教师会员表SQL执行指南

## 🎯 执行步骤

### 1. 选择SQL脚本
**强烈推荐使用 `教师会员表_安全版.sql`**，该脚本：
- ✅ 避免了MySQL版本兼容性问题
- ✅ 分步执行，错误提示清晰
- ✅ 避免了字段不存在的错误
- ✅ 包含完整的验证步骤

### 2. 执行前准备
```bash
# 1. 备份数据库（重要！）
mysqldump -u root -p your_database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 连接到MySQL
mysql -u root -p your_database_name
```

### 3. 执行SQL脚本
```bash
# 方式1：在MySQL命令行中执行（推荐）
mysql> source /path/to/教师会员表_安全版.sql;

# 方式2：使用命令行直接执行
mysql -u root -p your_database_name < 教师会员表_安全版.sql
```

## ⚠️ 可能遇到的错误及解决方案

### 错误1：Duplicate column name 'membership_type'
```
ERROR 1060 (42S21): Duplicate column name 'membership_type'
```
**原因**：字段已存在  
**解决**：这是正常的，说明字段已经添加过了，可以忽略此错误

### 错误2：Duplicate key name 'idx_membership_type'
```
ERROR 1061 (42000): Duplicate key name 'idx_membership_type'
```
**原因**：索引已存在  
**解决**：这是正常的，说明索引已经创建过了，可以忽略此错误

### 错误3：Unknown column 'm.status' in 'where clause'
```
ERROR 1054 (42S22): Unknown column 'm.status' in 'where clause'
```
**原因**：尝试同步更新时引用了不存在的字段
**解决**：使用`教师会员表_安全版.sql`，该脚本避免了此问题

### 错误4：Table 'teacher_info' doesn't exist
```
ERROR 1146 (42S02): Table 'teacher_info' doesn't exist
```
**原因**：teacher_info表不存在
**解决**：请先确保基础的teacher_info表已创建

## 🔍 执行后验证

### 1. 检查表是否创建成功
```sql
-- 查看teacher_membership表
SHOW TABLES LIKE 'teacher_membership';
DESCRIBE teacher_membership;

-- 查看teacher_info表的新字段
DESCRIBE teacher_info;
```

### 2. 检查数据初始化
```sql
-- 查看会员类型分布
SELECT 
    membership_type,
    CASE 
        WHEN membership_type = '1' THEN '普通会员'
        WHEN membership_type = '2' THEN '高级会员'
        ELSE '未知类型'
    END as membership_name,
    COUNT(*) as count
FROM teacher_info 
GROUP BY membership_type;
```

### 3. 测试插入会员记录
```sql
-- 插入一条测试会员记录
INSERT INTO teacher_membership (
    teacher_id, 
    membership_type, 
    membership_date, 
    payment_amount, 
    payment_status, 
    payment_time, 
    order_no, 
    status, 
    create_by, 
    create_time
) VALUES (
    1, 
    '2', 
    NOW(), 
    199.00, 
    '1', 
    NOW(), 
    'TEST202412300001', 
    '1', 
    'admin', 
    NOW()
);

-- 验证插入结果
SELECT * FROM teacher_membership WHERE teacher_id = 1;
```

## 📋 执行清单

- [ ] 1. 数据库备份完成
- [ ] 2. 确认teacher_info表存在
- [ ] 3. 执行教师会员表_简化版.sql
- [ ] 4. 验证teacher_membership表创建成功
- [ ] 5. 验证teacher_info表新增字段
- [ ] 6. 检查数据初始化结果
- [ ] 7. 测试插入会员记录
- [ ] 8. 执行VIP费用配置.sql

## 🎉 执行完成标志

当您看到以下输出时，说明执行成功：
```
+----------------------------------+
| result                           |
+----------------------------------+
| 教师会员表创建完成！              |
+----------------------------------+
```

## 📞 遇到问题？

如果遇到其他错误，请：
1. 检查MySQL版本（建议5.7+）
2. 确认数据库连接正常
3. 检查用户权限（需要CREATE、ALTER权限）
4. 查看完整的错误信息

执行成功后，您就可以使用教师会员功能了！

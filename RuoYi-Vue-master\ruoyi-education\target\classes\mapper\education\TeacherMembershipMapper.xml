<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherMembershipMapper">
    
    <resultMap type="TeacherMembership" id="TeacherMembershipResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="membershipType"    column="membership_type"    />
        <result property="membershipDate"    column="membership_date"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="paymentStatus"    column="payment_status"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="orderNo"    column="order_no"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTeacherMembershipVo">
        select id, teacher_id, membership_type, membership_date, payment_amount, payment_status, payment_time, create_by, create_time, update_by, update_time, remark from teacher_membership
    </sql>

    <sql id="selectTeacherMembershipVoWithOptionalFields">
        select id, teacher_id, membership_type, membership_date, payment_amount, payment_status, payment_time,
               NULL as order_no, '1' as status,
               create_by, create_time, update_by, update_time, remark
        from teacher_membership
    </sql>

    <select id="selectTeacherMembershipList" parameterType="TeacherMembership" resultMap="TeacherMembershipResult">
        <include refid="selectTeacherMembershipVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="membershipType != null  and membershipType != ''"> and membership_type = #{membershipType}</if>
            <if test="membershipDate != null "> and membership_date = #{membershipDate}</if>
            <if test="paymentAmount != null "> and payment_amount = #{paymentAmount}</if>
            <if test="paymentStatus != null  and paymentStatus != ''"> and payment_status = #{paymentStatus}</if>
            <if test="paymentTime != null "> and payment_time = #{paymentTime}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTeacherMembershipById" parameterType="Long" resultMap="TeacherMembershipResult">
        <include refid="selectTeacherMembershipVo"/>
        where id = #{id}
    </select>

    <select id="selectActiveByTeacherId" parameterType="Long" resultMap="TeacherMembershipResult">
        <include refid="selectTeacherMembershipVoWithOptionalFields"/>
        where teacher_id = #{teacherId}
        and payment_status = '1'
        order by membership_date desc
        limit 1
    </select>
        
    <insert id="insertTeacherMembership" parameterType="TeacherMembership" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_membership
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="membershipType != null">membership_type,</if>
            <if test="membershipDate != null">membership_date,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="membershipType != null">#{membershipType},</if>
            <if test="membershipDate != null">#{membershipDate},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTeacherMembership" parameterType="TeacherMembership">
        update teacher_membership
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="membershipType != null">membership_type = #{membershipType},</if>
            <if test="membershipDate != null">membership_date = #{membershipDate},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherMembershipById" parameterType="Long">
        delete from teacher_membership where id = #{id}
    </delete>

    <delete id="deleteTeacherMembershipByIds" parameterType="String">
        delete from teacher_membership where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

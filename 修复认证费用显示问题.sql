-- 修复认证费用显示问题的SQL脚本

-- 1. 检查当前配置值
SELECT 
    '=== 当前配置 ===' as info,
    config_key,
    config_value,
    config_name,
    config_desc,
    create_time,
    update_time
FROM platform_config 
WHERE config_key = 'teacher.certification.fee';

-- 2. 更新认证费用配置为99.00
UPDATE platform_config 
SET 
    config_value = '99.00',
    config_name = '教师认证费用',
    config_desc = '教师进行身份认证需要缴纳的费用金额',
    update_time = NOW()
WHERE config_key = 'teacher.certification.fee';

-- 3. 如果配置不存在，则插入
INSERT INTO platform_config (
    config_key, 
    config_value, 
    config_name, 
    config_desc, 
    config_type, 
    create_time, 
    update_time
) 
SELECT 
    'teacher.certification.fee', 
    '99.00', 
    '教师认证费用', 
    '教师进行身份认证需要缴纳的费用金额', 
    'system', 
    NOW(), 
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM platform_config WHERE config_key = 'teacher.certification.fee'
);

-- 4. 验证更新结果
SELECT 
    '=== 更新后配置 ===' as info,
    config_key,
    config_value,
    config_name,
    config_desc,
    update_time
FROM platform_config 
WHERE config_key = 'teacher.certification.fee';

-- 5. 检查教师缴费状态（用于测试）
SELECT 
    '=== 教师缴费状态 ===' as info,
    id,
    real_name,
    phone_number,
    certification_status,
    certification_fee_paid,
    CASE certification_fee_paid
        WHEN '0' THEN '未缴纳'
        WHEN '1' THEN '已缴纳'
        ELSE '未知状态'
    END as fee_status_desc
FROM teacher_info 
WHERE id = 30;  -- 根据日志中的教师ID

-- 6. 显示操作提示
SELECT 
    '=== 操作提示 ===' as info,
    '1. 执行完SQL后，需要调用清除缓存接口' as step1,
    'GET /applet/teacher/certification/clear-cache' as cache_api,
    '2. 然后重新测试认证费用接口' as step2,
    'GET /applet/teacher/certification/fee-info' as test_api;

-- 教师会员表创建脚本（手动执行版）
-- 请根据提示手动执行需要的语句

-- ==========================================
-- 第一步：创建教师会员表（必须执行）
-- ==========================================
DROP TABLE IF EXISTS `teacher_membership`;
CREATE TABLE `teacher_membership` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `teacher_id` bigint(20) NOT NULL COMMENT '教师ID',
  `membership_type` varchar(10) NOT NULL DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）',
  `membership_date` datetime NOT NULL COMMENT '成为会员日期',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `payment_status` varchar(10) NOT NULL DEFAULT '1' COMMENT '支付状态（0未支付 1已支付 2已退款）',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `status` varchar(10) DEFAULT '1' COMMENT '状态（0无效 1有效）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_membership_type` (`membership_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_membership_date` (`membership_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师会员表';

-- ==========================================
-- 第二步：检查teacher_info表字段（必须执行）
-- ==========================================
DESCRIBE teacher_info;

-- ==========================================
-- 第三步：根据需要添加字段（按需执行）
-- ==========================================

-- 如果上面的DESCRIBE结果中没有membership_type字段，请执行：
-- ALTER TABLE teacher_info ADD COLUMN membership_type varchar(10) DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）';

-- 如果上面的DESCRIBE结果中没有membership_date字段，请执行：
-- ALTER TABLE teacher_info ADD COLUMN membership_date datetime DEFAULT NULL COMMENT '成为会员日期';

-- ==========================================
-- 第四步：创建索引（按需执行）
-- ==========================================

-- 如果membership_type字段是新添加的，请执行：
-- ALTER TABLE teacher_info ADD INDEX idx_membership_type (membership_type);

-- 如果membership_date字段是新添加的，请执行：
-- ALTER TABLE teacher_info ADD INDEX idx_membership_date (membership_date);

-- ==========================================
-- 第五步：初始化数据（必须执行）
-- ==========================================
UPDATE teacher_info 
SET membership_type = '1' 
WHERE membership_type IS NULL OR membership_type = '';

-- ==========================================
-- 第六步：验证结果（必须执行）
-- ==========================================

-- 检查表结构
SELECT '=== teacher_membership表结构 ===' as info;
DESCRIBE teacher_membership;

SELECT '=== teacher_info表会员相关字段 ===' as info;
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME IN ('membership_type', 'membership_date')
ORDER BY COLUMN_NAME;

-- 检查数据分布
SELECT '=== 教师会员类型分布 ===' as info;
SELECT 
    membership_type,
    CASE 
        WHEN membership_type = '1' THEN '普通会员'
        WHEN membership_type = '2' THEN '高级会员'
        ELSE '未知类型'
    END as membership_name,
    COUNT(*) as teacher_count
FROM teacher_info 
WHERE membership_type IS NOT NULL
GROUP BY membership_type;

SELECT '🎉 处理完成！' as result;

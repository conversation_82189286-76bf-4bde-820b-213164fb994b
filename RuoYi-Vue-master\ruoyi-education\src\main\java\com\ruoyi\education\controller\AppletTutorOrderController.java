package com.ruoyi.education.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.domain.OrderApplication;
import com.ruoyi.education.service.ITutorOrderService;
import com.ruoyi.education.service.IOrderApplicationService;
import com.ruoyi.education.service.ITeacherIncomeService;
import com.ruoyi.education.service.ITeacherMembershipService;
import com.ruoyi.education.service.ITeacherInfoService;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.math.BigDecimal;

/**
 * 小程序端-家教订单Controller
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/applet/teacher/order")
public class AppletTutorOrderController extends BaseController
{
    @Autowired
    private ITutorOrderService tutorOrderService;

    @Autowired
    private IOrderApplicationService orderApplicationService;

    @Autowired
    private ITeacherIncomeService teacherIncomeService;

    @Autowired
    private ITeacherMembershipService teacherMembershipService;

    @Autowired
    private ITeacherInfoService teacherInfoService;

    /**
     * 查询新家教订单列表 (发布中)
     */
    @GetMapping("/new")
    public TableDataInfo listNewOrders() {
        System.out.println("=== 开始查询新家教订单列表 ===");
        startPage();
        List<TutorOrder> list = tutorOrderService.selectNewTutorOrderList();
        System.out.println("=== 查询到的订单数量: " + (list != null ? list.size() : 0) + " ===");
        if (list != null && !list.isEmpty()) {
            for (TutorOrder order : list) {
                System.out.println("订单ID: " + order.getId() + ", 状态: " + order.getStatus() +
                    ", 选中教师ID: " + order.getSelectedTeacherId() + ", 发布者类型: " + order.getPublisherType());
            }
        }
        TableDataInfo result = getDataTable(list);
        System.out.println("=== 返回结果: total=" + result.getTotal() + ", rows=" + result.getRows().size() + " ===");
        return result;
    }

    /**
     * 查询已报名的家教订单列表
     */
    @GetMapping("/applied")
    public TableDataInfo listAppliedOrders() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return getDataTable(new java.util.ArrayList<>());
        }
        Long teacherId = loginUser.getTeacherId();
        startPage();
        List<TutorOrder> list = tutorOrderService.selectAppliedTutorOrderListByTeacherId(teacherId);
        return getDataTable(list);
    }

    /**
     * 查询已完成的家教订单列表
     */
    @GetMapping("/completed")
    public TableDataInfo listCompletedOrders() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return getDataTable(new java.util.ArrayList<>());
        }
        Long teacherId = loginUser.getTeacherId();
        startPage();
        List<TutorOrder> list = tutorOrderService.selectCompletedTutorOrderListByTeacherId(teacherId);
        return getDataTable(list);
    }

    /**
     * 查询进行中的家教订单列表
     */
    @GetMapping("/ongoing")
    public TableDataInfo listOngoingOrders() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return getDataTable(new java.util.ArrayList<>());
        }
        Long teacherId = loginUser.getTeacherId();
        startPage();
        List<TutorOrder> list = tutorOrderService.selectOngoingTutorOrderListByTeacherId(teacherId);
        return getDataTable(list);
    }

    /**
     * 查询未选中的订单列表（已报名但未被选中）
     */
    @GetMapping("/unselected")
    public TableDataInfo listUnselectedOrders() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return getDataTable(new java.util.ArrayList<>());
        }
        Long teacherId = loginUser.getTeacherId();
        startPage();
        List<TutorOrder> list = tutorOrderService.selectUnselectedTutorOrderListByTeacherId(teacherId);
        return getDataTable(list);
    }

    /**
     * 完成试课，订单状态流转为“进行中”，自动生成试课收入记录（70%）
     */
    @PostMapping("/finishTrial/{orderId}")
    public AjaxResult finishTrial(@PathVariable Long orderId) {
        try {
            // 检查订单状态，防止重复操作
            TutorOrder order = tutorOrderService.selectTutorOrderById(orderId);
            if (order == null) {
                return AjaxResult.error("订单不存在");
            }

            // 只有试课中(6)或进行中(7)状态才能完成试课
            if (!"6".equals(order.getStatus()) && !"7".equals(order.getStatus())) {
                return AjaxResult.error("当前订单状态不允许完成试课操作");
            }

            // 检查是否已经生成过试课收入记录
            if (teacherIncomeService.hasTrialIncomeRecord(orderId)) {
                return AjaxResult.success("试课已完成，收入已生成");
            }

            // 计算订单总金额
            BigDecimal totalOrderAmount = calculateOrderTotalAmount(orderId);

            // 更新订单状态为进行中
            int result = tutorOrderService.updateOrderStatusToOngoing(orderId);
            if (result <= 0) {
                return AjaxResult.error("更新订单状态失败");
            }

            // 生成试课收入记录（70%）
            int incomeResult = teacherIncomeService.generateIncomeFromOrder(orderId, "trial", totalOrderAmount);
            if (incomeResult <= 0) {
                return AjaxResult.error("生成试课收入记录失败");
            }

            return AjaxResult.success("完成试课成功");
        } catch (Exception e) {
            System.err.println("完成试课异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("操作失败，请稍后重试");
        }
    }

    /**
     * 完成讲授一周，订单状态流转为“已完成”，自动生成一周收入记录（30%）
     */
    @PostMapping("/finishWeek/{orderId}")
    public AjaxResult finishWeek(@PathVariable Long orderId) {
        try {
            // 检查订单状态，防止重复操作
            TutorOrder order = tutorOrderService.selectTutorOrderById(orderId);
            if (order == null) {
                return AjaxResult.error("订单不存在");
            }

            // 只有进行中(7)状态才能完成讲授一周
            if (!"7".equals(order.getStatus())) {
                return AjaxResult.error("当前订单状态不允许完成讲授一周操作");
            }

            // 检查是否已经生成过一周收入记录
            if (teacherIncomeService.hasWeekIncomeRecord(orderId)) {
                return AjaxResult.success("讲授一周已完成，收入已生成");
            }

            // 计算订单总金额
            BigDecimal totalOrderAmount = calculateOrderTotalAmount(orderId);

            // 更新订单状态为已完成
            int result = tutorOrderService.updateOrderStatusToCompleted(orderId);
            if (result <= 0) {
                return AjaxResult.error("更新订单状态失败");
            }

            // 生成一周收入记录（30%）
            int incomeResult = teacherIncomeService.generateIncomeFromOrder(orderId, "week", totalOrderAmount);
            if (incomeResult <= 0) {
                return AjaxResult.error("生成一周收入记录失败");
            }

            // 更新教师接课成功统计和经历
            if (order.getSelectedTeacherId() != null) {
                teacherInfoService.updateTeacherSuccessStats(order.getSelectedTeacherId(), order);
            }

            return AjaxResult.success("完成讲授一周成功");
        } catch (Exception e) {
            System.err.println("完成讲授一周异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("操作失败，请稍后重试");
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    public AjaxResult getOrderDetail(@PathVariable Long orderId) {
        TutorOrder tutorOrder = tutorOrderService.selectTutorOrderById(orderId);
        return AjaxResult.success(tutorOrder);
    }

    /**
     * 教师报名订单
     */
    @PostMapping("/apply")
    public AjaxResult applyOrder(@RequestBody OrderApplication orderApplication) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }
            Long teacherId = loginUser.getTeacherId();
            orderApplication.setTeacherId(teacherId);

            // 检查是否为机构订单，如果是则需要验证会员权限
            TutorOrder order = tutorOrderService.selectTutorOrderById(orderApplication.getOrderId());
            if (order != null && "2".equals(order.getOrderType())) {
                // 机构订单（order_type=2），检查是否为高级会员
                if (!teacherMembershipService.isValidVip(teacherId)) {
                    return AjaxResult.error(403, "报名机构订单需要升级为高级会员");
                }
            }

            // 设置教师基本信息 (实际应该从教师表中查询)
            orderApplication.setTeacherName("张老师"); // TODO: 从教师表中获取真实姓名
            orderApplication.setTeacherPhone("13800138000"); // TODO: 从教师表中获取真实手机号

            int result = orderApplicationService.applyForOrder(orderApplication);
            return toAjax(result);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("报名失败，请稍后重试");
        }
    }

    /**
     * 查询当前教师对某订单的报名详情
     */
    @GetMapping("/application")
    public AjaxResult getOrderApplication(Long orderId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error(401, "登录信息失效，请重新登录");
        }
        Long teacherId = loginUser.getTeacherId();
        OrderApplication application = orderApplicationService.selectByOrderIdAndTeacherId(orderId, teacherId);
        return AjaxResult.success(application);
    }

    /**
     * 取消报名（状态变更）
     */
    @PostMapping("/application/cancel")
    public AjaxResult cancelOrderApplication(@RequestBody OrderApplication req) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error(401, "登录信息失效，请重新登录");
        }
        Long teacherId = loginUser.getTeacherId();
        Long orderId = req.getOrderId();
        // 设为已取消（如6）
        int result = orderApplicationService.cancelApplication(orderId, teacherId);
        return toAjax(result);
    }

    /**
     * 修改报名内容
     */
    @PostMapping("/application/update")
    public AjaxResult updateOrderApplication(@RequestBody OrderApplication req) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error(401, "登录信息失效，请重新登录");
        }
        Long teacherId = loginUser.getTeacherId();
        req.setTeacherId(teacherId);
        int result = orderApplicationService.updateApplicationByOrderIdAndTeacherId(req);
        return toAjax(result);
    }

    /**
     * 获取家长联系方式（被选中的教师专用）
     */
    @PostMapping("/getContactInfo/{applicationId}")
    public AjaxResult getContactInfo(@PathVariable Long applicationId) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 验证是否为当前教师的报名记录
            Long teacherId = loginUser.getTeacherId();
            OrderApplication application = orderApplicationService.selectOrderApplicationById(applicationId);
            if (application == null || !application.getTeacherId().equals(teacherId)) {
                return AjaxResult.error("无权限获取此联系方式");
            }

            // 检查是否为已选中状态
            if (!"4".equals(application.getStatus())) {
                return AjaxResult.error("只有被选中的教师才能获取联系方式");
            }

            int result = orderApplicationService.publishContactInfo(applicationId);
            return toAjax(result);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("获取联系方式失败，请稍后重试");
        }
    }

    /**
     * 计算订单总金额
     * 根据订单的期望薪资、每周次数、每次时长计算总金额
     *
     * @param orderId 订单ID
     * @return 订单总金额
     */
    private BigDecimal calculateOrderTotalAmount(Long orderId) {
        try {
            // 获取当前登录的教师ID
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long teacherId = loginUser.getTeacherId();

            // 获取教师对该订单的报名信息（包含期望薪资）
            OrderApplication application = orderApplicationService.selectByOrderIdAndTeacherId(orderId, teacherId);
            if (application == null) {
                throw new RuntimeException("未找到报名信息");
            }

            // 获取订单信息（包含每周次数、每次时长）
            TutorOrder order = tutorOrderService.selectTutorOrderById(orderId);
            if (order == null) {
                throw new RuntimeException("未找到订单信息");
            }

            // 计算订单总金额
            // 总金额 = 期望薪资(元/小时) × 每次时长(小时) × 每周次数 × 1周
            BigDecimal hourlyRate = application.getExpectedSalary(); // 期望薪资
            Integer weeklySessions = order.getWeeklySessions(); // 每周次数
            Integer sessionDuration = order.getSessionDuration(); // 每次时长（分钟）

            if (hourlyRate == null || weeklySessions == null || sessionDuration == null) {
                // 如果缺少必要信息，使用默认值
                return new BigDecimal("400.00");
            }

            // 转换为小时
            BigDecimal sessionHours = new BigDecimal(sessionDuration).divide(new BigDecimal("60"), 2, BigDecimal.ROUND_HALF_UP);

            // 计算总金额（按一周计算）
            BigDecimal totalAmount = hourlyRate
                .multiply(sessionHours)
                .multiply(new BigDecimal(weeklySessions)); // 1周

            return totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);

        } catch (Exception e) {
            System.err.println("计算订单总金额失败: " + e.getMessage());
            // 返回默认金额
            return new BigDecimal("1000.00");
        }
    }
}
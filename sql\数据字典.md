# 众桥辅导平台数据字典

## 📋 认证状态标准定义

### teacher_info.certification_status
| 值 | 含义 | 说明 |
|---|---|---|
| 0 | 未认证 | 初始状态 |
| 1 | 学生认证 | 已上传学生证 |
| 2 | 已认证 | 基础认证通过 |
| 4 | 已实名认证 | 最高认证级别 |

### 通用状态字段
| 字段名 | 值 | 含义 |
|---|---|---|
| status | 0 | 正常 |
| status | 1 | 停用 |
| deleted | 0 | 未删除 |
| deleted | 1 | 已删除 |

## 📊 核心表结构

### teacher_info (教师信息表)
| 字段名 | 类型 | 说明 | 示例 |
|---|---|---|---|
| id | bigint | 主键 | 1 |
| real_name | varchar(30) | 真实姓名 | 张三 |
| nick_name | varchar(30) | 昵称 | 小张老师 |
| certification_status | char(1) | 认证状态 | 4 |
| deleted | int | 逻辑删除 | 0 |

### parent_info (家长信息表)
| 字段名 | 类型 | 说明 | 示例 |
|---|---|---|---|
| id | bigint | 主键 | 1 |
| real_name | varchar(30) | 真实姓名 | 李四 |
| nick_name | varchar(30) | 昵称 | 李家长 |
| status | char(1) | 账号状态 | 0 |

### tutor_order (家教订单表)
| 字段名 | 类型 | 说明 | 示例 |
|---|---|---|---|
| id | bigint | 主键 | 1 |
| order_no | varchar(64) | 订单号 | ORDER_1234567890 |
| publisher_id | bigint | 发布者ID | 1 |
| publisher_type | char(1) | 发布者类型 | 1(家长) |
| preferred_teacher_id | bigint | 指定教师ID | 2 |

## 🔧 API接口规范

### 路径规范
```
/applet/teacher/*    # 教师端接口
/applet/parent/*     # 家长端接口
/system/*           # 管理端接口
```

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

## 📝 命名规范

### 数据库命名
- 表名：小写+下划线 (teacher_info)
- 字段名：小写+下划线 (real_name)
- 索引名：idx_表名_字段名 (idx_teacher_info_status)

### Java命名
- 类名：大驼峰 (TeacherInfo)
- 方法名：小驼峰 (getTeacherDetail)
- 变量名：小驼峰 (teacherId)

### 前端命名
- 页面目录：小写+下划线 (teacher_detail)
- 文件名：小写+下划线 (index.js)
- 变量名：小驼峰 (teacherDetail)

<view class="page-container tabbar-page">
  <!-- 顶部背景 -->
  <navigation-bar title="众桥辅导-教师版" back="{{false}}"></navigation-bar>

  <view class="content-container">
    <!-- 用户信息区 -->
    <view class="user-info-card" bind:tap="navigateToProfileDetail">
      <image class="avatar" src="{{teacherInfo.avatar || '/assets/images/avatar_placeholder.png'}}"></image>
      <view class="user-info">
        <text class="nickname">{{teacherInfo.name || '点击设置昵称'}}</text>
        <text class="detail-link">{{teacherInfo.profileText}}</text>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="card quick-actions-card">
      <view class="action-item" wx:for="{{quickActions}}" wx:key="text" bindtap="navigateTo" data-item="{{item}}">
        <image class="action-icon" src="{{item.icon}}"></image>
        <text class="action-text">{{item.text}}</text>
      </view>
    </view>

    <!-- 菜单列表 -->
    <view class="card menu-list-card">
      <view class="menu-item" wx:for="{{menuItems}}" wx:key="text" bindtap="navigateTo" data-item="{{item}}">
        <image class="menu-icon" src="{{item.icon}}"></image>
        <text class="menu-text">{{item.text}}</text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="card logout-card">
      <view class="logout-btn" bindtap="handleLogout">
        <text>退出登录</text>
      </view>
    </view>

  </view>
</view> 
/* pages/teacher/home/<USER>/

/* 页面基础样式 */
page {
  background-color: #f8f9fa;
  height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 30rpx;
  color: #000;
  background:white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding-top: calc(10rpx + var(--status-bar-height, 44px));
  height: 88rpx;
}

.location-selector {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  min-width: 100rpx;
}

.location-text {
  font-size: 26rpx;
  font-weight: 400;
  margin-right: 6rpx;
}

.location-arrow {
  font-size: 18rpx;
  transition: transform 0.3s ease;
}

.navbar-title {
  font-size: 34rpx;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-placeholder {
  width: 100rpx;
}

/* 页面内容 */
.page-content {
  margin-top: calc(110rpx + var(--status-bar-height, 44px));
  height: calc(100vh - 200rpx - env(safe-area-inset-bottom) - var(--status-bar-height, 44px));
  background-color: #f8f9fa;
}

/* 轮播图区域 */
.banner-section {
  margin: 0;
  background: white;
}

.banner-swiper {
  height: 320rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 通知公告区域 */
.announcement-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title-with-icon {
  display: flex;
  align-items: center;
}

.announcement-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.announcement-list {
  padding: 0;
}

.announcement-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-item:active {
  background-color: #f8f9fa;
}

.announcement-title {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.announcement-arrow {
  font-size: 28rpx;
  color: #999;
  font-weight: bold;
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 0 0 0;
  padding: 24rpx 30rpx 16rpx;
  background: white;
}
.section-title1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 0 0 0;
  padding: 10rpx 10rpx 10rpx;
}
.iconG{
  color:#6c7ae0;
}
.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.title-more {
  font-size: 24rpx;
  color: #6c7ae0;
}

/* 订单列表 */
.order-list {
  background: white;
  margin-top: 20rpx;
}

/* 状态样式 */
.empty-state,
.loading-state,
.no-more-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 20rpx;
  background: white;
}

.empty-image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.4;
}

.empty-text,
.loading-text,
.no-more-text {
  font-size: 26rpx;
  color: #999;
}

.loading-text {
  color: #666;
}

/* 动画效果 */
.location-selector:active {
  transform: scale(0.95);
}

/* 轮播图指示器自定义 */
.banner-swiper .wx-swiper-dots {
  bottom: 16rpx;
}

.banner-swiper .wx-swiper-dot {
  background: rgba(255, 255, 255, 0.6);
  width: 10rpx;
  height: 10rpx;
  margin: 0 6rpx;
}

.banner-swiper .wx-swiper-dot-active {
  background: white;
  width: 24rpx;
  border-radius: 5rpx;
}

/* 查看更多容器 */
.view-more-container {
  padding: 30rpx 20rpx;
  text-align: center;
}

.view-more-btn {
  background: #1296db;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(18, 150, 219, 0.3);
  transition: all 0.2s ease;
}

.view-more-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(18, 150, 219, 0.4);
}

/* 加载和空状态 */
.loading-state, .empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #999;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
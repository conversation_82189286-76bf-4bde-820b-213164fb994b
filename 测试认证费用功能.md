# 测试认证费用功能指南

## 问题修复总结

### 1. 解决的问题
- ✅ 修复了Bean名称冲突错误
- ✅ 使用现有的 `AppletCertificationFeeController`
- ✅ 更新了前端API调用路径
- ✅ 确保配置从正确的系统配置表获取

### 2. 修复内容
- **删除重复文件**：删除了冲突的Controller文件
- **更新API路径**：修改为 `/applet/teacher/certification/fee-info`
- **配置表修正**：使用 `sys_config` 表而不是 `platform_config` 表
- **完善SQL脚本**：提供正确的配置初始化脚本

## 测试步骤

### 步骤1：配置认证费用
```sql
-- 检查platform_config表中的配置
SELECT config_key, config_value, config_name FROM platform_config WHERE config_key = 'teacher.certification.fee';

-- 如果不存在或值为0，则更新配置
UPDATE platform_config
SET config_value = '99.00',
    update_time = NOW()
WHERE config_key = 'teacher.certification.fee';

-- 如果记录不存在，则插入
INSERT INTO platform_config (config_key, config_value, config_name, config_desc, config_type, create_time, update_time)
SELECT 'teacher.certification.fee', '99.00', '教师认证费用', '教师进行身份认证需要缴纳的费用金额', 'system', NOW(), NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM platform_config WHERE config_key = 'teacher.certification.fee'
);

-- 验证配置
SELECT config_key, config_value, config_name FROM platform_config WHERE config_key = 'teacher.certification.fee';
```

### 步骤2：启动后端服务
```bash
cd RuoYi-Vue-master/ruoyi-admin
mvn spring-boot:run
```

### 步骤3：测试配置获取
使用Postman或其他工具测试配置获取接口：

**测试配置获取**
```
GET /applet/teacher/certification/test-config
Headers: Authorization: Bearer {token}
```

预期响应：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "feeAmount": "99.00",
    "feeAmountWithDefault": "99.00",
    "isEmpty": false
  }
}
```

### 步骤4：测试认证费用接口
**获取认证费用信息**
```
GET /applet/teacher/certification/fee-info
Headers: Authorization: Bearer {token}
```

预期响应：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "feeAmount": "99.00",
    "feePaidStatus": "0"
  }
}
```

### 步骤5：测试报名校验
**测试报名接口**
```
POST /applet/teacher/order/apply
Headers: Authorization: Bearer {token}
Body: {
  "orderId": 1,
  "expectedSalary": "100",
  "timeNegotiation": "可协商",
  "advantage": "经验丰富"
}
```

如果未缴纳认证费用，预期响应：
```json
{
  "code": 500,
  "msg": "CERTIFICATION_FEE_NOT_PAID"
}
```

### 步骤6：测试小程序页面
1. 重新编译小程序
2. 登录教师账号
3. 访问认证费用页面
4. 检查费用是否正确显示为99.00
5. 尝试报名订单，验证校验机制

## 预期结果

### 1. 后端服务启动成功
- ✅ 无Bean冲突错误
- ✅ 服务正常启动
- ✅ 接口可正常访问

### 2. 认证费用正确显示
- ✅ 页面显示费用金额：¥99.00
- ✅ 缴费状态正确显示
- ✅ 页面样式现代化

### 3. 功能完整性
- ✅ 费用信息获取正常
- ✅ 支付订单创建功能
- ✅ 缴费状态管理
- ✅ 页面交互流畅

## 故障排除

### 问题1：仍然显示0.00
**原因**：配置未正确设置
**解决**：执行认证费用配置.sql脚本

### 问题2：接口404错误
**原因**：API路径错误
**解决**：确认使用 `/applet/teacher/certification/fee-info`

### 问题3：权限错误
**原因**：用户未登录或token过期
**解决**：重新登录获取有效token

### 问题4：数据库连接错误
**原因**：数据库配置问题
**解决**：检查数据库连接配置

## 技术细节

### 1. Controller路径
- 正确路径：`com.ruoyi.education.controller.applet.AppletCertificationFeeController`
- 接口路径：`/applet/teacher/certification/*`

### 2. 配置获取
- 使用：`ISysConfigService.selectConfigByKey("teacher.certification.fee")`
- 配置表：`sys_config`

### 3. 数据库字段
- 教师表：`teacher_info.certification_fee_paid`
- 值：'0'=未缴纳, '1'=已缴纳

### 4. 前端API
- 获取费用：`getCertificationFeeInfo()`
- 创建订单：`createCertificationFeeOrder()`

## 后续优化建议

1. **支付集成**：完善微信支付集成
2. **缴费记录**：添加详细的缴费记录管理
3. **通知功能**：缴费成功后的消息通知
4. **退费机制**：特殊情况下的退费处理
5. **审计日志**：记录所有缴费相关操作

## 联系支持

如果遇到问题，请提供：
1. 错误日志截图
2. 数据库配置信息
3. 接口测试结果
4. 小程序页面截图

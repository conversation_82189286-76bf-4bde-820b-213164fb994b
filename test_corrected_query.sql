-- 测试修正后的查询（使用正确的字段名）

-- 1. 检查表结构中的逻辑删除字段
DESCRIBE teacher_info;

-- 2. 查看所有教师的删除状态
SELECT id, nick_name, status, certification_status, deleted, del_flag FROM teacher_info;

-- 3. 使用修正后的查询条件
SELECT
    t.id,
    t.nick_name as name,
    t.real_name,
    t.avatar,
    t.university,
    t.major,
    t.grade,
    t.certification_status,
    CASE 
        WHEN t.certification_status = '1' THEN '学生认证'
        WHEN t.certification_status = '2' THEN '实名认证'
        ELSE '未认证'
    END as certification_text,
    COALESCE(t.teaching_subjects, '暂未设置') as subject,
    '经验丰富' as experience,
    IFNULL(t.rating, 4.5) as rating,
    COALESCE(t.self_introduction, '该教师暂未填写自我介绍') as features,
    '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三' as grades
FROM teacher_info t
WHERE t.status = '0' 
  AND t.certification_status IN ('1', '2') 
  AND t.deleted = 0
ORDER BY t.certification_status DESC, t.rating DESC, t.create_time DESC
LIMIT 10;

-- 4. 检查具体的数据值
SELECT 
    id,
    nick_name,
    status,
    certification_status,
    certification_fee_paid,
    deleted,
    CASE WHEN deleted = 0 THEN '未删除' ELSE '已删除' END as deleted_status,
    CASE WHEN status = '0' THEN '正常' ELSE '停用' END as status_text,
    CASE 
        WHEN certification_status = '0' THEN '未认证'
        WHEN certification_status = '1' THEN '学生认证'
        WHEN certification_status = '2' THEN '实名认证'
        ELSE '未知'
    END as cert_status_text
FROM teacher_info 
ORDER BY id;

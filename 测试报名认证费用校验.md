# 测试报名认证费用校验功能

## 功能说明
在教师报名订单时，系统会自动检查教师是否已缴纳认证费用：
- 如果未缴纳：显示提示弹窗，引导用户跳转到缴费页面
- 如果已缴纳：正常进行报名流程

## 修复内容

### 1. 后端错误处理
- 在 `OrderApplicationServiceImpl.applyForOrder()` 中检查认证费用缴纳状态
- 未缴纳时抛出特殊错误码：`CERTIFICATION_FEE_NOT_PAID`

### 2. 前端错误处理优化
- 修改 `request.js` 中的错误处理逻辑
- 对特殊错误码不显示通用Toast，允许自定义处理
- 确保错误信息正确传递到页面

### 3. 页面交互优化
- 订单详情页面：捕获特殊错误码，显示缴费引导弹窗
- 生源页面：修复数据获取逻辑，正确检查缴费状态

## 测试步骤

### 步骤1：设置测试环境
```sql
-- 将教师缴费状态设为未缴纳
UPDATE teacher_info 
SET certification_fee_paid = '0' 
WHERE id = 30;  -- 替换为实际的教师ID

-- 验证设置
SELECT id, real_name, certification_fee_paid FROM teacher_info WHERE id = 30;
```

### 步骤2：测试订单详情页面报名
1. 登录教师账号
2. 进入任意订单详情页面
3. 点击"立即报名"按钮
4. 预期结果：显示认证费用提示弹窗

**预期弹窗内容**：
```
标题：需要缴纳认证费用
内容：您还未缴纳认证费用，需要先缴纳认证费用才能报名订单。是否前往缴纳？
按钮：[取消] [去缴费]
```

### 步骤3：测试生源页面报名
1. 进入生源页面
2. 点击任意订单的"报名"按钮
3. 预期结果：显示认证费用提示弹窗（同上）

### 步骤4：测试跳转功能
1. 在弹窗中点击"去缴费"按钮
2. 预期结果：跳转到 `/pages/teacher/deposit/index` 页面

### 步骤5：测试缴费后的报名
```sql
-- 将教师缴费状态设为已缴纳
UPDATE teacher_info 
SET certification_fee_paid = '1' 
WHERE id = 30;
```
1. 重新尝试报名
2. 预期结果：正常进行报名流程，无认证费用提示

## 调试日志

### 后端日志
```
=== 开始报名订单校验 ===
教师ID: 30
订单ID: 123
教师认证费用缴纳状态: 0
教师未缴纳认证费用，拒绝报名
```

### 前端日志
```
=== API响应 ===
Status Code: 200
Response Data: {code: 500, msg: "CERTIFICATION_FEE_NOT_PAID"}
认证费用未缴纳错误，显示引导弹窗
```

## 错误处理流程

### 1. 后端处理
```java
// 检查认证费用是否已缴纳
if (!"1".equals(teacherInfo.getCertificationFeePaid())) {
    throw new RuntimeException("CERTIFICATION_FEE_NOT_PAID");
}
```

### 2. 前端request.js处理
```javascript
// 检查是否是特殊错误码
const isSpecialError = res.data.msg === 'CERTIFICATION_FEE_NOT_PAID';

if (!isSpecialError) {
    // 只对非特殊错误显示通用Toast
    wx.showToast({
        title: res.data.msg || '请求失败',
        icon: 'none'
    });
}

// 创建包含message字段的错误对象
const error = {
    ...res.data,
    message: res.data.msg
};
reject(error);
```

### 3. 页面级处理
```javascript
.catch(err => {
    if (err.message === 'CERTIFICATION_FEE_NOT_PAID') {
        // 显示自定义弹窗
        wx.showModal({
            title: '需要缴纳认证费用',
            content: '您还未缴纳认证费用，需要先缴纳认证费用才能报名订单。是否前往缴纳？',
            confirmText: '去缴费',
            success: (res) => {
                if (res.confirm) {
                    wx.navigateTo({
                        url: '/pages/teacher/deposit/index'
                    });
                }
            }
        });
    } else {
        // 其他错误的处理
        wx.showToast({
            title: err.message || '报名失败',
            icon: 'none'
        });
    }
});
```

## 预期结果

### 1. 未缴费时的用户体验
- ✅ 点击报名按钮
- ✅ 显示友好的提示弹窗
- ✅ 点击"去缴费"跳转到缴费页面
- ✅ 点击"取消"关闭弹窗

### 2. 已缴费时的用户体验
- ✅ 点击报名按钮
- ✅ 直接进入报名流程
- ✅ 无认证费用相关提示

### 3. 系统日志
- ✅ 后端正确记录校验过程
- ✅ 前端正确处理特殊错误码
- ✅ 无不必要的错误提示

## 故障排除

### 问题1：仍然显示"报名失败"
**原因**：request.js中的特殊错误码判断不正确
**解决**：检查错误码匹配逻辑

### 问题2：弹窗不显示
**原因**：错误对象的message字段未正确设置
**解决**：确保error.message = res.data.msg

### 问题3：跳转失败
**原因**：页面路径不正确
**解决**：确认路径为 `/pages/teacher/deposit/index`

### 问题4：缴费后仍然提示
**原因**：数据库状态未更新或缓存问题
**解决**：检查数据库更新和清除缓存

现在可以按照这个测试指南验证整个认证费用校验流程了！

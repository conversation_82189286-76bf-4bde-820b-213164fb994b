/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/**app.wxss - Glassmorphism Design System**/

/* ==================== CSS变量定义 ==================== */
page {
  /* 主色调 */
  
  /* 玻璃效果背景 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-bg-light: rgba(255, 255, 255, 0.35);
  --glass-bg-dark: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-border-strong: rgba(255, 255, 255, 0.3);
  
  /* 阴影系统 */
  --shadow-light: 0 4px 16px 0 rgba(31, 38, 135, 0.2);
  --shadow-medium: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
  --shadow-heavy: 0 12px 40px 0 rgba(31, 38, 135, 0.4);
  --shadow-button: 0 4px 16px rgba(102, 126, 234, 0.3);
  
  /* 模糊程度 */
  --blur-light: blur(4px);
  --blur-medium: blur(8px);
  --blur-heavy: blur(16px);
  
  /* 文字颜色 */
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-light: #718096;
  --text-white: #ffffff;
  --text-glass: rgba(255, 255, 255, 0.9);
  
  /* 状态颜色 */
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --error-color: #f56565;
  --info-color: #4299e1;
  
  /* 间距系统 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  --spacing-2xl: 64rpx;
  
  /* 圆角系统 */
  --radius-sm: 12rpx;
  --radius-md: 20rpx;
  --radius-lg: 28rpx;
  --radius-xl: 40rpx;
  --radius-round: 50rpx;
}

/* ==================== 基础重置样式 ==================== */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: var(--primary-gradient);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  box-sizing: border-box;
}

/* ==================== 布局容器 ==================== */
.container {
  padding: var(--spacing-md);
  min-height: 100vh;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* ==================== Glassmorphism基础组件 ==================== */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--blur-medium);
  -webkit-backdrop-filter: var(--blur-medium);
  border-radius: var(--radius-md);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.glass-card.light {
  background: var(--glass-bg-light);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
}

.glass-card.dark {
  background: var(--glass-bg-dark);
  backdrop-filter: var(--blur-heavy);
  -webkit-backdrop-filter: var(--blur-heavy);
}

.glass-card.no-padding {
  padding: 0;
}

.glass-card.large {
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
}

/* ==================== 按钮系统 ==================== */
.glass-button {
  background: var(--primary-gradient);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
  border-radius: var(--radius-round);
  border: 1px solid var(--glass-border-strong);
  color: var(--text-white);
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 500;
  font-size: 32rpx;
  box-shadow: var(--shadow-button);
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.glass-button.secondary {
  background: var(--glass-bg);
  color: var(--primary-color);
  box-shadow: var(--shadow-medium);
}

.glass-button.outline {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.glass-button.small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 28rpx;
}

.glass-button.large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: 36rpx;
}

.glass-button.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* ==================== 输入框系统 ==================== */
.glass-input {
  background: var(--glass-bg);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  font-size: 32rpx;
  color: var(--text-primary);
  width: 100%;
  box-shadow: var(--shadow-light);
}

.glass-input::placeholder {
  color: var(--text-light);
}

.glass-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

/* ==================== 文字样式 ==================== */
.text-xs { font-size: 24rpx; }
.text-sm { font-size: 28rpx; }
.text-base { font-size: 32rpx; }
.text-lg { font-size: 36rpx; }
.text-xl { font-size: 40rpx; }
.text-2xl { font-size: 48rpx; }
.text-3xl { font-size: 56rpx; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-light { color: var(--text-light); }
.text-white { color: var(--text-white); }
.text-glass { color: var(--text-glass); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* ==================== 间距工具类 ==================== */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

/* ==================== 页面背景样式 ==================== */
.glass-page {
  min-height: 100vh;
  background: var(--primary-gradient);
  position: relative;
  overflow-x: hidden;
}

.glass-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* ==================== 状态样式 ==================== */
.status-success {
  color: var(--success-color);
  background: rgba(72, 187, 120, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 24rpx;
}

.status-warning {
  color: var(--warning-color);
  background: rgba(237, 137, 54, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 24rpx;
}

.status-error {
  color: var(--error-color);
  background: rgba(245, 101, 101, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 24rpx;
}

/* ==================== 动画效果 ==================== */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ==================== 响应式工具类 ==================== */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

/* ==================== 安全区域适配 ==================== */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* ==================== TabBar适配 ==================== */
/* 为有TabBar的页面添加底部安全距离 */
.page-with-tabbar {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

/* TabBar页面容器 */
.tabbar-page {
  min-height: 100vh;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* TabBar页面的滚动容器 */
.tabbar-scroll-view {
  height: calc(100vh - 100rpx - env(safe-area-inset-bottom));
  padding-bottom: 0;
}
/* 默认scroll-view样式 */
scroll-view {
  background-color: #f6f7f8;
}

/* 非TabBar页面的scroll-view */
scroll-view:not(.tabbar-scroll-view) {
  height: calc(100vh - 160rpx); /* 减去导航栏高度 */
}
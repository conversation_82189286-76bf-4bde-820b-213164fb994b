/* custom-tab-bar/index.wxss */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #ffffff;
  display: flex;
  border-top: 1rpx solid #e5e5e5;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
}

.tab-bar-item.active {
  color: #1296db;
}

.tab-bar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 4rpx;
}

.tab-bar-text {
  font-size: 20rpx;
  color: #666666;
}

.tab-bar-item.active .tab-bar-text {
  color: #1296db;
}

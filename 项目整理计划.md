# 众桥辅导平台项目整理计划

## 🎯 整理目标
1. 统一代码规范和命名
2. 清理冗余和测试文件
3. 规范数据库设计
4. 优化项目结构
5. 完善文档

## 📋 问题清单

### 1. 数据库问题
- [ ] 认证状态定义不统一（0,1,2 vs 2,4）
- [ ] 字段名不一致（del_flag vs deleted）
- [ ] 表结构注释与实际使用不符

### 2. 后端代码问题
- [ ] Mapper XML文件版本不同步
- [ ] 接口命名不规范
- [ ] 错误处理不统一
- [ ] 日志输出格式混乱

### 3. 前端代码问题
- [ ] 页面路径命名不统一
- [ ] API调用方式不一致
- [ ] 组件复用度低
- [ ] 样式代码重复

### 4. 文件管理问题
- [ ] 测试文件混在项目中
- [ ] 临时文件未清理
- [ ] 文档分散且过时

## 🔧 整理步骤

### 第一阶段：数据库规范化
1. **统一认证状态定义**
   ```sql
   -- 标准认证状态
   0: 未认证
   1: 学生认证  
   2: 已认证
   4: 已实名认证
   ```

2. **统一字段命名**
   - 使用 `deleted` 而不是 `del_flag`
   - 统一时间字段格式
   - 规范状态字段值

3. **创建数据字典**
   - 记录所有表结构
   - 定义字段含义
   - 建立约束规则

### 第二阶段：后端代码整理
1. **清理冗余文件**
   ```
   删除：
   - test_*.sql 文件
   - debug_*.java 文件
   - 重复的Mapper文件
   ```

2. **统一接口规范**
   ```
   家长端：/applet/parent/*
   教师端：/applet/teacher/*
   管理端：/system/*
   ```

3. **规范错误处理**
   - 统一异常处理机制
   - 标准化错误码
   - 完善日志记录

### 第三阶段：前端代码整理
1. **页面结构规范**
   ```
   pages/
   ├── common/          # 公共页面
   ├── teacher/         # 教师端页面
   ├── parent/          # 家长端页面
   └── admin/           # 管理端页面（如需要）
   ```

2. **组件化改造**
   - 提取公共组件
   - 统一样式规范
   - 优化代码复用

3. **API调用统一**
   - 标准化请求格式
   - 统一错误处理
   - 完善响应处理

### 第四阶段：文档完善
1. **API文档**
   - 接口清单
   - 参数说明
   - 响应格式

2. **数据库文档**
   - 表结构说明
   - 字段定义
   - 关系图

3. **部署文档**
   - 环境配置
   - 部署步骤
   - 常见问题

## 📁 建议的目录结构

### 后端结构
```
RuoYi-Vue-master/
├── ruoyi-education/
│   ├── src/main/java/com/ruoyi/education/
│   │   ├── controller/
│   │   │   ├── applet/          # 小程序接口
│   │   │   │   ├── teacher/     # 教师端
│   │   │   │   └── parent/      # 家长端
│   │   │   └── admin/           # 管理端
│   │   ├── service/
│   │   ├── mapper/
│   │   └── domain/
│   └── src/main/resources/
│       ├── mapper/education/
│       └── application.yml
├── sql/
│   ├── init/                    # 初始化脚本
│   ├── update/                  # 更新脚本
│   └── docs/                    # 数据库文档
└── docs/                        # 项目文档
```

### 前端结构
```
applet/
├── pages/
│   ├── common/                  # 公共页面
│   ├── teacher/                 # 教师端
│   └── parent/                  # 家长端
├── components/                  # 公共组件
├── api/                         # API接口
├── utils/                       # 工具函数
└── assets/                      # 静态资源
```

## ⚡ 立即执行的清理任务

### 1. 删除测试文件
- [ ] check_*.sql
- [ ] test_*.sql
- [ ] debug_*.js
- [ ] verify_*.sql

### 2. 合并重复功能
- [ ] 统一教师查询接口
- [ ] 合并相似的Mapper方法
- [ ] 整理API接口

### 3. 修复关键问题
- [ ] 修复认证状态查询
- [ ] 统一字段命名
- [ ] 完善错误处理

## 🎯 预期效果

整理完成后：
1. **代码质量提升** - 减少bug，提高可维护性
2. **开发效率提高** - 规范统一，减少困惑
3. **项目结构清晰** - 易于理解和扩展
4. **文档完善** - 便于团队协作

## 📅 时间安排

- **第一阶段**：2小时（数据库规范化）
- **第二阶段**：3小时（后端整理）
- **第三阶段**：2小时（前端整理）
- **第四阶段**：1小时（文档完善）

**总计：8小时**

---

**建议：先执行立即清理任务，然后按阶段逐步整理，确保每个阶段完成后项目仍可正常运行。**

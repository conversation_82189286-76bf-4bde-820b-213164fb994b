-- 修复TutorOrderMapper.selectNewTutorOrderList问题

-- 1. 检查tutor_order表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ tutor_order表存在'
        ELSE '❌ tutor_order表不存在，需要创建'
    END as table_check
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order';

-- 2. 检查关键字段是否存在
SELECT '=== 检查关键字段 ===' as info;

-- 检查view_count字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ view_count字段存在'
        ELSE '❌ view_count字段不存在，需要添加'
    END as view_count_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'view_count';

-- 检查deleted字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ deleted字段存在'
        ELSE '❌ deleted字段不存在，需要添加'
    END as deleted_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'deleted';

-- 3. 添加缺失的字段

-- 添加view_count字段
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'tutor_order' 
                   AND COLUMN_NAME = 'view_count');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE tutor_order ADD COLUMN view_count int(11) DEFAULT 0 COMMENT ''浏览次数''',
              'SELECT ''view_count字段已存在'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加deleted字段
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'tutor_order' 
                   AND COLUMN_NAME = 'deleted');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE tutor_order ADD COLUMN deleted tinyint(1) DEFAULT 0 COMMENT ''是否删除（0否 1是）''',
              'SELECT ''deleted字段已存在'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 检查order_application表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ order_application表存在'
        ELSE '❌ order_application表不存在，需要创建'
    END as order_application_check
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'order_application';

-- 5. 如果order_application表不存在，创建它
SET @table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'order_application');

SET @sql = IF(@table_exists = 0, 
              'CREATE TABLE order_application (
                  id bigint NOT NULL AUTO_INCREMENT COMMENT ''报名ID'',
                  order_id bigint NOT NULL COMMENT ''订单ID'',
                  teacher_id bigint NOT NULL COMMENT ''教师ID'',
                  status char(1) DEFAULT ''0'' COMMENT ''状态（0待审核 1已通过 2已拒绝）'',
                  apply_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT ''报名时间'',
                  PRIMARY KEY (id),
                  KEY idx_order_id (order_id),
                  KEY idx_teacher_id (teacher_id)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''订单报名表''',
              'SELECT ''order_application表已存在'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 验证修复结果
SELECT '=== 验证修复结果 ===' as info;

-- 测试查询（模拟selectNewTutorOrderList的SQL）
SELECT '测试查询：' as test_info;
SELECT COUNT(*) as test_count
FROM tutor_order o
WHERE o.status = '2'
  AND o.selected_teacher_id IS NULL
  AND (o.deleted IS NULL OR o.deleted = 0);

-- 显示表结构
SELECT '=== tutor_order表结构 ===' as info;
DESCRIBE tutor_order;

SELECT '🎉 修复完成！请重新编译并重启应用。' as result;

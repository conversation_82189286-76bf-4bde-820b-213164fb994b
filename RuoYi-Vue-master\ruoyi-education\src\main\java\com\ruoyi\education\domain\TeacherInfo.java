package com.ruoyi.education.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 教师信息对象 teacher_info
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class TeacherInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 微信openid */
    private String openid;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickName;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /** 性别（0男 1女 2未知） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String gender;

    /** 年龄 */
    @Excel(name = "年龄")
    private Integer age;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 微信号 */
    @Excel(name = "微信号")
    private String wechatNumber;

    /** 身份证号（加密存储） */
    private String idCard;

    /** 所在大学 */
    @Excel(name = "所在大学")
    private String university;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 年级 */
    @Excel(name = "年级")
    private String grade;

    /** 学号 */
    @Excel(name = "学号")
    private String studentId;

    /** 教育背景 */
    private String educationBackground;

    /** 经验背景 */
    private String experienceBackground;

    /** 教授科目（逗号分隔） */
    @Excel(name = "教授科目")
    private String teachingSubjects;

    /** 自我介绍 */
    private String selfIntroduction;

    /** 认证状态（0未认证 1学生认证 2实名认证） */
    @Excel(name = "认证状态", readConverterExp = "0=未认证,1=学生认证,2=实名认证")
    private String certificationStatus;

    /** 认证费用缴纳状态（0未缴纳 1已缴纳） */
    @Excel(name = "认证费用缴纳状态", readConverterExp = "0=未缴纳,1=已缴纳")
    private String certificationFeePaid;

    /** 保证金缴纳状态（0未缴纳 1已缴纳） */
    @Excel(name = "保证金缴纳状态", readConverterExp = "0=未缴纳,1=已缴纳")
    private String depositFeePaid;

    /** 累计接单数 */
    @Excel(name = "累计接单数")
    private Integer totalOrders;

    /** 成功完成订单数 */
    @Excel(name = "成功完成订单数")
    private Integer successOrders;

    /** 接课成功经历（JSON格式存储） */
    private String successExperience;

    /** 评分 */
    @Excel(name = "评分")
    private BigDecimal rating;

    /** 所在地区编码 */
    private String areaCode;

    /** 所在地区名称 */
    @Excel(name = "所在地区")
    private String areaName;

    /** 拉黑状态（0正常 1已拉黑） */
    @Excel(name = "拉黑状态", readConverterExp = "0=正常,1=已拉黑")
    private String blacklistStatus;

    /** 拉黑原因 */
    private String blacklistReason;

    /** 账号状态（0正常 1停用） */
    @Excel(name = "账号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 密码 */
    private String password;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loginDate;

    /** 生日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /** 会员类型（1普通会员 2高级会员） */
    @Excel(name = "会员类型", readConverterExp = "1=普通会员,2=高级会员")
    private String membershipType;

    /** 成为会员日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "成为会员日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date membershipDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getPhone() 
    {
        return phone;
    }

    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public void setWechatNumber(String wechatNumber) 
    {
        this.wechatNumber = wechatNumber;
    }

    public String getWechatNumber() 
    {
        return wechatNumber;
    }

    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }

    public void setUniversity(String university) 
    {
        this.university = university;
    }

    public String getUniversity() 
    {
        return university;
    }

    public void setMajor(String major) 
    {
        this.major = major;
    }

    public String getMajor() 
    {
        return major;
    }

    public void setGrade(String grade) 
    {
        this.grade = grade;
    }

    public String getGrade() 
    {
        return grade;
    }

    public void setStudentId(String studentId) 
    {
        this.studentId = studentId;
    }

    public String getStudentId() 
    {
        return studentId;
    }

    public void setEducationBackground(String educationBackground) 
    {
        this.educationBackground = educationBackground;
    }

    public String getEducationBackground() 
    {
        return educationBackground;
    }

    public void setExperienceBackground(String experienceBackground) 
    {
        this.experienceBackground = experienceBackground;
    }

    public String getExperienceBackground() 
    {
        return experienceBackground;
    }

    public void setTeachingSubjects(String teachingSubjects) 
    {
        this.teachingSubjects = teachingSubjects;
    }

    public String getTeachingSubjects() 
    {
        return teachingSubjects;
    }

    public void setSelfIntroduction(String selfIntroduction) 
    {
        this.selfIntroduction = selfIntroduction;
    }

    public String getSelfIntroduction() 
    {
        return selfIntroduction;
    }

    public void setCertificationStatus(String certificationStatus) 
    {
        this.certificationStatus = certificationStatus;
    }

    public String getCertificationStatus() 
    {
        return certificationStatus;
    }

    public void setCertificationFeePaid(String certificationFeePaid) 
    {
        this.certificationFeePaid = certificationFeePaid;
    }

    public String getCertificationFeePaid()
    {
        return certificationFeePaid;
    }

    public void setDepositFeePaid(String depositFeePaid)
    {
        this.depositFeePaid = depositFeePaid;
    }

    public String getDepositFeePaid()
    {
        return depositFeePaid;
    }

    public void setTotalOrders(Integer totalOrders)
    {
        this.totalOrders = totalOrders;
    }

    public Integer getTotalOrders() 
    {
        return totalOrders;
    }

    public void setSuccessOrders(Integer successOrders) 
    {
        this.successOrders = successOrders;
    }

    public Integer getSuccessOrders()
    {
        return successOrders;
    }

    public void setSuccessExperience(String successExperience)
    {
        this.successExperience = successExperience;
    }

    public String getSuccessExperience()
    {
        return successExperience;
    }

    public void setRating(BigDecimal rating)
    {
        this.rating = rating;
    }

    public BigDecimal getRating() 
    {
        return rating;
    }

    public void setAreaCode(String areaCode) 
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode() 
    {
        return areaCode;
    }

    public void setAreaName(String areaName) 
    {
        this.areaName = areaName;
    }

    public String getAreaName() 
    {
        return areaName;
    }

    public void setBlacklistStatus(String blacklistStatus) 
    {
        this.blacklistStatus = blacklistStatus;
    }

    public String getBlacklistStatus() 
    {
        return blacklistStatus;
    }

    public void setBlacklistReason(String blacklistReason) 
    {
        this.blacklistReason = blacklistReason;
    }

    public String getBlacklistReason() 
    {
        return blacklistReason;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getPassword() 
    {
        return password;
    }

    public void setPassword(String password) 
    {
        this.password = password;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getLoginIp() 
    {
        return loginIp;
    }

    public void setLoginIp(String loginIp) 
    {
        this.loginIp = loginIp;
    }

    public Date getLoginDate() 
    {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) 
    {
        this.loginDate = loginDate;
    }

    public Date getBirthday()
    {
        return birthday;
    }
 
    public void setBirthday(Date birthday)
    {
        this.birthday = birthday;
    }

    public String getMembershipType()
    {
        return membershipType;
    }

    public void setMembershipType(String membershipType)
    {
        this.membershipType = membershipType;
    }

    public Date getMembershipDate()
    {
        return membershipDate;
    }

    public void setMembershipDate(Date membershipDate)
    {
        this.membershipDate = membershipDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("openid", getOpenid())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("avatar", getAvatar())
            .append("realName", getRealName())
            .append("phoneNumber", getPhoneNumber())
            .append("gender", getGender())
            .append("age", getAge())
            .append("phone", getPhone())
            .append("wechatNumber", getWechatNumber())
            .append("idCard", getIdCard())
            .append("university", getUniversity())
            .append("major", getMajor())
            .append("grade", getGrade())
            .append("studentId", getStudentId())
            .append("educationBackground", getEducationBackground())
            .append("experienceBackground", getExperienceBackground())
            .append("teachingSubjects", getTeachingSubjects())
            .append("selfIntroduction", getSelfIntroduction())
            .append("certificationStatus", getCertificationStatus())
            .append("certificationFeePaid", getCertificationFeePaid())
            .append("depositFeePaid", getDepositFeePaid())
            .append("totalOrders", getTotalOrders())
            .append("successOrders", getSuccessOrders())
            .append("rating", getRating())
            .append("areaCode", getAreaCode())
            .append("areaName", getAreaName())
            .append("blacklistStatus", getBlacklistStatus())
            .append("blacklistReason", getBlacklistReason())
            .append("status", getStatus())
            .append("membershipType", getMembershipType())
            .append("membershipDate", getMembershipDate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 
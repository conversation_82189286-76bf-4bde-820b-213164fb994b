# 众桥辅导平台API接口文档

## 🏠 家长端接口

### 1. 获取推荐教师
```
GET /applet/parent/teachers/recommend
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "display_name": "张老师",
      "real_name": "张三",
      "avatar": "http://example.com/avatar.jpg",
      "university": "北京大学",
      "major": "数学",
      "certification_status": "4",
      "certification_text": "已实名认证",
      "teaching_subjects": "数学,物理",
      "rating": 4.8
    }
  ]
}
```

### 2. 获取教师详情
```
GET /applet/parent/teachers/{teacherId}
```

**路径参数：**
- teacherId: 教师ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "display_name": "张老师",
    "real_name": "张三",
    "avatar": "http://example.com/avatar.jpg",
    "age": 22,
    "gender_text": "男",
    "university": "北京大学",
    "major": "数学",
    "education_background": "本科在读",
    "experience_background": "3年家教经验",
    "self_introduction": "擅长数学教学...",
    "certification_text": "已实名认证",
    "rating": 4.8,
    "total_orders": 15,
    "success_orders": 14
  }
}
```

### 3. 创建家教订单
```
POST /applet/parent/orders/create
```

**请求体：**
```json
{
  "subjectName": "数学",
  "grade": "初三",
  "salary": "100",
  "salaryUnit": "1",
  "teachingRequirements": "提高成绩",
  "preferredTeacherId": 1
}
```

## 👨‍🏫 教师端接口

### 1. 获取教师工作台数据
```
GET /applet/teacher/workspace/data
```

### 2. 获取生源订单
```
GET /applet/teacher/orders/available
```

## 🔧 管理端接口

### 1. 教师管理
```
GET /system/teacher/list
POST /system/teacher/add
PUT /system/teacher/edit
DELETE /system/teacher/remove/{ids}
```

## 📋 通用响应码

| 状态码 | 含义 | 说明 |
|---|---|---|
| 200 | 成功 | 请求成功 |
| 401 | 未授权 | 需要登录 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部错误 |

## 🔐 认证机制

所有接口都需要在请求头中携带token：
```
Authorization: Bearer {token}
```

## 📝 请求规范

### 分页参数
```json
{
  "pageNum": 1,
  "pageSize": 10
}
```

### 时间格式
```
yyyy-MM-dd HH:mm:ss
```

### 文件上传
```
Content-Type: multipart/form-data
```

-- 教师会员表创建脚本（安全版）
-- 分步执行，避免字段不存在的错误

-- ==========================================
-- 第一步：创建教师会员表
-- ==========================================
DROP TABLE IF EXISTS `teacher_membership`;
CREATE TABLE `teacher_membership` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `teacher_id` bigint(20) NOT NULL COMMENT '教师ID',
  `membership_type` varchar(10) NOT NULL DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）',
  `membership_date` datetime NOT NULL COMMENT '成为会员日期',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `payment_status` varchar(10) NOT NULL DEFAULT '1' COMMENT '支付状态（0未支付 1已支付 2已退款）',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `status` varchar(10) DEFAULT '1' COMMENT '状态（0无效 1有效）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_membership_type` (`membership_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_membership_date` (`membership_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师会员表';

SELECT 'teacher_membership表创建成功！' as step1_result;

-- ==========================================
-- 第二步：为teacher_info表添加字段
-- ==========================================

-- 添加会员类型字段（如果报错说字段已存在，请忽略）
ALTER TABLE `teacher_info` 
ADD COLUMN `membership_type` varchar(10) DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）';

-- 添加会员日期字段（如果报错说字段已存在，请忽略）
ALTER TABLE `teacher_info` 
ADD COLUMN `membership_date` datetime DEFAULT NULL COMMENT '成为会员日期';

SELECT 'teacher_info表字段添加完成！' as step2_result;

-- ==========================================
-- 第三步：创建索引
-- ==========================================

-- 创建会员类型索引（如果报错说索引已存在，请忽略）
ALTER TABLE `teacher_info` 
ADD INDEX `idx_membership_type` (`membership_type`);

-- 创建会员日期索引（如果报错说索引已存在，请忽略）
ALTER TABLE `teacher_info` 
ADD INDEX `idx_membership_date` (`membership_date`);

SELECT '索引创建完成！' as step3_result;

-- ==========================================
-- 第四步：初始化数据
-- ==========================================

-- 将所有现有教师设置为普通会员
UPDATE `teacher_info` 
SET `membership_type` = '1' 
WHERE `membership_type` IS NULL OR `membership_type` = '';

SELECT '数据初始化完成！' as step4_result;

-- ==========================================
-- 第五步：验证结果
-- ==========================================

-- 检查teacher_membership表
SELECT 'teacher_membership表结构检查:' as info;
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership';

-- 检查teacher_info表的新字段
SELECT 'teacher_info表新字段检查:' as info;
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME IN ('membership_type', 'membership_date');

-- 检查会员类型分布
SELECT '教师会员类型分布:' as info;
SELECT 
    membership_type,
    CASE 
        WHEN membership_type = '1' THEN '普通会员'
        WHEN membership_type = '2' THEN '高级会员'
        ELSE '未知类型'
    END as membership_name,
    COUNT(*) as teacher_count
FROM `teacher_info` 
WHERE membership_type IS NOT NULL
GROUP BY membership_type;

-- ==========================================
-- 完成提示
-- ==========================================
SELECT '🎉 教师会员表创建完成！所有教师已初始化为普通会员。' as final_result;

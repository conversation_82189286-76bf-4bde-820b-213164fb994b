package com.ruoyi.education.controller;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.education.domain.OrderApplication;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.domain.TutorTeacher;
import com.ruoyi.education.service.IOrderApplicationService;
import com.ruoyi.education.service.ITutorOrderService;
import com.ruoyi.education.service.ITutorTeacherService;

/**
 * 家长端小程序控制器
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
@RestController
@RequestMapping("/applet/parent")
public class AppletParentController extends BaseController
{
    @Autowired
    private IOrderApplicationService orderApplicationService;

    @Autowired
    private ITutorOrderService tutorOrderService;

    /**
     * 家长选择教师
     */
    @PostMapping("/selectTeacher")
    public AjaxResult parentSelectTeacher(@RequestBody Map<String, Object> params) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error(401, "登录信息失效");
        }
        
        Long orderId = Long.valueOf(params.get("orderId").toString());
        Long applicationId = Long.valueOf(params.get("applicationId").toString());
        String selectReason = (String) params.get("selectReason");
        
        int result = orderApplicationService.parentSelectTeacher(orderId, applicationId, selectReason);
        return toAjax(result);
    }

    /**
     * 查询我的订单报名列表
     */
    @GetMapping("/myOrder/{orderId}/applications")
    public TableDataInfo getMyOrderApplications(@PathVariable Long orderId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return getDataTable(new ArrayList<>());
        }

        // 验证订单归属
        TutorOrder order = tutorOrderService.selectTutorOrderById(orderId);
        if (order == null || !order.getPublisherId().equals(loginUser.getUserId())) {
            return getDataTable(new ArrayList<>());
        }

        List<OrderApplication> list = orderApplicationService.selectApplicationsByOrderId(orderId);
        return getDataTable(list);
    }

    /**
     * 家长发布订单
     */
    @PostMapping("/publishOrder")
    public AjaxResult publishOrder(@RequestBody TutorOrder tutorOrder) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                return AjaxResult.error(401, "登录信息失效，请重新登录");
            }

            // 设置发布者信息
            tutorOrder.setPublisherType("1"); // 家长发布
            tutorOrder.setPublisherId(loginUser.getUserId());

            // 家长发布的订单需要审核，状态设为待审核
            tutorOrder.setStatus("1"); // 待审核

            int result = tutorOrderService.insertTutorOrder(tutorOrder);
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("发布订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取正在进行的订单
     */
    @GetMapping("/orders/ongoing")
    public TableDataInfo getOngoingOrders() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return getDataTable(new ArrayList<>());
        }

        startPage();
        // 获取当前用户的正在进行的订单（状态为1-7）
        TutorOrder tutorOrder = new TutorOrder();
        tutorOrder.setPublisherId(loginUser.getUserId());
        tutorOrder.setPublisherType("1"); // 家长发布
        List<TutorOrder> list = tutorOrderService.selectTutorOrderList(tutorOrder);
        return getDataTable(list);
    }

    /**
     * 获取推荐教师列表
     */
    @GetMapping("/recommend/teachers")
    public TableDataInfo getRecommendTeachers() {
        startPage();
        // 模拟推荐教师数据，实际应该从教师表中获取
        List<Map<String, Object>> teachers = new ArrayList<>();

        // 添加模拟数据
        for (int i = 1; i <= 5; i++) {
            Map<String, Object> teacher = new java.util.HashMap<>();
            teacher.put("id", i);
            teacher.put("name", "张老师" + i);
            teacher.put("subject", "数学");
            teacher.put("experience", "5年教学经验");
            teacher.put("rating", "4.8");
            teacher.put("avatar", "/assets/images/teacher" + i + ".jpg");
            teachers.add(teacher);
        }

        return getDataTable(teachers);
    }
}
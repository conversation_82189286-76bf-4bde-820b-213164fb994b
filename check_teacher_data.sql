-- 检查数据库中的教师数据
-- 查看所有教师的认证状态

-- 1. 查看所有教师数据
SELECT 
    id,
    nick_name,
    real_name,
    university,
    major,
    grade,
    certification_status,
    status,
    rating,
    self_introduction,
    create_time
FROM teacher_info 
ORDER BY create_time DESC;

-- 2. 查看认证状态统计
SELECT 
    certification_status,
    CASE 
        WHEN certification_status = '0' THEN '未认证'
        WHEN certification_status = '1' THEN '学生认证'
        WHEN certification_status = '2' THEN '实名认证'
        ELSE '未知状态'
    END as status_name,
    COUNT(*) as count
FROM teacher_info 
GROUP BY certification_status;

-- 3. 查看符合推荐条件的教师（状态为0且认证状态为1或2）
SELECT 
    id,
    nick_name,
    real_name,
    university,
    major,
    grade,
    certification_status,
    status,
    rating,
    self_introduction
FROM teacher_info 
WHERE status = '0' AND certification_status IN ('1', '2')
ORDER BY rating DESC, create_time DESC;

-- 4. 检查teacher_subject表中的科目关联
SELECT 
    t.id,
    t.nick_name,
    GROUP_CONCAT(s.subject_name) as subjects
FROM teacher_info t
LEFT JOIN teacher_subject ts ON t.id = ts.teacher_id
LEFT JOIN subject s ON ts.subject_id = s.id
WHERE t.status = '0' AND t.certification_status IN ('1', '2')
GROUP BY t.id, t.nick_name;

package com.ruoyi.education.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 教师会员对象 teacher_membership
 * 
 * <AUTHOR>
 * @date 2024-12-30
 */
public class TeacherMembership extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会员ID */
    private Long id;

    /** 教师ID */
    @Excel(name = "教师ID")
    private Long teacherId;

    /** 会员类型（1普通会员 2高级会员） */
    @Excel(name = "会员类型", readConverterExp = "1=普通会员,2=高级会员")
    private String membershipType;

    /** 成为会员日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "成为会员日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime membershipDate;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal paymentAmount;

    /** 支付状态（0未支付 1已支付 2已退款） */
    @Excel(name = "支付状态", readConverterExp = "0=未支付,1=已支付,2=已退款")
    private String paymentStatus;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTime;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 状态（0无效 1有效） */
    @Excel(name = "状态", readConverterExp = "0=无效,1=有效")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setMembershipType(String membershipType) 
    {
        this.membershipType = membershipType;
    }

    public String getMembershipType() 
    {
        return membershipType;
    }
    public void setMembershipDate(LocalDateTime membershipDate)
    {
        this.membershipDate = membershipDate;
    }

    public LocalDateTime getMembershipDate()
    {
        return membershipDate;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) 
    {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() 
    {
        return paymentAmount;
    }
    public void setPaymentStatus(String paymentStatus) 
    {
        this.paymentStatus = paymentStatus;
    }

    public String getPaymentStatus() 
    {
        return paymentStatus;
    }
    public void setPaymentTime(LocalDateTime paymentTime) 
    {
        this.paymentTime = paymentTime;
    }

    public LocalDateTime getPaymentTime() 
    {
        return paymentTime;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("membershipType", getMembershipType())
            .append("membershipDate", getMembershipDate())
            .append("paymentAmount", getPaymentAmount())
            .append("paymentStatus", getPaymentStatus())
            .append("paymentTime", getPaymentTime())
            .append("orderNo", getOrderNo())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

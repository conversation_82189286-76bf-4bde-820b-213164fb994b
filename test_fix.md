# 管理员代发布订单生源接口修复说明

## 问题分析

经过代码分析，发现问题的根本原因是：

### 1. 数据库查询字段不匹配
- 在 `TutorOrderMapper.xml` 中，`selectTutorOrderVo` 查询语句缺少 `selected_teacher_id` 和 `select_time` 字段
- 但在 `selectNewTutorOrderList` 查询中使用了 `o.selected_teacher_id is null` 作为过滤条件
- 这导致SQL执行时出现字段不存在的错误

### 2. ResultMap 映射不完整
- `TutorOrderResult` 的 ResultMap 中缺少 `selectedTeacherId` 和 `selectTime` 字段的映射
- 导致即使查询成功，这些字段也无法正确映射到Java对象

## 修复内容

### 1. 更新 selectTutorOrderVo 查询语句
```xml
<sql id="selectTutorOrderVo">
    select o.id, o.order_no, o.order_type, o.subject_name, o.grade, o.student_gender, o.tutoring_mode, o.student_details, o.available_tutoring_time, o.weekly_sessions, o.tutoring_time_slots, o.session_duration, o.time_slot_notes, o.teacher_gender, o.teacher_category, o.teaching_type, o.salary, o.salary_unit, o.city, o.detailed_address, o.contact_name, o.contact_phone, o.teaching_requirements, o.publisher_type, o.status, o.selected_teacher_id, o.select_time, o.is_top, o.create_time,
           COALESCE(o.view_count, 0) as view_count,
           COALESCE((select count(*) from order_application a where a.order_id = o.id), 0) as apply_count
    from tutor_order o
</sql>
```

### 2. 更新 ResultMap 映射
```xml
<resultMap type="TutorOrder" id="TutorOrderResult">
    <!-- 其他字段映射... -->
    <result property="selectedTeacherId"    column="selected_teacher_id"    />
    <result property="selectTime"    column="select_time"    />
    <!-- 其他字段映射... -->
</resultMap>
```

### 3. 统一查询语句
将其他直接写SQL的查询改为使用 `<include refid="selectTutorOrderVo"/>` 以保持一致性。

## 修复后的逻辑流程

1. **管理员发布订单**：
   - 调用 `publishOrderByAdmin` 方法
   - 设置 `status = "2"`（审核通过）
   - 设置 `selected_teacher_id = null`（未选择教师）

2. **小程序获取生源**：
   - 调用 `/applet/teacher/order/new` 接口
   - 执行 `selectNewTutorOrderList` 查询
   - 查询条件：`status = '2' AND selected_teacher_id IS NULL`
   - 返回符合条件的订单列表

## 验证方法

1. 启动后端服务
2. 通过管理后台发布一个新订单
3. 调用小程序生源接口 `/applet/teacher/order/new`
4. 检查返回结果是否包含新发布的订单

## 相关文件

- `RuoYi-Vue-master/ruoyi-education/src/main/resources/mapper/education/TutorOrderMapper.xml`
- `RuoYi-Vue-master/ruoyi-education/target/classes/mapper/education/TutorOrderMapper.xml`

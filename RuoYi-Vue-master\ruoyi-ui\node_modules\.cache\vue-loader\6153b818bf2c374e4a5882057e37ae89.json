{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\education\\teacher\\index.vue?vue&type=style&index=0&id=0a09d214&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\education\\teacher\\index.vue", "mtime": 1753784823288}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\css-loader@3.6.0_webpack@4.47.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1746002926193}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1746002957864}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748798848719}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1746002959099}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1746002957679}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLyog6K+m5oOF5by556qX5qC35byPICovCi5kZXRhaWwtY29udGVudCB7CiAgcGFkZGluZzogMTBweCAwOwp9CgoudGVhY2hlci1pbmZvIGg0IHsKICBtYXJnaW46IDAgMCAyMHB4IDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICM0MDlFRkY7CiAgcGFkZGluZy1ib3R0b206IDEwcHg7Cn0KCi5zdWJqZWN0cy1saXN0IHsKICBtaW4taGVpZ2h0OiA2MHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Owp9CgouZWR1Y2F0aW9uLWxpc3QsIC5leHBlcmllbmNlLWxpc3QgewogIG1heC1oZWlnaHQ6IDUwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5lZHVjYXRpb24taXRlbSwgLmV4cGVyaWVuY2UtaXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLyog5pWZ6IKy6IOM5pmv5qC35byPICovCi5lZHVjYXRpb24taGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouZWR1Y2F0aW9uLXRpdGxlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5zY2hvb2wtbmFtZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMzMDMxMzM7Cn0KCi5lZHVjYXRpb24tcGVyaW9kIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjOTA5Mzk5Owp9CgoudGltZS10ZXh0IHsKICBmb250LXNpemU6IDEycHg7Cn0KCi5lZHVjYXRpb24tZGV0YWlscyB7CiAgcGFkZGluZy1sZWZ0OiAyNnB4Owp9CgouZGV0YWlsLXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgZm9udC1zaXplOiAxNHB4Owp9CgouZGV0YWlsLWxhYmVsIHsKICBjb2xvcjogIzkwOTM5OTsKICBtaW4td2lkdGg6IDYwcHg7CiAgZmxleC1zaHJpbms6IDA7Cn0KCi5kZXRhaWwtdmFsdWUgewogIGNvbG9yOiAjNjA2MjY2OwogIGZsZXg6IDE7Cn0KCi8qIOe7j+mqjOiDjOaZr+agt+W8jyAqLwouZXhwZXJpZW5jZS1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogIG1hcmdpbi1ib3R0b206IDE1cHg7Cn0KCi5leHBlcmllbmNlLXRpdGxlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi50YXJnZXQtbmFtZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMzMDMxMzM7Cn0KCi5leHBlcmllbmNlLXBlcmlvZCB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzkwOTM5OTsKfQoKLmV4cGVyaWVuY2UtZGV0YWlscyB7CiAgcGFkZGluZy1sZWZ0OiAyNnB4Owp9CgouY29udGVudC10ZXh0IHsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIHBhZGRpbmc6IDEycHg7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzYwNjI2NjsKICBsaW5lLWhlaWdodDogMS42OwogIG1hcmdpbi10b3A6IDhweDsKICBib3JkZXItbGVmdDogM3B4IHNvbGlkICNFNkEyM0M7Cn0KCi5lbXB0eS1zdGF0ZSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDQwcHggMDsKICBjb2xvcjogIzkwOTM5OTsKICBmb250LXNpemU6IDE0cHg7Cn0KCi8qIOWIl+ihqOaMiemSruagt+W8jyAqLwouZWwtYnV0dG9uLS10ZXh0IHsKICBwYWRkaW5nOiAwOwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLmVsLWJ1dHRvbi0tdGV4dDpob3ZlciB7CiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo4BA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/education/teacher", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"用户名\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\n        <el-input\n          v-model=\"queryParams.phoneNumber\"\n          placeholder=\"请输入手机号码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"真实姓名\" prop=\"realName\">\n        <el-input\n          v-model=\"queryParams.realName\"\n          placeholder=\"请输入真实姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"认证状态\" prop=\"certificationStatus\">\n        <el-select v-model=\"queryParams.certificationStatus\" placeholder=\"请选择认证状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.edu_certification_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"拉黑状态\" prop=\"blacklistStatus\">\n        <el-select v-model=\"queryParams.blacklistStatus\" placeholder=\"请选择拉黑状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.edu_blacklist_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['education:teacher:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['education:teacher:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-close\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleBlacklist\"\n          v-hasPermi=\"['education:teacher:blacklist']\"\n        >拉黑</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-check\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleUnblacklist\"\n          v-hasPermi=\"['education:teacher:blacklist']\"\n        >解除拉黑</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['education:teacher:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"teacherList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n\n      <!-- 基本信息 -->\n      <el-table-column label=\"头像\" align=\"center\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-avatar :size=\"40\" :src=\"scope.row.avatar\" icon=\"el-icon-user-solid\"></el-avatar>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"realName\" width=\"100\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"性别\" align=\"center\" prop=\"gender\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_user_sex\" :value=\"scope.row.gender\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"生日\" align=\"center\" prop=\"birthday\" width=\"120\" />\n\n      <!-- 教授科目 -->\n      <el-table-column label=\"教授科目\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-button\n            v-if=\"scope.row.teachingSubjects\"\n            type=\"text\"\n            size=\"mini\"\n            @click=\"viewSubjects(scope.row)\"\n            style=\"color: #409EFF;\">\n            查看详情\n          </el-button>\n          <span v-else style=\"color: #999;\">未设置</span>\n        </template>\n      </el-table-column>\n\n      <!-- 教育背景 -->\n      <el-table-column label=\"教育背景\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-button\n            v-if=\"scope.row.educationBackground\"\n            type=\"text\"\n            size=\"mini\"\n            @click=\"viewEducation(scope.row)\"\n            style=\"color: #67C23A;\">\n            查看详情\n          </el-button>\n          <span v-else style=\"color: #999;\">未填写</span>\n        </template>\n      </el-table-column>\n\n      <!-- 经验背景 -->\n      <el-table-column label=\"经验背景\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-button\n            v-if=\"scope.row.experienceBackground\"\n            type=\"text\"\n            size=\"mini\"\n            @click=\"viewExperience(scope.row)\"\n            style=\"color: #E6A23C;\">\n            查看详情\n          </el-button>\n          <span v-else style=\"color: #999;\">未填写</span>\n        </template>\n      </el-table-column>\n\n      <!-- 统计信息 -->\n      <el-table-column label=\"接单数\" align=\"center\" prop=\"totalOrders\" width=\"80\" />\n      <el-table-column label=\"完成数\" align=\"center\" prop=\"successOrders\" width=\"80\" />\n\n      <!-- 认证状态 -->\n      <el-table-column label=\"认证状态\" align=\"center\" prop=\"certificationStatus\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.teacher_certification_status\" :value=\"scope.row.certificationStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"缴费状态\" align=\"center\" prop=\"certificationFeePaid\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.edu_fee_status\" :value=\"scope.row.certificationFeePaid\"/>\n        </template>\n      </el-table-column>\n\n      <!-- 其他信息 -->\n      <el-table-column label=\"注册时间\" align=\"center\" prop=\"createTime\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"OpenID\" align=\"center\" prop=\"openid\" width=\"120\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"220\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['education:teacher:query']\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['education:teacher:edit']\"\n          >修改</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\">\n            <el-button size=\"mini\" type=\"text\">\n              更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"withdrawal\" icon=\"el-icon-bank-card\" v-hasPermi=\"['education:withdrawal:list']\">提现记录</el-dropdown-item>\n              <el-dropdown-item command=\"statistics\" icon=\"el-icon-data-line\">收入统计</el-dropdown-item>\n              <el-dropdown-item command=\"delete\" icon=\"el-icon-delete\" v-hasPermi=\"['education:teacher:remove']\">删除</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n          <el-button\n            v-if=\"scope.row.blacklistStatus === '0'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-close\"\n            @click=\"handleBlacklist(scope.row)\"\n            v-hasPermi=\"['education:teacher:blacklist']\"\n          >拉黑</el-button>\n          <el-button\n            v-if=\"scope.row.blacklistStatus === '1'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleUnblacklist(scope.row)\"\n            v-hasPermi=\"['education:teacher:blacklist']\"\n          >解除拉黑</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 修改教师信息对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"真实姓名\" prop=\"realName\">\n              <el-input v-model=\"form.realName\" placeholder=\"请输入真实姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"性别\">\n              <el-select v-model=\"form.gender\" placeholder=\"请选择性别\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_user_sex\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"生日\" prop=\"birthday\">\n              <el-date-picker\n                v-model=\"form.birthday\"\n                type=\"date\"\n                placeholder=\"选择生日\"\n                value-format=\"yyyy-MM-dd\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\n              <el-input v-model=\"form.phoneNumber\" placeholder=\"请输入手机号码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"认证状态\">\n          <el-select v-model=\"form.certificationStatus\" placeholder=\"请选择认证状态\">\n            <el-option\n              v-for=\"dict in dict.type.teacher_certification_status\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"缴费状态\">\n          <el-select v-model=\"form.certificationFeePaid\" placeholder=\"请选择缴费状态\">\n            <el-option\n              v-for=\"dict in dict.type.edu_fee_status\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注信息\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 拉黑对话框 -->\n    <el-dialog title=\"拉黑教师\" :visible.sync=\"blacklistDialogVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"blacklistForm\" :model=\"blacklistForm\" :rules=\"blacklistRules\" label-width=\"80px\">\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\n          <el-input\n            v-model=\"blacklistForm.blacklistReason\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入拉黑原因\"\n          />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmBlacklist\">确 定</el-button>\n        <el-button @click=\"blacklistDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 教授科目详情弹窗 -->\n    <el-dialog title=\"教授科目详情\" :visible.sync=\"subjectsDialogVisible\" width=\"500px\" append-to-body>\n      <div class=\"detail-content\">\n        <div class=\"teacher-info\">\n          <h4>{{ currentTeacher.realName }} 的教授科目</h4>\n        </div>\n        <div class=\"subjects-list\">\n          <el-tag\n            v-for=\"subject in parseSubjects(currentTeacher.teachingSubjects)\"\n            :key=\"subject\"\n            size=\"medium\"\n            style=\"margin: 5px;\">\n            {{ subject }}\n          </el-tag>\n        </div>\n        <div v-if=\"!currentTeacher.teachingSubjects\" class=\"empty-state\">\n          <span style=\"color: #999;\">暂无教授科目信息</span>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 教育背景详情弹窗 -->\n    <el-dialog title=\"教育背景详情\" :visible.sync=\"educationDialogVisible\" width=\"700px\" append-to-body>\n      <div class=\"detail-content\">\n        <div class=\"teacher-info\">\n          <h4>{{ currentTeacher.realName }} 的教育背景</h4>\n        </div>\n        <div class=\"education-list\">\n          <div\n            v-for=\"(edu, index) in parseEducationBackground(currentTeacher.educationBackground)\"\n            :key=\"index\"\n            class=\"education-item\">\n            <el-card shadow=\"hover\" style=\"margin-bottom: 15px;\">\n              <div class=\"education-header\">\n                <div class=\"education-title\">\n                  <i class=\"el-icon-school\" style=\"color: #67C23A; margin-right: 8px; font-size: 18px;\"></i>\n                  <span class=\"school-name\">{{ edu.school }}</span>\n                </div>\n                <div class=\"education-period\" v-if=\"edu.startDate || edu.endDate\">\n                  <i class=\"el-icon-time\" style=\"color: #909399; margin-right: 5px;\"></i>\n                  <span class=\"time-text\">{{ formatEducationPeriod(edu) }}</span>\n                </div>\n              </div>\n              <div class=\"education-details\">\n                <div class=\"detail-row\">\n                  <span class=\"detail-label\">学历：</span>\n                  <span class=\"detail-value\">{{ edu.degree }}</span>\n                </div>\n                <div class=\"detail-row\">\n                  <span class=\"detail-label\">专业：</span>\n                  <span class=\"detail-value\">{{ edu.major }}</span>\n                </div>\n              </div>\n            </el-card>\n          </div>\n        </div>\n        <div v-if=\"!currentTeacher.educationBackground\" class=\"empty-state\">\n          <span style=\"color: #999;\">暂无教育背景信息</span>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 经验背景详情弹窗 -->\n    <el-dialog title=\"经验背景详情\" :visible.sync=\"experienceDialogVisible\" width=\"750px\" append-to-body>\n      <div class=\"detail-content\">\n        <div class=\"teacher-info\">\n          <h4>{{ currentTeacher.realName }} 的经验背景</h4>\n        </div>\n        <div class=\"experience-list\">\n          <div\n            v-for=\"(exp, index) in parseExperienceBackground(currentTeacher.experienceBackground)\"\n            :key=\"index\"\n            class=\"experience-item\">\n            <el-card shadow=\"hover\" style=\"margin-bottom: 15px;\">\n              <div class=\"experience-header\">\n                <div class=\"experience-title\">\n                  <i class=\"el-icon-trophy\" style=\"color: #E6A23C; margin-right: 8px; font-size: 18px;\"></i>\n                  <span class=\"target-name\">{{ exp.teachingTarget }}</span>\n                </div>\n                <div class=\"experience-period\" v-if=\"exp.startDate || exp.endDate\">\n                  <i class=\"el-icon-time\" style=\"color: #909399; margin-right: 5px;\"></i>\n                  <span class=\"time-text\">{{ formatExperiencePeriod(exp) }}</span>\n                </div>\n              </div>\n              <div class=\"experience-details\" v-if=\"exp.content\">\n                <div class=\"detail-row\">\n                  <span class=\"detail-label\">详细内容：</span>\n                </div>\n                <div class=\"content-text\">\n                  {{ exp.content }}\n                </div>\n              </div>\n            </el-card>\n          </div>\n        </div>\n        <div v-if=\"!currentTeacher.experienceBackground\" class=\"empty-state\">\n          <span style=\"color: #999;\">暂无经验背景信息</span>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listTeacher, getTeacher, delTeacher, addTeacher, updateTeacher, blacklistTeacher, batchBlacklistTeacher, unblacklistTeacher, batchUnblacklistTeacher } from \"@/api/education/teacher\";\nimport { getTeacherIncomeStatistics } from \"@/api/education/income\";\n\nexport default {\n  name: \"Teacher\",\n  dicts: ['sys_user_sex', 'teacher_certification_status', 'edu_fee_status', 'edu_blacklist_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 教师信息表格数据\n      teacherList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 拉黑对话框显示状态\n      blacklistDialogVisible: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: null,\n        phoneNumber: null,\n        realName: null,\n        certificationStatus: null,\n        blacklistStatus: null,\n      },\n      // 表单参数\n      form: {},\n      // 拉黑表单\n      blacklistForm: {\n        blacklistReason: ''\n      },\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" }\n        ],\n        phoneNumber: [\n          { required: true, message: \"手机号码不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      },\n      // 拉黑表单校验\n      blacklistRules: {\n        blacklistReason: [\n          { required: true, message: \"拉黑原因不能为空\", trigger: \"blur\" }\n        ]\n      },\n\n      // 详情弹窗相关\n      subjectsDialogVisible: false,\n      educationDialogVisible: false,\n      experienceDialogVisible: false,\n      currentTeacher: {}\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询教师信息列表 */\n    getList() {\n      this.loading = true;\n      listTeacher(this.queryParams).then(response => {\n        this.teacherList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        userName: null,\n        nickName: null,\n        phoneNumber: null,\n        wechatNumber: null,\n        realName: null,\n        gender: null,\n        age: null,\n        avatar: null,\n        idCard: null,\n        university: null,\n        major: null,\n        grade: null,\n        studentId: null,\n        educationBackground: null,\n        experienceBackground: null,\n        teachingSubjects: null,\n        selfIntroduction: null,\n        certificationStatus: \"0\",\n        certificationFeePaid: \"0\",\n        totalOrders: null,\n        successOrders: null,\n        rating: null,\n        areaCode: null,\n        areaName: null,\n        blacklistStatus: \"0\",\n        blacklistReason: null,\n        status: \"0\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getTeacher(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改教师信息\";\n      });\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getTeacher(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"查看教师信息\";\n        // 设置表单为只读\n        this.$nextTick(() => {\n          this.$refs.form.$el.querySelectorAll('input,textarea,select').forEach(item => {\n            item.setAttribute('readonly', true);\n            item.setAttribute('disabled', true);\n          });\n        });\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateTeacher(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            this.$modal.msgError(\"不支持新增教师操作\");\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除教师信息编号为\"' + ids + '\"的数据项？').then(function() {\n        return delTeacher(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n\n\n    // 解析教学科目\n    parseSubjects(subjects) {\n      console.log('原始科目数据:', subjects);\n      if (!subjects) return [];\n      try {\n        // 如果是字符串，尝试JSON解析\n        if (typeof subjects === 'string') {\n          // 先尝试JSON解析\n          try {\n            const parsed = JSON.parse(subjects);\n            console.log('JSON解析结果:', parsed);\n            return Array.isArray(parsed) ? parsed : [parsed];\n          } catch (e) {\n            // JSON解析失败，按逗号分割\n            const split = subjects.split(',').filter(s => s.trim());\n            console.log('逗号分割结果:', split);\n            return split;\n          }\n        }\n        // 如果是数组，直接返回\n        if (Array.isArray(subjects)) {\n          console.log('数组数据:', subjects);\n          return subjects;\n        }\n        // 其他情况，转为数组\n        return [subjects];\n      } catch (e) {\n        console.error('解析科目数据失败:', e);\n        return [];\n      }\n    },\n\n    // 解析教育背景\n    parseEducationBackground(educationStr) {\n      if (!educationStr) return [];\n      return educationStr.split(';').map(item => {\n        const trimmed = item.trim();\n        if (!trimmed) return null;\n\n        // 解析格式：学校-学历-专业-开始时间至结束时间\n        const parts = trimmed.split('-');\n        const result = {\n          school: parts[0] || '',\n          degree: parts[1] || '',\n          major: parts[2] || '',\n          startDate: '',\n          endDate: ''\n        };\n\n        // 解析时间部分\n        if (parts.length > 3) {\n          const timePart = parts[3];\n          if (timePart.includes('至')) {\n            const timeParts = timePart.split('至');\n            result.startDate = timeParts[0] || '';\n            result.endDate = timeParts[1] || '';\n          }\n        }\n\n        return result;\n      }).filter(item => item);\n    },\n\n    // 解析经验背景\n    parseExperienceBackground(experienceStr) {\n      if (!experienceStr) return [];\n      return experienceStr.split(';').map(item => {\n        const trimmed = item.trim();\n        if (!trimmed) return null;\n\n        // 解析格式：教学对象-开始时间至结束时间-详细内容\n        const parts = trimmed.split('-');\n        const result = {\n          teachingTarget: parts[0] || '',\n          startDate: '',\n          endDate: '',\n          content: ''\n        };\n\n        // 解析时间部分\n        if (parts.length > 1) {\n          const timePart = parts[1];\n          if (timePart.includes('至')) {\n            const timeParts = timePart.split('至');\n            result.startDate = timeParts[0] || '';\n            result.endDate = timeParts[1] || '';\n\n            // 详细内容在时间之后\n            if (parts.length > 2) {\n              result.content = parts.slice(2).join('-');\n            }\n          } else {\n            // 如果没有时间信息，第二部分可能是详细内容\n            result.content = parts.slice(1).join('-');\n          }\n        }\n\n        return result;\n      }).filter(item => item);\n    },\n\n    // 查看教授科目详情\n    viewSubjects(row) {\n      this.currentTeacher = row;\n      this.subjectsDialogVisible = true;\n    },\n\n    // 查看教育背景详情\n    viewEducation(row) {\n      this.currentTeacher = row;\n      this.educationDialogVisible = true;\n    },\n\n    // 查看经验背景详情\n    viewExperience(row) {\n      this.currentTeacher = row;\n      this.experienceDialogVisible = true;\n    },\n\n    // 格式化教育背景时间段\n    formatEducationPeriod(edu) {\n      if (!edu.startDate && !edu.endDate) return '';\n\n      let period = '';\n      if (edu.startDate) {\n        period += edu.startDate;\n      }\n      period += ' 至 ';\n      if (edu.endDate) {\n        period += edu.endDate;\n      } else {\n        period += '至今';\n      }\n      return period;\n    },\n\n    // 格式化经验背景时间段\n    formatExperiencePeriod(exp) {\n      if (!exp.startDate && !exp.endDate) return '';\n\n      let period = '';\n      if (exp.startDate) {\n        period += exp.startDate;\n      }\n      period += ' 至 ';\n      if (exp.endDate) {\n        period += exp.endDate;\n      } else {\n        period += '至今';\n      }\n      return period;\n    },\n    /** 处理更多操作命令 */\n    handleCommand(command, row) {\n      switch (command) {\n        case 'withdrawal':\n          // 跳转到提现记录页面\n          this.$router.push({\n            path: '/education/withdrawal',\n            query: {\n              teacherId: row.id,\n              teacherName: row.realName || row.userName\n            }\n          });\n          break;\n        case 'statistics':\n          // 显示收入统计弹窗\n          this.showIncomeStatistics(row);\n          break;\n        case 'delete':\n          this.handleDelete(row);\n          break;\n      }\n    },\n    /** 显示收入统计 */\n    async showIncomeStatistics(row) {\n      try {\n        this.$modal.loading('正在获取收入统计...');\n        const response = await getTeacherIncomeStatistics(row.id);\n        this.$modal.closeLoading();\n\n        const stats = response.data;\n        const message = `\n          <div style=\"text-align: left;\">\n            <h4>教师：${row.realName || row.userName}</h4>\n            <p><strong>总收入：</strong>¥${stats.totalIncome || 0}</p>\n            <p><strong>可提现金额：</strong>¥${stats.availableAmount || 0}</p>\n            <p><strong>已提现金额：</strong>¥${stats.withdrawnAmount || 0}</p>\n            <p><strong>待结算金额：</strong>¥${stats.pendingAmount || 0}</p>\n            <hr/>\n            <p><strong>试课收入：</strong>¥${stats.trialIncome || 0}</p>\n            <p><strong>一周课程收入：</strong>¥${stats.weekIncome || 0}</p>\n            <p><strong>奖励收入：</strong>¥${stats.bonusIncome || 0}</p>\n          </div>\n        `;\n\n        this.$alert(message, '收入统计', {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '确定',\n          type: 'info'\n        });\n      } catch (error) {\n        this.$modal.closeLoading();\n        this.$modal.msgError('获取收入统计失败：' + (error.message || '未知错误'));\n      }\n    },\n    /** 拉黑按钮操作 */\n    handleBlacklist(row) {\n      if (row.id) {\n        this.blacklistForm.id = row.id;\n      } else {\n        this.blacklistForm.remark = this.ids.join(',');\n      }\n      this.blacklistDialogVisible = true;\n    },\n    /** 确认拉黑 */\n    confirmBlacklist() {\n      this.$refs[\"blacklistForm\"].validate(valid => {\n        if (valid) {\n          if (this.blacklistForm.id) {\n            // 单个拉黑\n            blacklistTeacher(this.blacklistForm).then(response => {\n              this.$modal.msgSuccess(\"拉黑成功\");\n              this.blacklistDialogVisible = false;\n              this.getList();\n              this.$set(this.blacklistForm, 'blacklistReason', '');\n            });\n          } else {\n            // 批量拉黑\n            batchBlacklistTeacher(this.blacklistForm).then(response => {\n              this.$modal.msgSuccess(\"拉黑成功\");\n              this.blacklistDialogVisible = false;\n              this.$set(this.blacklistForm, 'blacklistReason', '');\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 解除拉黑按钮操作 */\n    handleUnblacklist(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认解除拉黑教师编号为\"' + ids + '\"的数据项？').then(function() {\n        if (row.id) {\n          return unblacklistTeacher({id: row.id});\n        } else {\n          return batchUnblacklistTeacher({remark: ids.join(',')});\n        }\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"解除拉黑成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('education/teacher/export', {\n        ...this.queryParams\n      }, `teacher_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 详情弹窗样式 */\n.detail-content {\n  padding: 10px 0;\n}\n\n.teacher-info h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n  border-bottom: 2px solid #409EFF;\n  padding-bottom: 10px;\n}\n\n.subjects-list {\n  min-height: 60px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: flex-start;\n}\n\n.education-list, .experience-list {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.education-item, .experience-item {\n  margin-bottom: 15px;\n}\n\n/* 教育背景样式 */\n.education-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.education-title {\n  display: flex;\n  align-items: center;\n}\n\n.school-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.education-period {\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n  color: #909399;\n}\n\n.time-text {\n  font-size: 12px;\n}\n\n.education-details {\n  padding-left: 26px;\n}\n\n.detail-row {\n  display: flex;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.detail-label {\n  color: #909399;\n  min-width: 60px;\n  flex-shrink: 0;\n}\n\n.detail-value {\n  color: #606266;\n  flex: 1;\n}\n\n/* 经验背景样式 */\n.experience-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.experience-title {\n  display: flex;\n  align-items: center;\n}\n\n.target-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.experience-period {\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n  color: #909399;\n}\n\n.experience-details {\n  padding-left: 26px;\n}\n\n.content-text {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  font-size: 14px;\n  color: #606266;\n  line-height: 1.6;\n  margin-top: 8px;\n  border-left: 3px solid #E6A23C;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 列表按钮样式 */\n.el-button--text {\n  padding: 0;\n  font-size: 12px;\n}\n\n.el-button--text:hover {\n  text-decoration: underline;\n}\n</style>"]}]}
-- 创建测试家长数据

-- 1. 确保parent_info表存在
CREATE TABLE IF NOT EXISTS `parent_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `total_orders` int(11) DEFAULT '0' COMMENT '累计发布订单数',
  `completed_orders` int(11) DEFAULT '0' COMMENT '完成订单数',
  `average_rating` decimal(3,2) DEFAULT '0.00' COMMENT '平均评分',
  `area_code` varchar(20) DEFAULT NULL COMMENT '所在地区编码',
  `area_name` varchar(100) DEFAULT NULL COMMENT '所在地区名称',
  `blacklist_status` char(1) DEFAULT '0' COMMENT '拉黑状态（0正常 1已拉黑）',
  `blacklist_reason` varchar(500) DEFAULT NULL COMMENT '拉黑原因',
  `blacklist_time` datetime DEFAULT NULL COMMENT '拉黑时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  UNIQUE KEY `uk_user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长信息表';

-- 2. 插入测试家长数据
INSERT INTO parent_info (
    openid, user_name, nick_name, real_name, phone_number, 
    area_code, area_name, status, create_time, update_time
) VALUES 
(
    'test_parent_openid_001', 'parent_test_001', '张家长', '张三', '13800138001',
    '430100', '长沙市', '0', NOW(), NOW()
),
(
    'test_parent_openid_002', 'parent_test_002', '李家长', '李四', '13800138002',
    '430100', '长沙市', '0', NOW(), NOW()
)
ON DUPLICATE KEY UPDATE
    nick_name = VALUES(nick_name),
    real_name = VALUES(real_name),
    phone_number = VALUES(phone_number),
    update_time = NOW();

-- 3. 验证插入结果
SELECT 
    id, openid, user_name, nick_name, real_name, phone_number, status, create_time
FROM parent_info 
ORDER BY create_time DESC;

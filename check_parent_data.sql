-- 检查家长数据和登录相关信息

-- 1. 检查是否有parent_info表
SHOW TABLES LIKE 'parent_info';

-- 2. 如果表存在，查看表结构
DESCRIBE parent_info;

-- 3. 查看所有家长数据
SELECT * FROM parent_info ORDER BY create_time DESC;

-- 4. 检查家长数据数量
SELECT COUNT(*) as total_parents FROM parent_info;

-- 5. 检查家长状态分布
SELECT 
    status,
    CASE 
        WHEN status = '0' THEN '正常'
        WHEN status = '1' THEN '停用'
        ELSE '未知'
    END as status_text,
    COUNT(*) as count
FROM parent_info 
GROUP BY status;

-- 6. 如果没有家长数据，插入一个测试家长
-- INSERT INTO parent_info (
--     openid, user_name, nick_name, real_name, phone_number, 
--     status, create_time, update_time
-- ) VALUES (
--     'test_parent_openid', 'test_parent', '测试家长', '张三', '13800138000',
--     '0', NOW(), NOW()
-- );

-- 7. 检查教师数据（对比用）
SELECT COUNT(*) as total_teachers FROM teacher_info;

-- 8. 查看最近的登录记录（如果有sys_logininfor表）
-- SELECT * FROM sys_logininfor WHERE user_name LIKE '%parent%' ORDER BY login_time DESC LIMIT 5;

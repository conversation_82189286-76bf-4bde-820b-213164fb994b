// pages/parent/home/<USER>
const { request } = require('../../../utils/request.js');
const app = getApp();

Page({
  data: {
    userInfo: null,
    stats: {
      publishedOrders: 0,
      ongoingOrders: 0,
      completedOrders: 0
    },
    recentOrders: [],
    recommendTeachers: [],
    loading: true
  },

  /**
   * 页面加载
   */
  onLoad() {
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 0
      });
    }

    this.loadUserInfo();
    this.loadStats();
    this.loadRecentOrders();
    this.loadRecommendTeachers();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 确保设置为家长端
    app.setUserType('parent');

    wx.setNavigationBarTitle({
      title: '家长端'
    });
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStats() {
    try {
      const res = await request({
        url: '/applet/parent/stats',
        method: 'GET'
      });
      
      if (res.code === 200) {
        this.setData({
          stats: res.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用模拟数据
      this.setData({
        stats: {
          publishedOrders: 3,
          ongoingOrders: 1,
          completedOrders: 5
        }
      });
    }
  },

  /**
   * 加载最近订单
   */
  async loadRecentOrders() {
    try {
      const res = await request({
        url: '/applet/parent/orders/recent',
        method: 'GET'
      });
      
      if (res.code === 200) {
        this.setData({
          recentOrders: res.data
        });
      }
    } catch (error) {
      console.error('加载最近订单失败:', error);
      // 使用模拟数据
      this.setData({
        recentOrders: [
          {
            id: 1,
            title: '小学数学辅导',
            subject: '数学',
            grade: '三年级',
            status: '招募中',
            createTime: '2024-01-15'
          },
          {
            id: 2,
            title: '初中英语提升',
            subject: '英语', 
            grade: '初二',
            status: '进行中',
            createTime: '2024-01-10'
          }
        ]
      });
    }
  },

  /**
   * 加载推荐教师
   */
  async loadRecommendTeachers() {
    try {
      const res = await request({
        url: '/applet/parent/teachers/recommend',
        method: 'GET'
      });
      
      if (res.code === 200) {
        this.setData({
          recommendTeachers: res.data
        });
      }
    } catch (error) {
      console.error('加载推荐教师失败:', error);
      // 使用模拟数据
      this.setData({
        recommendTeachers: [
          {
            id: 1,
            name: '张老师',
            avatar: '/assets/images/default_avatar.png',
            subject: '数学',
            experience: '5年教学经验',
            rating: 4.8
          },
          {
            id: 2,
            name: '李老师',
            avatar: '/assets/images/default_avatar.png',
            subject: '英语',
            experience: '3年教学经验',
            rating: 4.9
          }
        ]
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 跳转到发布订单页面
   */
  goToPublish() {
    wx.switchTab({
      url: '/pages/parent/publish/index'
    });
  },

  /**
   * 跳转到订单列表
   */
  goToOrderList() {
    wx.navigateTo({
      url: '/pages/parent/order/list/index'
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/parent/order/detail/index?id=${orderId}`
    });
  },

  /**
   * 查看教师详情
   */
  viewTeacherDetail(e) {
    const teacherId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/teacher/profile/detail/index?id=${teacherId}`
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadStats();
    this.loadRecentOrders();
    this.loadRecommendTeachers();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '众桥辅导 - 找到最适合的家教老师',
      path: '/pages/parent/home/<USER>',
      imageUrl: '/assets/images/share_logo.png'
    };
  }
});

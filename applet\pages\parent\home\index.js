// pages/parent/home/<USER>
const parentApi = require('../../../api/parent.js');

Page({
  data: {
    // 轮播图和通知
    bannerList: [],
    announcements: [],
    
    // 订单相关
    ongoingOrders: [],
    
    // 推荐教师
    recommendTeachers: [],
    
    // 状态
    loading: false,
    refreshing: false
  },

  onLoad() {
    this.loadPageData();
  },

  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 0
      });
    }

    // 刷新订单数据
    this.loadOngoingOrders();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ refreshing: true });
    this.loadPageData().finally(() => {
      this.setData({ refreshing: false });
    });
  },

  // 加载页面数据
  async loadPageData() {
    this.setData({ loading: true });
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadAnnouncements(),
        this.loadOngoingOrders(),
        this.loadRecommendTeachers()
      ]);
    } catch (error) {
      console.error('加载页面数据失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载家长端轮播图
  async loadBanners() {
    try {
      const res = await parentApi.getBannerList();
      if (res.code === 200) {
        this.setData({
          bannerList: res.data || []
        });
      }
    } catch (error) {
      console.error('加载家长端轮播图失败:', error);
      // 使用模拟数据
      this.setData({
        bannerList: [
          { id: 1, image: '/assets/images/banner1.jpg', title: '优质教师推荐', url: '' },
          { id: 2, image: '/assets/images/banner2.jpg', title: '家教服务', url: '' }
        ]
      });
    }
  },

  // 加载家长端通知公告
  async loadAnnouncements() {
    try {
      const res = await parentApi.getAnnouncementList();
      if (res.code === 200) {
        this.setData({
          announcements: res.data || []
        });
      }
    } catch (error) {
      console.error('加载家长端通知公告失败:', error);
      // 使用模拟数据
      this.setData({
        announcements: [
          {
            id: 1,
            title: '欢迎使用众桥辅导平台，为您提供优质的家教服务',
            content: '众桥辅导平台致力于为家长和学生提供优质的家教服务，我们拥有专业的教师团队和完善的服务体系。'
          },
          {
            id: 2,
            title: '平台新增在线辅导功能，支持远程教学',
            content: '为了更好地服务广大用户，平台新增了在线辅导功能，支持视频通话、屏幕共享等多种教学方式。'
          }
        ]
      });
    }
  },

  // 加载正在进行的订单
  async loadOngoingOrders() {
    try {
      const res = await parentApi.getOngoingOrders({ pageNum: 1, pageSize: 5 });
      if (res.code === 200) {
        const orders = (res.rows || []).map(order => ({
          ...order,
          statusText: this.getOrderStatusText(order.status),
          canPay: order.status === '1', // 待支付
          canEvaluate: order.status === '8' // 已完成
        }));
        
        this.setData({
          ongoingOrders: orders
        });
      }
    } catch (error) {
      console.error('加载正在进行的订单失败:', error);
      this.setData({
        ongoingOrders: []
      });
    }
  },

  // 加载推荐教师
  async loadRecommendTeachers() {
    try {
      console.log('开始加载推荐教师...');
      const res = await parentApi.getRecommendTeachers();
      console.log('推荐教师API响应:', res);

      if (res.code === 200) {
        const teachers = res.data || [];
        console.log('推荐教师数据:', teachers);
        console.log('推荐教师数量:', teachers.length);

        // 为每个教师添加认证状态显示
        const processedTeachers = teachers.map(teacher => ({
          ...teacher,
          // 确保所有必要字段都有默认值
          avatar: teacher.avatar || '/assets/images/default_avatar.png',
          university: teacher.university || '未填写',
          major: teacher.major || '未填写',
          subject: teacher.subject || '暂未设置',
          features: teacher.features || '该教师暂未填写自我介绍',
          grades: teacher.grades || '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三',
          certification_text: teacher.certification_text || '未认证'
        }));

        this.setData({
          recommendTeachers: processedTeachers
        });

        if (teachers.length === 0) {
          console.log('没有找到符合条件的推荐教师');
        }
      } else {
        console.error('推荐教师API返回错误:', res);
        this.setData({
          recommendTeachers: []
        });
        wx.showToast({
          title: res.msg || '获取推荐教师失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载推荐教师失败:', error);
      this.setData({
        recommendTeachers: []
      });
      wx.showToast({
        title: '网络错误，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 获取订单状态文本
  getOrderStatusText(status) {
    const statusMap = {
      '1': '待支付',
      '2': '待确认',
      '3': '已确认',
      '4': '试课中',
      '5': '试课通过',
      '6': '试课未通过',
      '7': '进行中',
      '8': '已完成',
      '9': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 轮播图点击
  onBannerTap(e) {
    const { item } = e.currentTarget.dataset;
    if (item.url) {
      wx.navigateTo({
        url: item.url
      });
    }
  },

  // 公告点击事件
  onAnnouncementTap(e) {
    const { index } = e.currentTarget.dataset;
    const announcement = this.data.announcements[index];

    if (!announcement) {
      return;
    }

    // 跳转到公告详情页面
    wx.navigateTo({
      url: `/pages/parent/announcement/detail/index?title=${encodeURIComponent(announcement.title)}&content=${encodeURIComponent(announcement.content || announcement.title)}&publishTime=${encodeURIComponent(announcement.publishTime || '刚刚')}`
    });
  },

  // 发布家教
  goToPublish() {
    wx.navigateTo({
      url: '/pages/parent/publish/index'
    });
  },

  // 查看所有订单
  viewAllOrders() {
    wx.navigateTo({
      url: '/pages/parent/orders/index'
    });
  },

  // 查看教师
  viewTeachers() {
    wx.navigateTo({
      url: '/pages/parent/teachers/index'
    });
  },

  // 查看教师详情
  viewTeacherDetail(e) {
    const teacher = e.currentTarget.dataset.teacher;
    console.log('查看教师详情:', teacher);

    // 跳转到教师详情页面
    wx.navigateTo({
      url: `/pages/teacher/detail/index?teacherId=${teacher.id}`
    });
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/parent/order/detail/index?id=${id}`
    });
  },

  // 支付订单
  payOrder(e) {
    const { id } = e.currentTarget.dataset;
    wx.showModal({
      title: '支付订单',
      content: '确定要支付此订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '支付功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 评价订单
  evaluateOrder(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/parent/reviews/index?orderId=${id}`
    });
  },

  // 预约教师
  bookTeacher(e) {
    const { teacher } = e.currentTarget.dataset;

    wx.showModal({
      title: '预约教师',
      content: `确定要预约 ${teacher.name} 老师吗？预约后将引导您发布家教需求。`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到发布页面，并传递教师信息
          wx.navigateTo({
            url: `/pages/parent/publish/index?teacherId=${teacher.id}&teacherName=${teacher.name}`
          });
        }
      }
    });
  },

  // 底部导航栏方法
  goToHome() {
    // 当前就在首页，不需要跳转
  },

  goToProfile() {
    wx.navigateTo({
      url: '/pages/parent/profile/index'
    });
  }
});

package com.ruoyi.education.service;

import java.util.List;
import com.ruoyi.education.domain.TutorOrder;

/**
 * 家教订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
public interface ITutorOrderService 
{
    /**
     * 查询家教订单
     * 
     * @param id 家教订单主键
     * @return 家教订单
     */
    public TutorOrder selectTutorOrderById(Long id);

    /**
     * 查询家教订单列表
     * 
     * @param tutorOrder 家教订单
     * @return 家教订单集合
     */
    public List<TutorOrder> selectTutorOrderList(TutorOrder tutorOrder);

    /**
     * 新增家教订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int insertTutorOrder(TutorOrder tutorOrder);

    /**
     * 修改家教订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int updateTutorOrder(TutorOrder tutorOrder);

    /**
     * 批量删除家教订单
     * 
     * @param ids 需要删除的家教订单主键集合
     * @return 结果
     */
    public int deleteTutorOrderByIds(Long[] ids);

    /**
     * 删除家教订单信息
     * 
     * @param id 家教订单主键
     * @return 结果
     */
    public int deleteTutorOrderById(Long id);

    /**
     * 管理员代发布订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    public int publishOrderByAdmin(TutorOrder tutorOrder);

    /**
     * 选择教师
     * 
     * @param orderId 订单ID
     * @param teacherId 教师ID
     * @return 结果
     */
    public int selectTeacher(Long orderId, Long teacherId);

    /**
     * 设置置顶
     *
     * @param id 订单ID
     * @param isTop 是否置顶 (0-否 1-是)
     * @return 结果
     */
    public int setTop(Long id, String isTop);

    /**
     * 审核家教订单
     *
     * @param id 订单ID
     * @param status 审核状态
     * @param auditReason 审核原因
     * @return 结果
     */
    public int auditTutorOrder(Long id, String status, String auditReason);

    /**
     * 设置订单置顶（别名方法）
     *
     * @param id 订单ID
     * @param isTop 是否置顶
     * @return 结果
     */
    public int setTutorOrderTop(Long id, String isTop);

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 家教订单
     */
    public TutorOrder selectTutorOrderByOrderNo(String orderNo);

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    public String generateOrderNo();

    /**
     * 统计订单数量（按状态）
     * 
     * @param status 订单状态
     * @return 订单数量
     */
    public int countOrdersByStatus(String status);

    /**
     * 查询待审核订单列表
     *
     * @return 待审核订单集合
     */
    public List<TutorOrder> selectPendingAuditOrders();

    /**
     * 查询置顶订单列表
     * 
     * @return 置顶订单集合
     */
    public List<TutorOrder> selectTopOrders();

    /**
     * 获取订单统计信息
     * 
     * @return 统计信息
     */
    public String getOrderStatistics();

    /**
     * 查询小程序端-新家教订单列表
     * @return
     */
    public List<TutorOrder> selectNewTutorOrderList();

    /**
     * 查询小程序端-教师已报名的订单列表
     * @param teacherId
     * @return
     */
    public List<TutorOrder> selectAppliedTutorOrderListByTeacherId(Long teacherId);

    /**
     * 查询小程序端-教师已完成的订单列表
     * @param teacherId
     * @return
     */
    public List<TutorOrder> selectCompletedTutorOrderListByTeacherId(Long teacherId);

    /**
     * 查询小程序端-教师进行中的订单列表
     * @param teacherId
     * @return
     */
    public List<TutorOrder> selectOngoingTutorOrderListByTeacherId(Long teacherId);

    /**
     * 将订单状态流转为进行中
     * @param orderId
     * @return
     */
    public int updateOrderStatusToOngoing(Long orderId);

    /**
     * 查询教师未选中的订单列表（已报名但未被选中）
     * @param teacherId 教师ID
     * @return 未选中订单集合
     */
    public List<TutorOrder> selectUnselectedTutorOrderListByTeacherId(Long teacherId);

    /**
     * 将订单状态流转为已完成
     * @param orderId
     * @return
     */
    public int updateOrderStatusToCompleted(Long orderId);

    /**
     * 统计家长发布的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    public int countOrdersByParentId(Long parentId);

    /**
     * 统计家长进行中的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    public int countOngoingOrdersByParentId(Long parentId);

    /**
     * 统计家长已完成的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    public int countCompletedOrdersByParentId(Long parentId);

    /**
     * 查询家长最近的订单
     * @param parentId 家长ID
     * @param limit 限制数量
     * @return 订单列表
     */
    public List<TutorOrder> selectRecentOrdersByParentId(Long parentId, int limit);

    /**
     * 获取推荐教师
     * @param limit 限制数量
     * @return 推荐教师列表
     */
    public List<java.util.Map<String, Object>> getRecommendTeachers(int limit);

    /**
     * 获取教师详情
     * @param teacherId 教师ID
     * @return 教师详情
     */
    public java.util.Map<String, Object> getTeacherDetail(Long teacherId);

    /**
     * 查询家长发布的所有订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    public List<TutorOrder> selectOrdersByParentId(Long parentId);

    /**
     * 查询家长进行中的订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    public List<TutorOrder> selectOngoingOrdersByParentId(Long parentId);

    /**
     * 查询家长已完成的订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    public List<TutorOrder> selectCompletedOrdersByParentId(Long parentId);
}
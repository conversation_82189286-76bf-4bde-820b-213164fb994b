# 登录接口404问题诊断

## 🎯 问题现象

1. **登录接口全报404**
2. **TabBar.list有5个项目**（已修复）

---

## 🔍 问题分析

### 1. TabBar配置问题 ✅ 已修复

**问题**：`app.json`中TabBar.list有5个项目
```json
"list": [
  {"pagePath": "pages/teacher/home/<USER>", "text": "首页"},
  {"pagePath": "pages/teacher/workspace/index", "text": "工作台"},
  {"pagePath": "pages/teacher/student_source/index", "text": "生源"},
  {"pagePath": "pages/teacher/profile/index", "text": "我的"},
  {"pagePath": "pages/parent/home/<USER>", "text": "首页"} // 多余的第5个
]
```

**修复**：移除第5个项目，保持4个TabBar项
```json
"list": [
  {"pagePath": "pages/teacher/home/<USER>", "text": "首页"},
  {"pagePath": "pages/teacher/workspace/index", "text": "工作台"},
  {"pagePath": "pages/teacher/student_source/index", "text": "生源"},
  {"pagePath": "pages/teacher/profile/index", "text": "我的"}
]
```

### 2. 登录接口404问题分析

#### 前端配置
- **请求URL**：`/applet/auth/login`
- **baseURL**：`http://t8f63f47.natappfree.cc`
- **完整URL**：`http://t8f63f47.natappfree.cc/applet/auth/login`

#### 后端配置
- **控制器**：`AppletLoginController`
- **映射路径**：`@RequestMapping("/applet/auth")`
- **方法路径**：`@PostMapping("/login")`
- **完整路径**：`/applet/auth/login` ✅

#### 服务器配置
- **端口**：8080
- **natapp映射**：`u984cf73.natappfree.cc` → `localhost:8080`

---

## 🧪 诊断步骤

### 1. 检查后端服务状态

#### 启动后端服务
```bash
# 进入后端目录
cd RuoYi-Vue-master

# 启动服务（Windows）
.\ry.bat

# 或者使用Maven启动
mvn spring-boot:run -pl ruoyi-admin
```

#### 验证服务启动
- 访问：`http://localhost:8080`
- 检查控制台日志是否有错误
- 确认端口8080被正确监听

### 2. 检查natapp隧道状态

#### natapp配置检查
- 确认natapp客户端正在运行
- 检查隧道状态：`u984cf73.natappfree.cc` → `localhost:8080`
- 测试基础连通性：访问 `http://t8f63f47.natappfree.cc`

#### 可能的natapp问题
- 隧道断开或过期
- 端口映射错误
- 免费版限制（流量、时间等）

### 3. 接口路径验证

#### 直接测试后端接口
```bash
# 本地测试
curl -X POST http://localhost:8080/applet/auth/login \
  -H "Content-Type: application/json" \
  -d '{"code":"mock_code","userType":"teacher","nickName":"测试","avatar":""}'

# natapp测试
curl -X POST http://t8f63f47.natappfree.cc/applet/auth/login \
  -H "Content-Type: application/json" \
  -d '{"code":"mock_code","userType":"teacher","nickName":"测试","avatar":""}'
```

### 4. 使用调试页面

已创建调试页面：`pages/debug-login/index`
- 测试登录接口连通性
- 检查详细错误信息
- 验证请求参数和响应

---

## 🔧 可能的解决方案

### 方案1：检查并重启后端服务

```bash
# 1. 检查Java进程
jps -l | grep ruoyi

# 2. 停止现有服务
# 找到进程ID并kill

# 3. 重新启动
cd RuoYi-Vue-master
.\ry.bat
```

### 方案2：更新natapp配置

```bash
# 1. 重启natapp客户端
# 2. 检查隧道配置
# 3. 确认端口映射：8080 → u984cf73.natappfree.cc
```

### 方案3：使用本地IP地址

如果natapp有问题，临时使用本地IP：
```javascript
// applet/config/index.js
const development = {
  baseUrl: 'http://*************:8080', // 使用本地IP
  debug: true,
  uploadMaxSize: 10
};
```

### 方案4：检查防火墙和网络

```bash
# Windows检查端口占用
netstat -ano | findstr :8080

# 检查防火墙设置
# 确保8080端口对外开放
```

---

## 📋 检查清单

### 后端服务
- [ ] 后端服务正常启动
- [ ] 端口8080正常监听
- [ ] 控制台无错误日志
- [ ] 数据库连接正常

### 网络连接
- [ ] natapp客户端运行正常
- [ ] 隧道状态正常
- [ ] 基础连通性测试通过
- [ ] 防火墙配置正确

### 接口配置
- [ ] 控制器路径正确：`/applet/auth`
- [ ] 方法路径正确：`/login`
- [ ] 请求方法正确：`POST`
- [ ] 请求参数格式正确

### 前端配置
- [ ] baseURL配置正确
- [ ] 请求工具正常工作
- [ ] TabBar配置修复完成
- [ ] 调试页面可以使用

---

## 🚀 下一步行动

1. **立即检查**：
   - 后端服务是否正在运行
   - natapp隧道是否正常

2. **使用调试页面**：
   - 访问 `pages/debug-login/index`
   - 查看详细错误信息

3. **备用方案**：
   - 如果natapp有问题，切换到本地IP
   - 如果后端有问题，重新启动服务

4. **验证修复**：
   - 登录功能正常
   - TabBar动态切换正常
   - 家长端页面正常访问

---

## 📞 紧急联系

如果问题持续存在，请检查：
1. 后端控制台日志
2. 小程序开发者工具Network面板
3. natapp客户端状态
4. 本地网络连接

现在请先使用调试页面测试接口连通性，然后根据结果进行相应的修复！

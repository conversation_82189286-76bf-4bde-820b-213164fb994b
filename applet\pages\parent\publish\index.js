// pages/parent/publish/index.js
const parentApi = require('../../../api/parent.js');

Page({
  data: {
    // 预约教师相关
    preferredTeacherId: null,
    preferredTeacherName: '',
    isBookingTeacher: false,

    formData: {
      // 与后台TutorOrder实体类字段完全一致
      orderType: '1', // 订单类型：1-家教订单
      subjectName: '', // 科目名称
      grade: '', // 年级
      studentGender: '2', // 学生性别：0-男，1-女，2-不限
      tutoringMode: '上门辅导', // 辅导模式
      studentDetails: '', // 学生详情
      availableTutoringTime: '', // 可辅导时间
      weeklySessions: '', // 每周辅导次数
      tutoringTimeSlots: '', // 辅导时间段
      sessionDuration: '', // 每次辅导时长
      timeSlotNotes: '', // 时段补充说明
      teacherGender: '不限', // 教师性别要求
      teacherCategory: '', // 教师类别
      teachingType: '1', // 授课方式：1-上门授课，2-在线授课，3-指定地点
      salary: '', // 薪资
      salaryUnit: '1', // 薪资单位：1-小时，2-天，3-周，4-月
      city: '', // 城市
      detailedAddress: '', // 详细地址
      contactName: '', // 联系人姓名
      contactPhone: '', // 联系电话
      teachingRequirements: '', // 教学要求
      publisherType: '1', // 发布者类型：1-家长
      preferredTeacherId: null // 指定教师ID
    },

    // 选择器数据
    subjects: [], // 从后台获取

    // 年级二级选项
    gradeOptions: [
      [
        {name: '小学', value: 'primary'},
        {name: '初中', value: 'middle'},
        {name: '高中', value: 'high'},
        {name: '大学', value: 'university'},
        {name: '其他', value: 'other'}
      ],
      [
        {name: '一年级', value: '1'},
        {name: '二年级', value: '2'},
        {name: '三年级', value: '3'},
        {name: '四年级', value: '4'},
        {name: '五年级', value: '5'},
        {name: '六年级', value: '6'}
      ]
    ],

    // 周一到周日选项
    weekdays: [
      {name: '周一', value: '1'},
      {name: '周二', value: '2'},
      {name: '周三', value: '3'},
      {name: '周四', value: '4'},
      {name: '周五', value: '5'},
      {name: '周六', value: '6'},
      {name: '周日', value: '7'}
    ],

    // 称谓选项
    titleOptions: [
      {name: '先生', value: 'mr'},
      {name: '女士', value: 'ms'}
    ],

    teacherCategories: [
      '在校大学生', '专职教师', '退休教师', '培训机构教师', '其他'
    ],

    salaryUnits: [
      {value: '1', name: '元/小时'},
      {value: '2', name: '元/天'},
      {value: '3', name: '元/周'},
      {value: '4', name: '元/月'}
    ],

    // 选择器索引
    subjectIndex: -1,
    gradeIndex: [0, 0], // 二级选择器索引
    teacherCategoryIndex: -1,
    salaryUnitIndex: 0,
    titleIndex: 0,

    // 选中的周几
    selectedWeekdays: [],

    submitting: false
  },

  onLoad(options) {
    console.log('发布家教页面加载，参数:', options);

    // 检查是否是预约教师
    if (options.preferredTeacherId) {
      this.setData({
        preferredTeacherId: options.preferredTeacherId,
        preferredTeacherName: decodeURIComponent(options.preferredTeacherName || ''),
        isBookingTeacher: true,
        'formData.preferredTeacherId': options.preferredTeacherId
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: `预约${this.data.preferredTeacherName}`
      });
    }

    this.initUserInfo();
    this.loadSubjects(); // 加载科目数据
  },

  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 1
      });
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        'formData.contactName': userInfo.nickName || userInfo.realName || '',
        'formData.contactPhone': userInfo.phoneNumber || '',
        'formData.contactTitle': '先生' // 默认称谓
      });
    }
  },

  // 加载科目数据
  async loadSubjects() {
    try {
      const parentApi = require('../../../api/parent.js');
      const res = await parentApi.getSubjects();

      if (res.code === 200 && res.data) {
        this.setData({ subjects: res.data });
      } else {
        // 如果后台接口失败，使用默认数据
        this.setData({
          subjects: [
            {id: 1, name: '语文'},
            {id: 2, name: '数学'},
            {id: 3, name: '英语'},
            {id: 4, name: '物理'},
            {id: 5, name: '化学'},
            {id: 6, name: '生物'},
            {id: 7, name: '历史'},
            {id: 8, name: '地理'},
            {id: 9, name: '政治'},
            {id: 10, name: '其他'}
          ]
        });
      }
    } catch (error) {
      console.error('加载科目失败:', error);
      // 使用默认数据
      this.setData({
        subjects: [
          {id: 1, name: '语文'},
          {id: 2, name: '数学'},
          {id: 3, name: '英语'},
          {id: 4, name: '物理'},
          {id: 5, name: '化学'},
          {id: 6, name: '生物'},
          {id: 7, name: '历史'},
          {id: 8, name: '地理'},
          {id: 9, name: '政治'},
          {id: 10, name: '其他'}
        ]
      });
    }
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 科目选择
  onSubjectChange(e) {
    const index = e.detail.value;
    const subject = this.data.subjects[index];
    this.setData({
      subjectIndex: index,
      'formData.subjectName': subject.name
    });
  },

  // 年级选择
  onGradeChange(e) {
    const [levelIndex, gradeIndex] = e.detail.value;
    const level = this.data.gradeOptions[0][levelIndex];
    const grade = this.data.gradeOptions[1][gradeIndex];
    const gradeText = `${level.name}${grade.name}`;

    this.setData({
      gradeIndex: [levelIndex, gradeIndex],
      'formData.grade': gradeText
    });
  },

  // 年级列变化
  onGradeColumnChange(e) {
    const { column, value } = e.detail;
    if (column === 0) {
      // 第一列变化，更新第二列选项
      const gradeOptions = [...this.data.gradeOptions];

      switch(value) {
        case 0: // 小学
          gradeOptions[1] = [
            {name: '一年级', value: '1'},
            {name: '二年级', value: '2'},
            {name: '三年级', value: '3'},
            {name: '四年级', value: '4'},
            {name: '五年级', value: '5'},
            {name: '六年级', value: '6'}
          ];
          break;
        case 1: // 初中
        case 2: // 高中
          gradeOptions[1] = [
            {name: '一年级', value: '1'},
            {name: '二年级', value: '2'},
            {name: '三年级', value: '3'}
          ];
          break;
        case 3: // 大学
          gradeOptions[1] = [
            {name: '大一', value: '1'},
            {name: '大二', value: '2'},
            {name: '大三', value: '3'},
            {name: '大四', value: '4'}
          ];
          break;
        case 4: // 其他
          gradeOptions[1] = [
            {name: '成人教育', value: 'adult'},
            {name: '其他', value: 'other'}
          ];
          break;
      }

      this.setData({
        gradeOptions: gradeOptions
      });
    }
  },

  // 学生性别选择
  onGenderChange(e) {
    this.setData({
      'formData.studentGender': e.detail.value
    });
  },

  // 辅导模式选择
  onModeChange(e) {
    this.setData({
      'formData.tutoringMode': e.detail.value
    });
  },

  // 授课方式选择
  onTeachingTypeChange(e) {
    this.setData({
      'formData.teachingType': e.detail.value
    });
  },

  // 教师性别选择
  onTeacherGenderChange(e) {
    this.setData({
      'formData.teacherGender': e.detail.value
    });
  },

  // 教师类别选择
  onTeacherCategoryChange(e) {
    const index = e.detail.value;
    const category = this.data.teacherCategories[index];
    this.setData({
      teacherCategoryIndex: index,
      'formData.teacherCategory': category
    });
  },

  // 周几选择
  toggleWeekday(e) {
    const { value } = e.currentTarget.dataset;
    let selectedWeekdays = [...this.data.selectedWeekdays];

    const index = selectedWeekdays.indexOf(value);
    if (index > -1) {
      selectedWeekdays.splice(index, 1);
    } else {
      selectedWeekdays.push(value);
    }

    // 更新显示文本
    const weekdayNames = selectedWeekdays.map(val => {
      const weekday = this.data.weekdays.find(w => w.value === val);
      return weekday ? weekday.name : '';
    }).join('、');

    this.setData({
      selectedWeekdays: selectedWeekdays,
      'formData.availableTutoringTime': weekdayNames
    });
  },

  // 开始时间选择
  onStartTimeChange(e) {
    this.setData({
      'formData.startTime': e.detail.value
    });
  },

  // 结束时间选择
  onEndTimeChange(e) {
    this.setData({
      'formData.endTime': e.detail.value
    });
  },

  // 称谓选择
  onTitleChange(e) {
    const index = e.detail.value;
    const title = this.data.titleOptions[index];
    this.setData({
      titleIndex: index,
      'formData.contactTitle': title.name
    });
  },

  // 薪资单位选择
  onSalaryUnitChange(e) {
    const index = e.detail.value;
    const unit = this.data.salaryUnits[index];
    this.setData({
      salaryUnitIndex: index,
      'formData.salaryUnit': unit.value,
      'formData.salaryUnitText': unit.name
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.subjectName) {
      wx.showToast({ title: '请选择辅导科目', icon: 'none' });
      return false;
    }

    if (!formData.grade) {
      wx.showToast({ title: '请选择学生年级', icon: 'none' });
      return false;
    }

    if (!formData.salary) {
      wx.showToast({ title: '请输入薪资', icon: 'none' });
      return false;
    }

    if (!formData.city) {
      wx.showToast({ title: '请输入所在城市', icon: 'none' });
      return false;
    }

    if (!formData.contactName) {
      wx.showToast({ title: '请输入联系人姓名', icon: 'none' });
      return false;
    }

    if (!formData.contactPhone) {
      wx.showToast({ title: '请输入联系电话', icon: 'none' });
      return false;
    }

    return true;
  },

  // 提交订单
  async submitOrder() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      const res = await parentApi.publishOrder(this.data.formData);

      if (res.code === 200) {
        wx.showToast({
          title: '发布成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '发布失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发布订单失败:', error);
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  }
});

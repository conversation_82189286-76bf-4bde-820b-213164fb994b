// pages/parent/publish/index.js
const { request } = require('../../../utils/request.js');
const app = getApp();

Page({
  data: {
    // 基本信息
    title: '',
    subjectName: '',
    grade: '',
    studentGender: '不限',
    
    // 辅导详情
    tutoringMode: '上门辅导',
    studentDetails: '',
    availableTutoringTime: '',
    weeklySessionsIndex: 0,
    weeklySessionsOptions: ['1次/周', '2次/周', '3次/周', '4次/周', '5次/周'],
    sessionDurationIndex: 0,
    sessionDurationOptions: ['1小时', '1.5小时', '2小时', '2.5小时', '3小时'],
    
    // 教师要求
    teacherGender: '不限',
    teacherCategory: '不限',
    teachingType: '不限',
    teachingRequirements: '',
    
    // 薪资和地址
    salary: '',
    salaryUnit: '元/小时',
    city: '',
    detailedAddress: '',
    
    // 联系方式
    contactName: '',
    contactPhone: '',
    
    // 选择器数据
    genderOptions: ['不限', '男', '女'],
    genderIndex: 0,
    teacherGenderIndex: 0,
    
    modeOptions: ['上门辅导', '学生上门', '在线辅导'],
    modeIndex: 0,
    
    categoryOptions: ['不限', '在校教师', '专业教师', '大学生'],
    categoryIndex: 0,
    
    typeOptions: ['不限', '一对一', '一对多'],
    typeIndex: 0,
    
    unitOptions: ['元/小时', '元/次', '元/月'],
    unitIndex: 0,
    
    // 状态
    isSubmitting: false
  },

  /**
   * 页面加载
   */
  onLoad() {
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 1
      });
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    wx.setNavigationBarTitle({
      title: '发布家教需求'
    });
    
    // 获取用户信息，预填联系方式
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        contactName: userInfo.nickName || '',
        contactPhone: userInfo.phone || ''
      });
    }
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [field]: value
    });
  },

  /**
   * 选择器变化处理
   */
  onPickerChange(e) {
    const { field, options } = e.currentTarget.dataset;
    const index = e.detail.value;
    const value = this.data[options][index];
    
    this.setData({
      [`${field}Index`]: index,
      [field]: value
    });
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const index = e.detail.value;
    this.setData({
      genderIndex: index,
      studentGender: this.data.genderOptions[index]
    });
  },

  /**
   * 教师性别选择
   */
  onTeacherGenderChange(e) {
    const index = e.detail.value;
    this.setData({
      teacherGenderIndex: index,
      teacherGender: this.data.genderOptions[index]
    });
  },

  /**
   * 辅导方式选择
   */
  onModeChange(e) {
    const index = e.detail.value;
    this.setData({
      modeIndex: index,
      tutoringMode: this.data.modeOptions[index]
    });
  },

  /**
   * 教师类别选择
   */
  onCategoryChange(e) {
    const index = e.detail.value;
    this.setData({
      categoryIndex: index,
      teacherCategory: this.data.categoryOptions[index]
    });
  },

  /**
   * 教学类型选择
   */
  onTypeChange(e) {
    const index = e.detail.value;
    this.setData({
      typeIndex: index,
      teachingType: this.data.typeOptions[index]
    });
  },

  /**
   * 薪资单位选择
   */
  onUnitChange(e) {
    const index = e.detail.value;
    this.setData({
      unitIndex: index,
      salaryUnit: this.data.unitOptions[index]
    });
  },

  /**
   * 每周次数选择
   */
  onWeeklySessionsChange(e) {
    const index = e.detail.value;
    this.setData({
      weeklySessionsIndex: index
    });
  },

  /**
   * 每次时长选择
   */
  onSessionDurationChange(e) {
    const index = e.detail.value;
    this.setData({
      sessionDurationIndex: index
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { title, subjectName, grade, salary, contactName, contactPhone } = this.data;
    
    if (!title.trim()) {
      wx.showToast({ title: '请输入订单标题', icon: 'none' });
      return false;
    }
    
    if (!subjectName.trim()) {
      wx.showToast({ title: '请输入辅导科目', icon: 'none' });
      return false;
    }
    
    if (!grade.trim()) {
      wx.showToast({ title: '请输入学生年级', icon: 'none' });
      return false;
    }
    
    if (!salary.trim()) {
      wx.showToast({ title: '请输入期望薪资', icon: 'none' });
      return false;
    }
    
    if (!contactName.trim()) {
      wx.showToast({ title: '请输入联系人姓名', icon: 'none' });
      return false;
    }
    
    if (!contactPhone.trim()) {
      wx.showToast({ title: '请输入联系电话', icon: 'none' });
      return false;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(contactPhone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }
    
    return true;
  },

  /**
   * 提交订单
   */
  async submitOrder() {
    if (!this.validateForm()) {
      return;
    }
    
    if (this.data.isSubmitting) {
      return;
    }
    
    this.setData({ isSubmitting: true });
    
    try {
      const orderData = {
        title: this.data.title,
        subjectName: this.data.subjectName,
        grade: this.data.grade,
        studentGender: this.data.studentGender,
        tutoringMode: this.data.tutoringMode,
        studentDetails: this.data.studentDetails,
        availableTutoringTime: this.data.availableTutoringTime,
        weeklySessions: this.data.weeklySessionsOptions[this.data.weeklySessionsIndex],
        sessionDuration: this.data.sessionDurationOptions[this.data.sessionDurationIndex],
        teacherGender: this.data.teacherGender,
        teacherCategory: this.data.teacherCategory,
        teachingType: this.data.teachingType,
        teachingRequirements: this.data.teachingRequirements,
        salary: this.data.salary,
        salaryUnit: this.data.salaryUnit,
        city: this.data.city,
        detailedAddress: this.data.detailedAddress,
        contactName: this.data.contactName,
        contactPhone: this.data.contactPhone,
        orderType: '1', // 家教订单
        publisherType: '1' // 家长发布
      };
      
      const res = await request({
        url: '/applet/parent/orders/create',
        method: 'POST',
        data: orderData
      });
      
      if (res.code === 200) {
        wx.showToast({
          title: '发布成功',
          icon: 'success'
        });
        
        // 跳转到订单详情页
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/parent/order/detail/index?id=${res.data.id}`
          });
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '发布失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发布订单失败:', error);
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmitting: false });
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            title: '',
            subjectName: '',
            grade: '',
            studentGender: '不限',
            tutoringMode: '上门辅导',
            studentDetails: '',
            availableTutoringTime: '',
            weeklySessionsIndex: 0,
            sessionDurationIndex: 0,
            teacherGender: '不限',
            teacherCategory: '不限',
            teachingType: '不限',
            teachingRequirements: '',
            salary: '',
            salaryUnit: '元/小时',
            city: '',
            detailedAddress: '',
            genderIndex: 0,
            teacherGenderIndex: 0,
            modeIndex: 0,
            categoryIndex: 0,
            typeIndex: 0,
            unitIndex: 0
          });
        }
      }
    });
  }
});

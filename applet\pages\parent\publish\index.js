// pages/parent/publish/index.js
const parentApi = require('../../../api/parent.js');

Page({
  data: {
    formData: {
      // 与后台TutorOrder实体类字段完全一致
      orderType: '1', // 订单类型：1-家教订单
      subjectName: '', // 科目名称
      grade: '', // 年级
      studentGender: '2', // 学生性别：0-男，1-女，2-不限
      tutoringMode: '上门辅导', // 辅导模式
      studentDetails: '', // 学生详情
      availableTutoringTime: '', // 可辅导时间
      weeklySessions: '', // 每周辅导次数
      tutoringTimeSlots: '', // 辅导时间段
      sessionDuration: '', // 每次辅导时长
      timeSlotNotes: '', // 时段补充说明
      teacherGender: '不限', // 教师性别要求
      teacherCategory: '', // 教师类别
      teachingType: '1', // 授课方式：1-上门授课，2-在线授课，3-指定地点
      salary: '', // 薪资
      salaryUnit: '1', // 薪资单位：1-小时，2-天，3-周，4-月
      city: '', // 城市
      detailedAddress: '', // 详细地址
      contactName: '', // 联系人姓名
      contactPhone: '', // 联系电话
      teachingRequirements: '', // 教学要求
      publisherType: '1' // 发布者类型：1-家长
    },

    // 选择器数据
    subjects: [
      {id: 1, name: '语文'},
      {id: 2, name: '数学'},
      {id: 3, name: '英语'},
      {id: 4, name: '物理'},
      {id: 5, name: '化学'},
      {id: 6, name: '生物'},
      {id: 7, name: '历史'},
      {id: 8, name: '地理'},
      {id: 9, name: '政治'},
      {id: 10, name: '其他'}
    ],

    grades: [
      '小学一年级', '小学二年级', '小学三年级', '小学四年级', '小学五年级', '小学六年级',
      '初中一年级', '初中二年级', '初中三年级',
      '高中一年级', '高中二年级', '高中三年级',
      '大学', '成人教育', '其他'
    ],

    teacherCategories: [
      '在校大学生', '专职教师', '退休教师', '培训机构教师', '其他'
    ],

    salaryUnits: [
      {value: '1', name: '元/小时'},
      {value: '2', name: '元/天'},
      {value: '3', name: '元/周'},
      {value: '4', name: '元/月'}
    ],

    // 选择器索引
    subjectIndex: -1,
    gradeIndex: -1,
    teacherCategoryIndex: -1,
    salaryUnitIndex: 0,

    submitting: false
  },

  onLoad() {
    this.initUserInfo();
  },

  onShow() {
    // 设置TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        userType: 'parent',
        selected: 1
      });
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        'formData.contactName': userInfo.nickName || userInfo.realName || '',
        'formData.contactPhone': userInfo.phoneNumber || ''
      });
    }
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 科目选择
  onSubjectChange(e) {
    const index = e.detail.value;
    const subject = this.data.subjects[index];
    this.setData({
      subjectIndex: index,
      'formData.subjectName': subject.name
    });
  },

  // 年级选择
  onGradeChange(e) {
    const index = e.detail.value;
    const grade = this.data.grades[index];
    this.setData({
      gradeIndex: index,
      'formData.grade': grade
    });
  },

  // 学生性别选择
  onGenderChange(e) {
    this.setData({
      'formData.studentGender': e.detail.value
    });
  },

  // 辅导模式选择
  onModeChange(e) {
    this.setData({
      'formData.tutoringMode': e.detail.value
    });
  },

  // 授课方式选择
  onTeachingTypeChange(e) {
    this.setData({
      'formData.teachingType': e.detail.value
    });
  },

  // 教师性别选择
  onTeacherGenderChange(e) {
    this.setData({
      'formData.teacherGender': e.detail.value
    });
  },

  // 教师类别选择
  onTeacherCategoryChange(e) {
    const index = e.detail.value;
    const category = this.data.teacherCategories[index];
    this.setData({
      teacherCategoryIndex: index,
      'formData.teacherCategory': category
    });
  },

  // 薪资单位选择
  onSalaryUnitChange(e) {
    const index = e.detail.value;
    const unit = this.data.salaryUnits[index];
    this.setData({
      salaryUnitIndex: index,
      'formData.salaryUnit': unit.value,
      'formData.salaryUnitText': unit.name
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.subjectName) {
      wx.showToast({ title: '请选择辅导科目', icon: 'none' });
      return false;
    }

    if (!formData.grade) {
      wx.showToast({ title: '请选择学生年级', icon: 'none' });
      return false;
    }

    if (!formData.salary) {
      wx.showToast({ title: '请输入薪资', icon: 'none' });
      return false;
    }

    if (!formData.city) {
      wx.showToast({ title: '请输入所在城市', icon: 'none' });
      return false;
    }

    if (!formData.contactName) {
      wx.showToast({ title: '请输入联系人姓名', icon: 'none' });
      return false;
    }

    if (!formData.contactPhone) {
      wx.showToast({ title: '请输入联系电话', icon: 'none' });
      return false;
    }

    return true;
  },

  // 提交订单
  async submitOrder() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      const res = await parentApi.publishOrder(this.data.formData);

      if (res.code === 200) {
        wx.showToast({
          title: '发布成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '发布失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发布订单失败:', error);
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  }
});

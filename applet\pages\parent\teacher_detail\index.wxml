<!--教师详情页面-->
<view class="teacher-detail-page">
  <!-- 自定义导航栏 -->
  <navigation-bar title="{{teacherDetail.display_name || '教师详情'}}" show-back="{{true}}" />

  <scroll-view class="page-content tabbar-scroll-view"
  scroll-y="{{true}}"
  refresher-enabled="{{true}}"
  refresher-triggered="{{isLoading}}"
  bindrefresherrefresh="onPullDownRefresh"
  bindscrolltolower="onReachBottom">
    <!-- 教师头像和基本信息 -->
    <view class="teacher-profile-card">
      <view class="avatar-section">
        <image class="teacher-avatar" src="{{teacherDetail.avatar || '/assets/images/default_avatar.png'}}" mode="aspectFill"></image>
        <view class="certification-badge {{teacherDetail.certification_status == '4' ? 'verified' : 'normal'}}">
          {{teacherDetail.certification_text || '未认证'}}
        </view>
      </view>

      <view class="basic-info">
        <view class="teacher-name">{{teacherDetail.display_name || '未知老师'}}</view>
        <view class="teacher-meta">
          <text class="meta-tag">{{teacherDetail.age || '未知'}}岁</text>
          <text class="meta-tag">{{teacherDetail.gender_text || '未知'}}</text>
        </view>
      </view>
    </view>

    <!-- 教学信息卡片 -->
    <view class="info-card">
      <view class="card-title">教授科目</view>
      <view class="info-grid">
        <view class="info-item">
          <view class="info-value">{{teacherDetail.teaching_subjects || '暂未设置'}}</view>
        </view>
      </view>
    </view>

    <!-- 教育背景 -->
    <view class="content-card" wx:if="{{educationList && educationList.length > 0}}">
      <view class="card-title">教育背景</view>
      <view class="education-list">
        <block wx:for="{{educationList}}" wx:key="index">
          <view class="education-item">
            <view class="education-school">{{item.school}}</view>
            <view class="education-details">
              <text class="education-degree">{{item.degree}}</text>
              <text class="education-major">{{item.major}}</text>
              <text class="education-time" wx:if="{{item.timeRange}}">{{item.timeRange}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 教学经历 -->
    <view class="content-card" wx:if="{{experienceList && experienceList.length > 0}}">
      <view class="card-title">教学经历</view>
      <view class="experience-list">
        <block wx:for="{{experienceList}}" wx:key="index">
          <view class="teaching-experience-item">
            <view class="experience-header">
              <text class="experience-target">{{item.teachingTarget}}</text>
              <text class="experience-time" wx:if="{{item.timeRange}}">{{item.timeRange}}</text>
            </view>
            <view class="experience-content" wx:if="{{item.content}}">{{item.content}}</view>
          </view>
        </block>
      </view>
    </view>

    <!-- 教学统计 -->
    <view class="stats-card">
      <view class="stats-grid-two">
        <view class="stat-item">
          <view class="stat-number">{{teacherDetail.total_orders || 0}}</view>
          <view class="stat-label">累计接单</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{teacherDetail.success_orders || 0}}</view>
          <view class="stat-label">成功完成</view>
        </view>
      </view>
    </view>

    <!-- 接课成功经历 -->
    <view class="content-card" wx:if="{{successExperienceList && successExperienceList.length > 0}}">
      <view class="card-title">接课成功经历</view>
      <view class="experience-list">
        <block wx:for="{{successExperienceList}}" wx:key="orderId">
          <view class="experience-item">
            <view class="experience-header">
              <text class="subject-grade">{{item.subjectName}} · {{item.grade}}</text>
              <text class="completed-time">{{item.completedTime}}</text>
            </view>
            <view class="experience-content">
              <text class="improvement">{{item.improvement}}</text>
              <text class="feedback">家长反馈：{{item.feedback}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 预约按钮 -->
    <view class="action-section">
      <button class="book-btn" bindtap="bookTeacher">
        <text class="btn-text">预约TA</text>
      </button>
    </view>
  </scroll-view>
</view>

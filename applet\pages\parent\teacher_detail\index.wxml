<!--教师详情页面-->
<view class="teacher-detail-container">
  <!-- 教师基本信息 -->
  <view class="teacher-info-card">
    <view class="teacher-header">
      <image class="teacher-avatar" src="{{teacherDetail.avatar || '/assets/images/default_avatar.png'}}" mode="aspectFill"></image>
      <view class="teacher-basic">
        <view class="teacher-name">{{teacherDetail.display_name || '未知老师'}}</view>
        <view class="teacher-meta">
          <text class="meta-item">{{teacherDetail.age || '未知'}}岁</text>
          <text class="meta-item">{{teacherDetail.gender_text || '未知'}}</text>
          <view class="certification-badge {{teacherDetail.certification_status == '4' ? 'verified' : 'normal'}}">
            {{teacherDetail.certification_text || '未认证'}}
          </view>
        </view>
        <view class="teacher-rating">
          <view class="stars">
            <block wx:for="{{5}}" wx:key="index">
              <text class="star {{index < (teacherDetail.rating || 0) ? 'filled' : ''}}">★</text>
            </block>
          </view>
          <text class="rating-text">{{teacherDetail.rating || '0.0'}}分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 教学信息 -->
  <view class="info-section">
    <view class="section-title">教学信息</view>
    <view class="info-item">
      <text class="info-label">教授科目：</text>
      <text class="info-value">{{teacherDetail.teaching_subjects || '暂未设置'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">所在学校：</text>
      <text class="info-value">{{teacherDetail.university || '未填写'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">专业：</text>
      <text class="info-value">{{teacherDetail.major || '未填写'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">年级：</text>
      <text class="info-value">{{teacherDetail.grade || '未填写'}}</text>
    </view>
  </view>

  <!-- 教育背景 -->
  <view class="info-section" wx:if="{{teacherDetail.education_background}}">
    <view class="section-title">教育背景</view>
    <view class="section-content">
      <text class="content-text">{{teacherDetail.education_background}}</text>
    </view>
  </view>

  <!-- 经验背景 -->
  <view class="info-section" wx:if="{{teacherDetail.experience_background}}">
    <view class="section-title">经验背景</view>
    <view class="section-content">
      <text class="content-text">{{teacherDetail.experience_background}}</text>
    </view>
  </view>

  <!-- 自我介绍 -->
  <view class="info-section" wx:if="{{teacherDetail.self_introduction}}">
    <view class="section-title">自我介绍</view>
    <view class="section-content">
      <text class="content-text">{{teacherDetail.self_introduction}}</text>
    </view>
  </view>

  <!-- 教学统计 -->
  <view class="stats-section">
    <view class="section-title">教学统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{teacherDetail.total_orders || 0}}</text>
        <text class="stat-label">累计接单</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{teacherDetail.success_orders || 0}}</text>
        <text class="stat-label">成功完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{teacherDetail.rating || '0.0'}}</text>
        <text class="stat-label">平均评分</text>
      </view>
    </view>
  </view>

  <!-- 预约按钮 -->
  <view class="action-section">
    <button class="book-btn" bindtap="bookTeacher">预约TA</button>
  </view>
</view>

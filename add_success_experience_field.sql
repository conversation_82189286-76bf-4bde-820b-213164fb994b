-- 在现有teacher_info表中添加接课成功经历字段

-- 1. 检查当前表结构
SELECT '=== 当前teacher_info表结构 ===' as info;
DESCRIBE teacher_info;

-- 2. 添加接课成功经历字段
ALTER TABLE teacher_info 
ADD COLUMN success_experience TEXT COMMENT '接课成功经历（JSON格式存储）' AFTER success_orders;

-- 3. 验证字段添加成功
SELECT '=== 验证新字段 ===' as info;
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME = 'success_experience';

-- 4. 查看当前教师的统计数据
SELECT '=== 当前教师统计数据 ===' as info;
SELECT 
    id,
    real_name,
    nick_name,
    total_orders,
    success_orders,
    success_experience,
    rating
FROM teacher_info 
WHERE status = '0'
ORDER BY id;

-- 5. 为现有教师初始化一些示例成功经历数据
UPDATE teacher_info 
SET success_experience = JSON_ARRAY(
    JSON_OBJECT(
        'orderId', 1001,
        'orderNo', 'ORDER_20240101001',
        'subjectName', '数学',
        'grade', '初三',
        'studentName', '张同学',
        'completedTime', '2024-01-15 18:00:00',
        'duration', '2个月',
        'improvement', '成绩从70分提升到85分',
        'feedback', '老师教学认真负责，孩子很喜欢',
        'rating', 5.0
    ),
    JSON_OBJECT(
        'orderId', 1002,
        'orderNo', 'ORDER_20240201002',
        'subjectName', '英语',
        'grade', '高一',
        'studentName', '李同学',
        'completedTime', '2024-02-20 19:30:00',
        'duration', '1个月',
        'improvement', '英语口语明显提升',
        'feedback', '老师很有耐心，教学方法很好',
        'rating', 4.8
    )
)
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 1) as temp)
  AND (success_experience IS NULL OR success_experience = '');

-- 6. 验证数据更新
SELECT '=== 验证示例数据 ===' as info;
SELECT 
    id,
    real_name,
    success_orders,
    JSON_PRETTY(success_experience) as formatted_experience
FROM teacher_info 
WHERE success_experience IS NOT NULL
LIMIT 1;

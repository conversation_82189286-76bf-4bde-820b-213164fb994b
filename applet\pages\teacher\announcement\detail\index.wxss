.announcement-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  margin-top:20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.announcement-content {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.announcement-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
  text-align: center;
}

.announcement-body {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

.rich-content {
  word-break: break-all;
}

/* 富文本内容样式 */
.rich-content p {
  margin: 20rpx 0;
}

.rich-content strong {
  font-weight: bold;
  color: #333;
}

.rich-content a {
  color: #007aff;
  text-decoration: underline;
}

.rich-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

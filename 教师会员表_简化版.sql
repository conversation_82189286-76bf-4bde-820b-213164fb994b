-- 教师会员表创建脚本（简化版）
-- 适用于MySQL 5.7及以上版本

-- 1. 创建教师会员表
DROP TABLE IF EXISTS `teacher_membership`;
CREATE TABLE `teacher_membership` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `teacher_id` bigint(20) NOT NULL COMMENT '教师ID',
  `membership_type` varchar(10) NOT NULL DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）',
  `membership_date` datetime NOT NULL COMMENT '成为会员日期',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `payment_status` varchar(10) NOT NULL DEFAULT '1' COMMENT '支付状态（0未支付 1已支付 2已退款）',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `status` varchar(10) DEFAULT '1' COMMENT '状态（0无效 1有效）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_membership_type` (`membership_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_membership_date` (`membership_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师会员表';

-- 2. 检查teacher_info表是否存在membership_type字段
-- 如果执行时报错"Duplicate column name"，说明字段已存在，可以忽略该错误

-- 添加会员类型字段
ALTER TABLE `teacher_info` 
ADD COLUMN `membership_type` varchar(10) DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）';

-- 添加会员日期字段  
ALTER TABLE `teacher_info` 
ADD COLUMN `membership_date` datetime DEFAULT NULL COMMENT '成为会员日期';

-- 3. 创建索引
-- 如果执行时报错"Duplicate key name"，说明索引已存在，可以忽略该错误

ALTER TABLE `teacher_info` 
ADD INDEX `idx_membership_type` (`membership_type`);

ALTER TABLE `teacher_info` 
ADD INDEX `idx_membership_date` (`membership_date`);

-- 4. 初始化数据
-- 将所有现有教师设置为普通会员
UPDATE `teacher_info`
SET `membership_type` = '1'
WHERE `membership_type` IS NULL OR `membership_type` = '';

-- 注意：由于teacher_membership表刚创建，暂时没有数据，所以跳过同步更新
-- 当有会员记录时，可以执行以下语句进行同步：
-- UPDATE teacher_info t
-- INNER JOIN teacher_membership m ON t.id = m.teacher_id
-- SET t.membership_type = m.membership_type, t.membership_date = m.membership_date
-- WHERE (m.status IS NULL OR m.status = '1') AND m.payment_status = '1';

-- 5. 验证表结构
SELECT 'teacher_membership表结构:' as info;
DESCRIBE `teacher_membership`;

SELECT 'teacher_info表中的会员相关字段:' as info;
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_info' 
  AND COLUMN_NAME IN ('membership_type', 'membership_date');

-- 6. 查看当前教师会员状态统计
SELECT 
    membership_type,
    CASE 
        WHEN membership_type = '1' THEN '普通会员'
        WHEN membership_type = '2' THEN '高级会员'
        ELSE '未知类型'
    END as membership_name,
    COUNT(*) as count
FROM `teacher_info` 
GROUP BY membership_type;

SELECT '教师会员表创建完成！' as result;

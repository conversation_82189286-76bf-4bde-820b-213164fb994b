<!-- 自定义导航栏 -->
<view class="custom-navbar">
  <!-- 位置选择器 -->
  <view class="location-selector" bindtap="showCitySelector">
    <text class="location-text">{{currentCity}}</text>
    <text class="location-arrow">▼</text>
  </view>

  <!-- 标题 -->
  <view class="navbar-title">众桥辅导</view>

  <!-- 占位 -->
  <view class="navbar-placeholder"></view>
</view>

<!-- 页面内容 -->
<scroll-view
  class="page-content tabbar-scroll-view"
  scroll-y="{{true}}"
  refresher-enabled="{{true}}"
  refresher-triggered="{{isLoading}}"
  bindrefresherrefresh="onPullDownRefresh"
  bindscrolltolower="onReachBottom"
>
  <!-- 轮播图 -->
  <view class="banner-section" wx:if="{{bannerList.length > 0}}">
    <swiper
      class="banner-swiper"
      indicator-dots="{{true}}"
      autoplay="{{true}}"
      interval="{{5000}}"
      duration="{{500}}"
      circular="{{true}}"
    >
      <swiper-item
        wx:for="{{bannerList}}"
        wx:key="id"
        bindtap="onBannerTap"
        data-index="{{index}}"
      >
        <image
          class="banner-image"
          src="{{item.image}}"
          mode="aspectFill"
        />
      </swiper-item>
    </swiper>
  </view>

  <!-- 通知公告区域 -->
  <view class="announcement-section" wx:if="{{announcementList.length > 0}}">
    <view class="section-header">
      <view class="section-title-with-icon">
        <text class="announcement-icon">📢</text>
        <text class="section-title-text">通知公告</text>
      </view>
    </view>

    <view class="announcement-list">
      <view
        class="announcement-item"
        wx:for="{{announcementList}}"
        wx:key="id"
        bindtap="onAnnouncementTap"
        data-index="{{index}}"
      >
        <view class="announcement-title">{{item.title}}</view>
        <view class="announcement-arrow">></view>
      </view>
    </view>
  </view>



  <!-- 订单列表标题 -->
  <view class="section-title1">
    <text class="title-text"><text class='iconG'>丨</text>家教订单</text>
    <text class="title-more" bindtap="goToOrderList">查看更多></text>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <order-card
      wx:for="{{displayOrderList}}"
      wx:key="id"
      order="{{item}}"
      card-type="default"
      bind:cardtap="onOrderTap"
      bind:action="onOrderAction"
    />

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{orderList.length === 0 && !isLoading}}">
      <image class="empty-image" src="/static/images/empty-order.png" />
      <text class="empty-text">暂无订单</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 查看更多按钮 -->
    <view class="view-more-container" wx:if="{{orderList.length > 10}}">
      <button class="view-more-btn" bindtap="goToOrderList">查看更多订单</button>
    </view>
  </view>
</scroll-view>

<!-- 位置选择器弹窗 -->
<location-selector
  wx:if="{{showCityPicker}}"
  current-location="{{currentCity}}"
  location-list="{{cityList}}"
  bind:locationchange="onCityChange"
  bind:close="onCityClose"
/>


// custom-tab-bar/index.js
Component({
  data: {
    selected: 0,
    userType: 'teacher',
    // 教师端TabBar配置
    teacherList: [
      {
        pagePath: "/pages/teacher/home/<USER>",
        text: "首页",
        iconPath: "/assets/images/tabbar/home.png",
        selectedIconPath: "/assets/images/tabbar/home_active.png"
      },
      {
        pagePath: "/pages/teacher/workspace/index",
        text: "工作台",
        iconPath: "/assets/images/tabbar/workspace.png",
        selectedIconPath: "/assets/images/tabbar/workspace_active.png"
      },
      {
        pagePath: "/pages/teacher/student_source/index",
        text: "生源",
        iconPath: "/assets/images/tabbar/student.png",
        selectedIconPath: "/assets/images/tabbar/student_active.png"
      },
      {
        pagePath: "/pages/teacher/profile/index",
        text: "我的",
        iconPath: "/assets/images/tabbar/profile.png",
        selectedIconPath: "/assets/images/tabbar/profile_active.png"
      }
    ],
    // 家长端TabBar配置
    parentList: [
      {
        pagePath: "/pages/parent/home/<USER>",
        text: "首页",
        iconPath: "/assets/images/tabbar/home.png",
        selectedIconPath: "/assets/images/tabbar/home_active.png"
      },
      {
        pagePath: "/pages/parent/publish/index",
        text: "发布家教",
        iconPath: "/assets/images/tabbar/workspace.png",
        selectedIconPath: "/assets/images/tabbar/workspace_active.png"
      },
      {
        pagePath: "/pages/parent/profile/index",
        text: "我的",
        iconPath: "/assets/images/tabbar/profile.png",
        selectedIconPath: "/assets/images/tabbar/profile_active.png"
      }
    ]
  },

  attached() {
    this.init();
  },

  methods: {
    init() {
      const userType = wx.getStorageSync('userType') || 'teacher';
      this.setData({ userType });
    },

    switchTab(e) {
      const { path, index } = e.currentTarget.dataset;
      this.setData({ selected: index });
      wx.switchTab({ url: path });
    }
  }
});

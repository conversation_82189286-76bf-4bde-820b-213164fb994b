-- 手动修复teacher_membership表缺失字段

-- 1. 查看当前表结构
DESCRIBE teacher_membership;

-- 2. 如果上面的结果中没有membership_date字段，请执行以下语句：
ALTER TABLE teacher_membership 
ADD COLUMN membership_date datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '成为会员日期' 
AFTER membership_type;

-- 3. 验证字段是否添加成功
DESCRIBE teacher_membership;

-- 4. 如果表中已有数据，更新membership_date字段
-- 使用create_time作为membership_date的值
UPDATE teacher_membership 
SET membership_date = COALESCE(create_time, NOW()) 
WHERE membership_date IS NULL OR membership_date = '0000-00-00 00:00:00';

-- 5. 验证数据更新
SELECT id, teacher_id, membership_type, membership_date, create_time 
FROM teacher_membership 
LIMIT 5;

-- 完成提示
SELECT '✅ teacher_membership表修复完成！现在可以正常升级会员了。' as result;

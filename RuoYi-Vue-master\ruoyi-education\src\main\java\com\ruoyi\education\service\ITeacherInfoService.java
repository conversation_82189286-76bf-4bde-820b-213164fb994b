package com.ruoyi.education.service;

import java.util.List;
import com.ruoyi.education.domain.TeacherInfo;
import com.ruoyi.education.domain.Subject;

/**
 * 教师信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITeacherInfoService 
{
    /**
     * 查询教师信息
     * 
     * @param id 教师信息主键
     * @return 教师信息
     */
    public TeacherInfo selectTeacherInfoById(Long id);

    /**
     * 根据OpenID查询教师信息
     * 
     * @param openid 微信openid
     * @return 教师信息
     */
    public TeacherInfo selectTeacherInfoByOpenid(String openid);

    /**
     * 查询教师信息列表
     * 
     * @param teacherInfo 教师信息
     * @return 教师信息集合
     */
    public List<TeacherInfo> selectTeacherInfoList(TeacherInfo teacherInfo);

    /**
     * 新增教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    public int insertTeacherInfo(TeacherInfo teacherInfo);

    /**
     * 修改教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    public int updateTeacherInfo(TeacherInfo teacherInfo);

    /**
     * 根据教师ID查询所教的科目列表
     * @param teacherId 教师ID
     * @return 科目列表
     */
    public List<Subject> selectSubjectsByTeacherId(Long teacherId);
    
    /**
     * 更新教师所教的科目
     *
     * @param teacherId 教师ID
     * @param subjectIds 科目ID列表
     * @return 结果
     */
    public int updateTeacherSubjects(Long teacherId, List<Long> subjectIds);

    /**
     * 更新教师接课成功统计和经历
     *
     * @param teacherId 教师ID
     * @param order 完成的订单信息
     * @return 结果
     */
    public int updateTeacherSuccessStats(Long teacherId, com.ruoyi.education.domain.TutorOrder order);

    /**
     * 批量删除教师信息
     *
     * @param ids 需要删除的教师信息主键集合
     * @return 结果
     */
    public int deleteTeacherInfoByIds(Long[] ids);

    /**
     * 删除教师信息信息
     * 
     * @param id 教师信息主键
     * @return 结果
     */
    public int deleteTeacherInfoById(Long id);

    /**
     * 根据手机号查询教师信息
     * 
     * @param phoneNumber 手机号
     * @return 教师信息
     */
    public TeacherInfo selectTeacherInfoByPhoneNumber(String phoneNumber);

    /**
     * 批量拉黑教师
     * 
     * @param ids 教师ID集合
     * @param blacklistReason 拉黑原因
     * @return 结果
     */
    public int blacklistTeacherByIds(Long[] ids, String blacklistReason);

    /**
     * 批量解除拉黑
     * 
     * @param ids 教师ID集合
     * @return 结果
     */
    public int unblacklistTeacherByIds(Long[] ids);
} 
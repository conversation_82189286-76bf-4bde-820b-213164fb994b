<!--pages/parent/profile/index.wxml-->
<view class="page-container tabbar-page">
  <!-- 自定义导航栏 -->
  <navigation-bar title="我的" />

  <!-- 个人信息区域 -->
  <view class="profile-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatar || '/assets/images/default_avatar.png'}}" mode="aspectFill" />
      <view class="user-details">
        <view class="name">{{userInfo.nickName || userInfo.realName || '家长用户'}}</view>
        <view class="phone">{{userInfo.phoneNumber || '未绑定手机号'}}</view>
      </view>
      <view class="edit-btn" bindtap="editProfile">
        <text class="iconfont icon-edit"></text>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-section">
    <view class="stats-item" bindtap="viewAllOrders">
      <view class="stats-number">{{stats.publishedOrders || 0}}</view>
      <view class="stats-label">已发布</view>
    </view>
    <view class="stats-item" bindtap="viewOngoingOrders">
      <view class="stats-number">{{stats.ongoingOrders || 0}}</view>
      <view class="stats-label">进行中</view>
    </view>
    <view class="stats-item" bindtap="viewCompletedOrders">
      <view class="stats-number">{{stats.completedOrders || 0}}</view>
      <view class="stats-label">已完成</view>
    </view>
  </view>

  <!-- 我的订单 -->
  <view class="section">
    <view class="section-title">我的订单</view>
    <view class="menu-list">
      <view class="menu-item" bindtap="viewAllOrders">
        <view class="menu-icon">
          <text class="iconfont icon-order"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">全部订单</view>
          <view class="menu-desc">查看所有发布的家教订单</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>

      <view class="menu-item" bindtap="viewOngoingOrders">
        <view class="menu-icon">
          <text class="iconfont icon-ongoing"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">正在进行</view>
          <view class="menu-desc">查看正在进行的订单</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>

      <view class="menu-item" bindtap="viewCompletedOrders">
        <view class="menu-icon">
          <text class="iconfont icon-completed"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">已完成</view>
          <view class="menu-desc">查看已完成的订单</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 基本信息修改 -->
  <view class="section">
    <view class="section-title">个人设置</view>
    <view class="menu-list">
      <view class="menu-item" bindtap="editProfile">
        <view class="menu-icon">
          <text class="iconfont icon-edit"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">编辑资料</view>
          <view class="menu-desc">修改姓名和头像</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
-- 修复tutor_order表缺失字段

-- 1. 添加area_name字段（如果不存在）
-- 先检查字段是否存在，避免重复添加错误
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'tutor_order' 
                   AND COLUMN_NAME = 'area_name');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE tutor_order ADD COLUMN area_name varchar(100) DEFAULT NULL COMMENT ''地区名称'' AFTER city',
              'SELECT ''area_name字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 添加view_count字段（如果不存在）
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'tutor_order' 
                   AND COLUMN_NAME = 'view_count');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE tutor_order ADD COLUMN view_count int(11) DEFAULT 0 COMMENT ''浏览次数''',
              'SELECT ''view_count字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 验证字段添加结果
SELECT '=== 验证字段添加结果 ===' as info;

-- 检查area_name字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ area_name字段存在'
        ELSE '❌ area_name字段仍然不存在'
    END as area_name_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'area_name';

-- 检查view_count字段
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ view_count字段存在'
        ELSE '❌ view_count字段仍然不存在'
    END as view_count_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tutor_order' 
  AND COLUMN_NAME = 'view_count';

-- 4. 显示更新后的表结构
SELECT '=== 更新后的tutor_order表结构 ===' as info;
DESCRIBE tutor_order;

-- 5. 测试SQL查询（不执行，仅显示）
SELECT '=== 测试SQL语句 ===' as info;
SELECT 'SQL测试：如果上面显示字段都存在，则以下查询应该能正常执行' as test_info;

-- 这是一个测试查询，验证所有字段都存在
-- SELECT o.id, o.order_no, o.city, o.area_name, o.view_count 
-- FROM tutor_order o 
-- WHERE o.status = '2' 
-- LIMIT 1;

SELECT '🎉 字段修复完成！请重新编译项目并重启应用。' as result;

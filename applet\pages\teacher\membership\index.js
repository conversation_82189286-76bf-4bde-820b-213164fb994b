import { getMembershipInfo, getVipFeeConfig, getMembershipBenefits } from '../../../api/membership.js';
const { request } = require('../../../utils/request.js');

Page({
  data: {
    // 会员信息
    membershipInfo: {
      isVip: false,
      membershipDate: null,
      membershipType: '1' // 1普通会员 2高级会员
    },
    
    // VIP费用配置
    vipFee: '0',
    
    // 会员权益（简化版）
    benefits: [
      {
        title: '报名机构订单',
        desc: '可以报名机构发布的高质量订单',
        isVip: true
      },
      {
        title: '报名普通订单',
        desc: '可以报名所有普通用户发布的订单',
        isVip: false
      }
    ],
    
    // 加载状态
    loading: true,
    upgrading: false
  },

  onLoad() {
    this.loadMembershipInfo();
    this.loadVipFeeConfig();
  },

  // 加载会员信息
  async loadMembershipInfo() {
    try {
      const res = await getMembershipInfo();
      if (res.code === 200) {
        const membershipInfo = res.data || {};

        this.setData({
          membershipInfo: membershipInfo
        });
      }
    } catch (error) {
      console.error('加载会员信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载VIP费用配置
  async loadVipFeeConfig() {
    try {
      const res = await getVipFeeConfig();
      if (res.code === 200) {
        this.setData({
          vipFee: res.msg || '0'
        });
      }
    } catch (error) {
      console.error('加载VIP费用配置失败:', error);
    }
  },

  // 升级为高级会员
  async upgradeToVip() {
    const { vipFee, membershipInfo } = this.data;

    if (membershipInfo.isVip) {
      wx.showToast({
        title: '您已是高级会员',
        icon: 'none'
      });
      return;
    }

    // 确认升级
    const confirmRes = await new Promise((resolve) => {
      wx.showModal({
        title: '升级确认',
        content: `升级为高级会员需要支付 ¥${vipFee} 元，会员权限永久有效，确认升级吗？`,
        success: resolve
      });
    });

    if (!confirmRes.confirm) return;

    this.setData({ upgrading: true });

    try {
      // 1. 创建支付订单
      const paymentOrder = await this.createPaymentOrder();

      // 2. 调用微信支付
      await this.callWechatPay(paymentOrder);

      // 3. 支付成功，处理升级
      await this.handlePaymentSuccess();

    } catch (error) {
      console.error('升级失败:', error);

      if (error.message === 'cancel') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: error.message || '升级失败，请重试',
          icon: 'none'
        });
      }
    } finally {
      this.setData({ upgrading: false });
    }
  },

  // 创建支付订单
  async createPaymentOrder() {
    // 调用后端接口创建支付订单
    const res = await request({
      url: '/applet/teacher/membership/createPayOrder',
      method: 'POST',
      data: {
        membershipType: '2',
        paymentType: 'wechat'
      }
    });

    if (res.code !== 200) {
      throw new Error(res.msg || '创建支付订单失败');
    }

    return res.data;
  },

  // 调用微信支付
  async callWechatPay(paymentOrder) {
    console.log('准备调用微信支付，参数:', paymentOrder);

    return new Promise((resolve, reject) => {
      // 检查必需参数 - 使用正确的字段名
      if (!paymentOrder.timeStamp || !paymentOrder.nonceStr || !paymentOrder.packageValue || !paymentOrder.paySign) {
        console.error('支付参数检查失败:', {
          timeStamp: paymentOrder.timeStamp,
          nonceStr: paymentOrder.nonceStr,
          packageValue: paymentOrder.packageValue,
          paySign: paymentOrder.paySign
        });
        reject(new Error('支付参数不完整'));
        return;
      }

      // 调用微信支付
      wx.requestPayment({
        timeStamp: paymentOrder.timeStamp,
        nonceStr: paymentOrder.nonceStr,
        package: paymentOrder.packageValue, // 使用packageValue字段
        signType: paymentOrder.signType || 'MD5',
        paySign: paymentOrder.paySign,
        success: (res) => {
          console.log('微信支付成功:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('微信支付失败:', err);

          // 处理不同的错误情况
          if (err.errMsg) {
            if (err.errMsg.includes('cancel')) {
              reject(new Error('cancel'));
            } else if (err.errMsg.includes('total_fee')) {
              reject(new Error('支付金额参数错误'));
            } else if (err.errMsg.includes('param')) {
              reject(new Error('支付参数错误：' + err.errMsg));
            } else {
              reject(new Error('支付失败：' + err.errMsg));
            }
          } else {
            reject(new Error('支付失败'));
          }
        }
      });
    });
  },

  // 处理支付成功
  async handlePaymentSuccess() {
    // 支付成功，显示提示并刷新会员信息
    wx.showToast({
      title: '支付成功！',
      icon: 'success'
    });

    // 延迟刷新会员信息，等待支付回调处理完成
    setTimeout(() => {
      this.loadMembershipInfo();
    }, 2000);
  },

  // 查看会员权益详情
  viewBenefitDetail(e) {
    const { index } = e.currentTarget.dataset;
    const benefit = this.data.benefits[index];
    
    wx.showModal({
      title: benefit.title,
      content: benefit.desc,
      showCancel: false
    });
  },





  // 分享页面
  onShareAppMessage() {
    return {
      title: '成为高级会员，解锁更多权益',
      path: '/pages/teacher/membership/index'
    };
  }
});

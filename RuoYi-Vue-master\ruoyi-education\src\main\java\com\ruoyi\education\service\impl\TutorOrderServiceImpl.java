
package com.ruoyi.education.service.impl;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.education.mapper.TutorOrderMapper;
import com.ruoyi.education.domain.TutorOrder;
import com.ruoyi.education.service.ITutorOrderService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;

/**
 * 家教订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
@Service
public class TutorOrderServiceImpl implements ITutorOrderService 
{
    @Autowired
    private TutorOrderMapper tutorOrderMapper;

    /**
     * 查询家教订单
     * 
     * @param id 家教订单主键
     * @return 家教订单
     */
    @Override
    public TutorOrder selectTutorOrderById(Long id)
    {
        return tutorOrderMapper.selectTutorOrderById(id);
    }

    /**
     * 查询家教订单列表
     * 
     * @param tutorOrder 家教订单
     * @return 家教订单
     */
    @Override
    public List<TutorOrder> selectTutorOrderList(TutorOrder tutorOrder)
    {
        return tutorOrderMapper.selectTutorOrderList(tutorOrder);
    }

    /**
     * 新增家教订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    @Override
    public int insertTutorOrder(TutorOrder tutorOrder)
    {
        // 自动生成订单编号
        if (StringUtils.isEmpty(tutorOrder.getOrderNo()))
        {
            tutorOrder.setOrderNo(generateOrderNo());
        }
        
        // 设置默认值
        if (StringUtils.isEmpty(tutorOrder.getStatus()))
        {
            // 根据发布者类型设置不同的默认状态
            if ("2".equals(tutorOrder.getPublisherType())) {
                // 管理员发布的订单，直接审核通过
                tutorOrder.setStatus("2");
                tutorOrder.setAuditTime(new Date());
                tutorOrder.setAuditBy(SecurityUtils.getUsername());
            } else {
                // 教师端发布的订单，需要审核
                tutorOrder.setStatus("1");
            }
        }
        if (StringUtils.isEmpty(tutorOrder.getPublisherType()))
        {
            tutorOrder.setPublisherType("1"); // 默认教师端发布
        }
        if (tutorOrder.getViewCount() == null)
        {
            tutorOrder.setViewCount(0);
        }
        if (tutorOrder.getApplyCount() == null)
        {
            tutorOrder.setApplyCount(0);
        }
        if (StringUtils.isEmpty(tutorOrder.getIsTop()))
        {
            tutorOrder.setIsTop("0"); // 默认不置顶
        }
        
        return tutorOrderMapper.insertTutorOrder(tutorOrder);
    }

    /**
     * 修改家教订单
     * 
     * @param tutorOrder 家教订单
     * @return 结果
     */
    @Override
    public int updateTutorOrder(TutorOrder tutorOrder)
    {
        return tutorOrderMapper.updateTutorOrder(tutorOrder);
    }

    /**
     * 批量删除家教订单
     * 
     * @param ids 需要删除的家教订单主键
     * @return 结果
     */
    @Override
    public int deleteTutorOrderByIds(Long[] ids)
    {
        return tutorOrderMapper.deleteTutorOrderByIds(ids);
    }

    /**
     * 删除家教订单信息
     * 
     * @param id 家教订单主键
     * @return 结果
     */
    @Override
    public int deleteTutorOrderById(Long id)
    {
        return tutorOrderMapper.deleteTutorOrderById(id);
    }

    /**
     * 管理员代发布订单
     *
     * @param tutorOrder 家教订单
     * @return 结果
     */
    @Override
    public int publishOrderByAdmin(TutorOrder tutorOrder)
    {
        System.out.println("=== 管理员代发布订单开始 ===");
        // 设置发布者类型为管理员
        tutorOrder.setPublisherType("2");
        tutorOrder.setPublisherId(SecurityUtils.getUserId());

        // 管理员发布的订单无需审核，直接设为审核通过状态
        tutorOrder.setStatus("2"); // 审核通过，可直接报名
        tutorOrder.setAuditTime(new Date());
        tutorOrder.setAuditBy(SecurityUtils.getUsername());

        System.out.println("设置订单状态: status=" + tutorOrder.getStatus() +
            ", publisherType=" + tutorOrder.getPublisherType() +
            ", selectedTeacherId=" + tutorOrder.getSelectedTeacherId());

        int result = insertTutorOrder(tutorOrder);
        System.out.println("=== 管理员代发布订单完成，结果: " + result + " ===");
        return result;
    }

    /**
     * 选择教师
     * 
     * @param orderId 订单ID
     * @param teacherId 教师ID
     * @return 结果
     */
    @Override
    public int selectTeacher(Long orderId, Long teacherId)
    {
        TutorOrder tutorOrder = new TutorOrder();
        tutorOrder.setId(orderId);
        tutorOrder.setSelectedTeacherId(teacherId);
        tutorOrder.setSelectTime(new Date());
        tutorOrder.setContactPublishedTime(new Date()); // 同时公布联系方式
        tutorOrder.setUpdateTime(new Date());
        tutorOrder.setUpdateBy(SecurityUtils.getUsername());
        
        return tutorOrderMapper.selectTeacher(tutorOrder);
    }

    /**
     * 设置置顶
     * 
     * @param id 订单ID
     * @param isTop 是否置顶 (0-否 1-是)
     * @return 结果
     */
    @Override
    public int setTop(Long id, String isTop)
    {
        TutorOrder tutorOrder = new TutorOrder();
        tutorOrder.setId(id);
        tutorOrder.setIsTop(isTop);
        if ("1".equals(isTop))
        {
            tutorOrder.setTopTime(new Date());
        }
        tutorOrder.setUpdateTime(new Date());
        tutorOrder.setUpdateBy(SecurityUtils.getUsername());
        
        return tutorOrderMapper.setTop(tutorOrder);
    }

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 家教订单
     */
    @Override
    public TutorOrder selectTutorOrderByOrderNo(String orderNo)
    {
        return tutorOrderMapper.selectTutorOrderByOrderNo(orderNo);
    }

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    @Override
    public String generateOrderNo()
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        // 生成4位随机数
        int random = (int) (Math.random() * 9000) + 1000;
        return "TO" + timestamp + random;
    }

    /**
     * 统计订单数量（按状态）
     * 
     * @param status 订单状态
     * @return 订单数量
     */
    @Override
    public int countOrdersByStatus(String status)
    {
        return tutorOrderMapper.countOrdersByStatus(status);
    }

    /**
     * 查询待审核订单列表
     * 
     * @return 待审核订单集合
     */
    @Override
    public List<TutorOrder> selectPendingAuditOrders()
    {
        return tutorOrderMapper.selectPendingAuditOrders();
    }

    /**
     * 查询置顶订单列表
     *
     * @return 置顶订单集合
     */
    @Override
    public List<TutorOrder> selectTopOrders()
    {
        return tutorOrderMapper.selectTopOrders();
    }



    /**
     * 获取订单统计信息
     * 
     * @return 统计信息
     */
    @Override
    public String getOrderStatistics()
    {
        StringBuilder sb = new StringBuilder();
        sb.append("待审核: ").append(countOrdersByStatus("1")).append("单");
        sb.append(", 审核通过: ").append(countOrdersByStatus("2")).append("单");
        sb.append(", 报名中: ").append(countOrdersByStatus("4")).append("单");
        sb.append(", 进行中: ").append(countOrdersByStatus("7")).append("单");
        sb.append(", 已完成: ").append(countOrdersByStatus("8")).append("单");
        return sb.toString();
    }

    /**
     * 查询小程序端-教师已完成的订单列表
     * @param teacherId 教师ID
     * @return 已完成订单集合
     */
    @Override
    public List<TutorOrder> selectCompletedTutorOrderListByTeacherId(Long teacherId) {
        return tutorOrderMapper.selectCompletedTutorOrderListByTeacherId(teacherId);
    }

    /**
     * 查询小程序端-新家教订单列表
     * @return 新订单集合
     */
    @Override
    public List<TutorOrder> selectNewTutorOrderList() {
        System.out.println("=== TutorOrderServiceImpl.selectNewTutorOrderList() 开始执行 ===");
        List<TutorOrder> result = tutorOrderMapper.selectNewTutorOrderList();
        System.out.println("=== Mapper 返回结果数量: " + (result != null ? result.size() : 0) + " ===");
        return result;
    }

    /**
     * 查询小程序端-教师已报名的订单列表
     * @param teacherId 教师ID
     * @return 已报名订单集合
     */
    @Override
    public List<TutorOrder> selectAppliedTutorOrderListByTeacherId(Long teacherId) {
        return tutorOrderMapper.selectAppliedTutorOrderListByTeacherId(teacherId);
    }

    /**
     * 查询小程序端-教师进行中的订单列表
     * @param teacherId 教师ID
     * @return 进行中订单集合
     */
    @Override
    public List<TutorOrder> selectOngoingTutorOrderListByTeacherId(Long teacherId) {
        return tutorOrderMapper.selectOngoingTutorOrderListByTeacherId(teacherId);
    }

    /**
     * 将订单状态流转为进行中
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int updateOrderStatusToOngoing(Long orderId) {
        return tutorOrderMapper.updateOrderStatusToOngoing(orderId);
    }

    /**
     * 查询教师未选中的订单列表（已报名但未被选中）
     * @param teacherId 教师ID
     * @return 未选中订单集合
     */
    @Override
    public List<TutorOrder> selectUnselectedTutorOrderListByTeacherId(Long teacherId) {
        return tutorOrderMapper.selectUnselectedTutorOrderListByTeacherId(teacherId);
    }

    /**
     * 将订单状态流转为已完成
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int updateOrderStatusToCompleted(Long orderId) {
        return tutorOrderMapper.updateOrderStatusToCompleted(orderId);
    }

    /**
     * 审核家教订单
     * @param id 订单ID
     * @param status 审核状态
     * @param auditReason 审核原因
     * @return 结果
     */
    @Override
    public int auditTutorOrder(Long id, String status, String auditReason) {
        TutorOrder tutorOrder = new TutorOrder();
        tutorOrder.setId(id);
        tutorOrder.setStatus(status);
        tutorOrder.setAuditReason(auditReason);
        tutorOrder.setUpdateTime(new Date());
        tutorOrder.setUpdateBy(SecurityUtils.getUsername());
        return tutorOrderMapper.auditTutorOrder(tutorOrder);
    }

    /**
     * 设置订单置顶（别名方法）
     * @param id 订单ID
     * @param isTop 是否置顶
     * @return 结果
     */
    @Override
    public int setTutorOrderTop(Long id, String isTop) {
        return setTop(id, isTop);
    }

    /**
     * 统计家长发布的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    @Override
    public int countOrdersByParentId(Long parentId) {
        return tutorOrderMapper.countOrdersByParentId(parentId);
    }

    /**
     * 统计家长进行中的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    @Override
    public int countOngoingOrdersByParentId(Long parentId) {
        return tutorOrderMapper.countOngoingOrdersByParentId(parentId);
    }

    /**
     * 统计家长已完成的订单数量
     * @param parentId 家长ID
     * @return 订单数量
     */
    @Override
    public int countCompletedOrdersByParentId(Long parentId) {
        return tutorOrderMapper.countCompletedOrdersByParentId(parentId);
    }

    /**
     * 查询家长最近的订单
     * @param parentId 家长ID
     * @param limit 限制数量
     * @return 订单列表
     */
    @Override
    public List<TutorOrder> selectRecentOrdersByParentId(Long parentId, int limit) {
        return tutorOrderMapper.selectRecentOrdersByParentId(parentId, limit);
    }

    /**
     * 获取推荐教师
     * @param limit 限制数量
     * @return 推荐教师列表
     */
    @Override
    public List<java.util.Map<String, Object>> getRecommendTeachers(int limit) {
        return tutorOrderMapper.getRecommendTeachers(limit);
    }

    /**
     * 查询家长发布的所有订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    @Override
    public List<TutorOrder> selectOrdersByParentId(Long parentId) {
        return tutorOrderMapper.selectOrdersByParentId(parentId);
    }

    /**
     * 查询家长进行中的订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    @Override
    public List<TutorOrder> selectOngoingOrdersByParentId(Long parentId) {
        return tutorOrderMapper.selectOngoingOrdersByParentId(parentId);
    }

    /**
     * 查询家长已完成的订单列表
     * @param parentId 家长ID
     * @return 订单列表
     */
    @Override
    public List<TutorOrder> selectCompletedOrdersByParentId(Long parentId) {
        return tutorOrderMapper.selectCompletedOrdersByParentId(parentId);
    }
}
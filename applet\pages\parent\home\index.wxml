<!--pages/parent/home/<USER>
<page-meta
  background-color="#f5f7fa"
  background-text-style="dark"
  page-style="background-color: #f5f7fa;"
/>

<view class="home-container">
  <!-- 顶部用户信息 -->
  <view class="header-section">
    <view class="user-info">
      <image
        class="avatar"
        src="{{userInfo.avatar || '/assets/images/profile.png'}}"
        mode="aspectFill"
      />
      <view class="user-details">
        <view class="greeting">您好，{{userInfo.nickName || '家长'}}！</view>
        <view class="subtitle">为孩子找到最适合的老师</view>
      </view>
    </view>

    <view class="quick-publish">
      <button class="publish-btn" bindtap="goToPublish">
        <text class="btn-icon">📝</text>
        <text class="btn-text">发布需求</text>
      </button>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stat-item" bindtap="goToOrderList">
        <view class="stat-number">{{stats.publishedOrders}}</view>
        <view class="stat-label">已发布</view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" bindtap="goToOrderList">
        <view class="stat-number">{{stats.ongoingOrders}}</view>
        <view class="stat-label">进行中</view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" bindtap="goToOrderList">
        <view class="stat-number">{{stats.completedOrders}}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>
  </view>

  <!-- 最近订单 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">
        <text class="title-icon">📋</text>
        <text class="title-text">最近订单</text>
      </view>
      <view class="section-more" bindtap="goToOrderList">
        <text>查看全部</text>
        <text class="arrow">→</text>
      </view>
    </view>

    <view class="order-list" wx:if="{{recentOrders.length > 0}}">
      <view
        class="order-item"
        wx:for="{{recentOrders}}"
        wx:key="id"
        bindtap="viewOrderDetail"
        data-id="{{item.id}}"
      >
        <view class="order-header">
          <view class="order-title">{{item.title}}</view>
          <view class="order-status status-{{item.status}}">{{item.status}}</view>
        </view>
        <view class="order-info">
          <text class="info-item">{{item.subject}}</text>
          <text class="info-divider">·</text>
          <text class="info-item">{{item.grade}}</text>
        </view>
        <view class="order-time">{{item.createTime}}</view>
      </view>
    </view>

    <view class="empty-state" wx:else>
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无订单，快去发布需求吧</text>
      <button class="empty-btn" bindtap="goToPublish">立即发布</button>
    </view>
  </view>

  <!-- 推荐教师 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">
        <text class="title-icon">⭐</text>
        <text class="title-text">优秀教师</text>
      </view>
    </view>

    <view class="teacher-list" wx:if="{{recommendTeachers.length > 0}}">
      <view
        class="teacher-item"
        wx:for="{{recommendTeachers}}"
        wx:key="id"
        bindtap="viewTeacherDetail"
        data-id="{{item.id}}"
      >
        <image
          class="teacher-avatar"
          src="{{item.avatar || '/assets/images/profile.png'}}"
          mode="aspectFill"
        />
        <view class="teacher-info">
          <view class="teacher-name">{{item.name}}</view>
          <view class="teacher-subject">{{item.subject}}老师</view>
          <view class="teacher-experience">{{item.experience}}</view>
          <view class="teacher-rating">
            <text class="rating-icon">⭐</text>
            <text class="rating-score">{{item.rating}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="empty-state" wx:else>
      <text class="empty-icon">👨‍🏫</text>
      <text class="empty-text">暂无推荐教师</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
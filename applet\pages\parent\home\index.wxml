<!--pages/parent/home/<USER>
<page-meta
  background-color="#f5f7fa"
  background-text-style="dark"
  page-style="background-color: #f5f7fa;"
/>

<view class="page-container tabbar-page">
  <!-- 自定义导航栏 -->
  <navigation-bar title="众桥辅导" />
  
  <!-- 页面内容 -->
  <scroll-view class="home-container" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
    
    <!-- 轮播图 -->
    <view class="banner-section" wx:if="{{bannerList.length > 0}}">
      <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="5000" duration="500" circular="true">
        <swiper-item wx:for="{{bannerList}}" wx:key="id">
          <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-item="{{item}}" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 通知公告 -->
    <view class="announcement-section" wx:if="{{announcements.length > 0}}">
      <view class="section-header">
        <view class="section-title-with-icon">
          <text class="announcement-icon">📢</text>
          <text class="section-title-text">通知公告</text>
        </view>
      </view>

      <view class="announcement-list">
        <view
          class="announcement-item"
          wx:for="{{announcements}}"
          wx:key="id"
          bindtap="onAnnouncementTap"
          data-index="{{index}}"
        >
          <view class="announcement-title">{{item.title}}</view>
          <view class="announcement-arrow">></view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-item" bindtap="goToPublish">
        <view class="action-icon">📝</view>
        <text class="action-text">发布家教</text>
      </view>
      <view class="action-item" bindtap="viewAllOrders">
        <view class="action-icon">📋</view>
        <text class="action-text">我的订单</text>
      </view>
      <view class="action-item" bindtap="viewTeachers">
        <view class="action-icon">👨‍🏫</view>
        <text class="action-text">找教师</text>
      </view>
      <view class="action-item" bindtap="contactService">
        <view class="action-icon">💬</view>
        <text class="action-text">联系客服</text>
      </view>
    </view>

    <!-- 我的订单 -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">📋</text>
          <text>我的订单</text>
        </view>
        <view class="section-more" bindtap="viewAllOrders">
          <text>查看全部</text>
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 正在进行的订单 -->
      <view class="order-list" wx:if="{{ongoingOrders.length > 0}}">
        <view class="order-item" wx:for="{{ongoingOrders}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
          <view class="order-header">
            <view class="order-title">{{item.subjectName}} - {{item.grade}}</view>
            <view class="order-status status-{{item.status}}">{{item.statusText}}</view>
          </view>
          <view class="order-info">
            <text>{{item.tutoringMode}}</text>
            <text class="info-divider">|</text>
            <text>{{item.salary}}{{item.salaryUnitText}}</text>
            <text class="info-divider">|</text>
            <text>{{item.city}}</text>
          </view>
          <view class="order-footer">
            <view class="order-time">{{item.createTime}}</view>
            <view class="order-actions">
              <button class="action-btn" wx:if="{{item.canPay}}" bindtap="payOrder" data-id="{{item.id}}" catchtap="true">支付</button>
              <button class="action-btn" wx:if="{{item.canEvaluate}}" bindtap="evaluateOrder" data-id="{{item.id}}" catchtap="true">评价</button>
              <button class="action-btn secondary" bindtap="viewOrderDetail" data-id="{{item.id}}" catchtap="true">查看详情</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 无正在进行的订单时显示推荐教师 -->
      <view wx:if="{{ongoingOrders.length === 0}}">
        <!-- 推荐教师列表 -->
        <view class="recommend-teachers" wx:if="{{recommendTeachers.length > 0}}">
          <view class="teacher-card" wx:for="{{recommendTeachers}}" wx:key="id" bindtap="viewTeacherDetail" data-teacher="{{item}}">
            <!-- 教师头像和基本信息 -->
            <view class="teacher-header">
              <image class="teacher-avatar" src="{{item.avatar || '/assets/images/default_avatar.png'}}" mode="aspectFill" />
              <view class="teacher-basic">
                <view class="teacher-name">{{item.name}}</view>
                <view class="teacher-school">{{item.university}} - {{item.major}}</view>
              </view>
            </view>

            <!-- 辅导年级 -->
            <view class="teacher-grades">
              <text class="grade-label">辅导年级：</text>
              <text class="grade-text">{{item.grades || '幼儿园,学前班,一年级,二年级,三年级,四年级,五年级,六年级,初一,初二,初三'}}</text>
            </view>

            <!-- 擅长科目 -->
            <view class="teacher-subjects">
              <text class="subject-label">擅长科目：</text>
              <text class="subject-text">{{item.subject || '数学,英语,语文,物理,化学'}}</text>
            </view>

            <!-- 教学特点 -->
            <view class="teacher-features">
              <text class="feature-label">教学特点：</text>
              <text class="feature-text">{{item.features || '本人性格开朗、乐观向上、爱与人沟通，具有较强的组织协调能力和团队合作精神。在教学工作中，本人认真负责，...'}}</text>
            </view>

            <!-- 查看详情按钮 -->
            <view class="detail-btn">
              <text>查看详情</text>
            </view>
          </view>
        </view>

        <!-- 完全无订单时的空状态 -->
        <view class="empty-state" wx:if="{{recommendTeachers.length === 0}}">
          <text class="empty-icon">📚</text>
          <text class="empty-title">暂无订单</text>
          <text class="empty-desc">快来发布家教需求，找到合适的教师吧</text>
          <button class="empty-btn" bindtap="goToPublish">发布家教需求</button>
        </view>
      </view>
    </view>

  </scroll-view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>

  <!-- 底部导航栏 -->
  <view class="bottom-nav">
    <view class="nav-item active" bindtap="goToHome">
      <text class="nav-icon">🏠</text>
      <text class="nav-text">首页</text>
    </view>
    <view class="nav-item" bindtap="goToPublish">
      <text class="nav-icon">➕</text>
      <text class="nav-text">发布家教</text>
    </view>
    <view class="nav-item" bindtap="goToProfile">
      <text class="nav-icon">👤</text>
      <text class="nav-text">我的</text>
    </view>
  </view>
</view>

<!--pages/parent/home/<USER>
<page-meta
  background-color="#f5f7fa"
  background-text-style="dark"
  page-style="background-color: #f5f7fa;"
/>

<view class="page-container tabbar-page">
  <!-- 自定义导航栏 -->
  <navigation-bar title="众桥辅导" />
  
  <!-- 页面内容 -->
  <scroll-view class="home-container" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
    
    <!-- 轮播图 -->
    <view class="banner-section" wx:if="{{bannerList.length > 0}}">
      <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="5000" duration="500" circular="true">
        <swiper-item wx:for="{{bannerList}}" wx:key="id">
          <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-item="{{item}}" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 通知公告 -->
    <view class="notice-section" wx:if="{{announcements.length > 0}}">
      <view class="notice-header">
        <text class="notice-icon">📢</text>
        <text class="notice-title">平台通知</text>
      </view>
      <swiper class="notice-swiper" vertical="true" autoplay="true" interval="3000" duration="500" circular="true">
        <swiper-item wx:for="{{announcements}}" wx:key="id">
          <view class="notice-item" bindtap="viewNoticeDetail" data-item="{{item}}">
            <text class="notice-text">{{item.title}}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-item" bindtap="goToPublish">
        <view class="action-icon">📝</view>
        <text class="action-text">发布家教</text>
      </view>
      <view class="action-item" bindtap="viewAllOrders">
        <view class="action-icon">📋</view>
        <text class="action-text">我的订单</text>
      </view>
      <view class="action-item" bindtap="viewTeachers">
        <view class="action-icon">👨‍🏫</view>
        <text class="action-text">找教师</text>
      </view>
      <view class="action-item" bindtap="contactService">
        <view class="action-icon">💬</view>
        <text class="action-text">联系客服</text>
      </view>
    </view>

    <!-- 我的订单 -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">📋</text>
          <text>我的订单</text>
        </view>
        <view class="section-more" bindtap="viewAllOrders">
          <text>查看全部</text>
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 正在进行的订单 -->
      <view class="order-list" wx:if="{{ongoingOrders.length > 0}}">
        <view class="order-item" wx:for="{{ongoingOrders}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
          <view class="order-header">
            <view class="order-title">{{item.subjectName}} - {{item.grade}}</view>
            <view class="order-status status-{{item.status}}">{{item.statusText}}</view>
          </view>
          <view class="order-info">
            <text>{{item.tutoringMode}}</text>
            <text class="info-divider">|</text>
            <text>{{item.salary}}{{item.salaryUnitText}}</text>
            <text class="info-divider">|</text>
            <text>{{item.city}}</text>
          </view>
          <view class="order-footer">
            <view class="order-time">{{item.createTime}}</view>
            <view class="order-actions">
              <button class="action-btn" wx:if="{{item.canPay}}" bindtap="payOrder" data-id="{{item.id}}" catchtap="true">支付</button>
              <button class="action-btn" wx:if="{{item.canEvaluate}}" bindtap="evaluateOrder" data-id="{{item.id}}" catchtap="true">评价</button>
              <button class="action-btn secondary" bindtap="viewOrderDetail" data-id="{{item.id}}" catchtap="true">查看详情</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 无正在进行的订单时显示推荐教师 -->
      <view wx:if="{{ongoingOrders.length === 0}}">
        <!-- 推荐教师 -->
        <view class="recommend-section" wx:if="{{recommendTeachers.length > 0}}">
          <view class="recommend-header">
            <text class="recommend-title">推荐优质教师</text>
            <text class="recommend-subtitle">为您精选优秀教师</text>
          </view>
          <view class="teacher-list">
            <view class="teacher-item" wx:for="{{recommendTeachers}}" wx:key="id" bindtap="bookTeacher" data-teacher="{{item}}">
              <image class="teacher-avatar" src="{{item.avatar || '/assets/images/default_avatar.png'}}" mode="aspectFill" />
              <view class="teacher-info">
                <view class="teacher-name">{{item.name}}</view>
                <view class="teacher-subject">{{item.subject}}</view>
                <view class="teacher-experience">{{item.experience}}</view>
              </view>
              <view class="teacher-rating">
                <text class="rating-star">⭐</text>
                <text>{{item.rating}}</text>
              </view>
              <view class="book-btn">预约</view>
            </view>
          </view>
        </view>

        <!-- 完全无订单时的空状态 -->
        <view class="empty-state" wx:if="{{recommendTeachers.length === 0}}">
          <text class="empty-icon">📚</text>
          <text class="empty-title">暂无订单</text>
          <text class="empty-desc">快来发布家教需求，找到合适的教师吧</text>
          <button class="empty-btn" bindtap="goToPublish">发布家教需求</button>
        </view>
      </view>
    </view>

  </scroll-view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>
</view>

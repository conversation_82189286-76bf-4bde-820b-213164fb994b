<!--pages/parent/home/<USER>
<page-meta
  background-color="#f5f7fa"
  background-text-style="dark"
  page-style="background-color: #f5f7fa;"
/>

<view class="page-container tabbar-page">
  <!-- 自定义导航栏 -->
  <navigation-bar title="众桥辅导" />
  
  <!-- 页面内容 -->
  <scroll-view   class="page-content tabbar-scroll-view"
  scroll-y="{{true}}"
  refresher-enabled="{{true}}"
  refresher-triggered="{{isLoading}}"
  bindrefresherrefresh="onPullDownRefresh"
  bindscrolltolower="onReachBottom">
    
    <!-- 轮播图 -->
    <view class="banner-section" wx:if="{{bannerList.length > 0}}">
      <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="5000" duration="500" circular="true">
        <swiper-item wx:for="{{bannerList}}" wx:key="id">
          <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-item="{{item}}" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 通知公告 -->
    <view class="announcement-section" wx:if="{{announcements.length > 0}}">
      <view class="section-header">
        <view class="section-title-with-icon">
          <text class="announcement-icon">📢</text>
          <text class="section-title-text">通知公告</text>
        </view>
      </view>

      <view class="announcement-list">
        <view
          class="announcement-item"
          wx:for="{{announcements}}"
          wx:key="id"
          bindtap="onAnnouncementTap"
          data-index="{{index}}"
        >
          <view class="announcement-title">{{item.title}}</view>
          <view class="announcement-arrow">></view>
        </view>
      </view>
    </view>



    <!-- 我的订单 -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">📋</text>
          <text>我的订单</text>
        </view>
        <view class="section-more" bindtap="viewAllOrders">
          <text>查看全部</text>
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 正在进行的订单 -->
      <view class="order-list" wx:if="{{ongoingOrders.length > 0}}">
        <view class="order-item" wx:for="{{ongoingOrders}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
          <view class="order-header">
            <view class="order-title">{{item.subjectName}} - {{item.grade}}</view>
            <view class="order-status status-{{item.status}}">{{item.statusText}}</view>
          </view>
          <view class="order-info">
            <text>{{item.tutoringMode}}</text>
            <text class="info-divider">|</text>
            <text>{{item.salary}}{{item.salaryUnitText}}</text>
            <text class="info-divider">|</text>
            <text>{{item.city}}</text>
          </view>
          <view class="order-footer">
            <view class="order-time">{{item.createTime}}</view>
            <view class="order-actions">
              <button class="action-btn" wx:if="{{item.canPay}}" bindtap="payOrder" data-id="{{item.id}}" catchtap="true">支付</button>
              <button class="action-btn" wx:if="{{item.canEvaluate}}" bindtap="evaluateOrder" data-id="{{item.id}}" catchtap="true">评价</button>
              <button class="action-btn secondary" bindtap="viewOrderDetail" data-id="{{item.id}}" catchtap="true">查看详情</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 无正在进行的订单时显示推荐教师 -->
      <view wx:if="{{ongoingOrders.length === 0}}">
        <!-- 推荐教师列表 -->
        <view class="recommend-teachers" wx:if="{{recommendTeachers.length > 0}}">
          <view class="teacher-card" wx:for="{{recommendTeachers}}" wx:key="id" bindtap="viewTeacherDetail" data-teacher="{{item}}">
            <!-- 教师头像和基本信息 -->
            <view class="teacher-header">
              <image class="teacher-avatar" src="{{item.avatar || '/assets/images/default_avatar.png'}}" mode="aspectFill" />
              <view class="teacher-basic">
                <view class="teacher-name-row">
                  <text class="teacher-name">{{item.display_name}}</text>
                  <view class="certification-badge {{item.certification_status == '4' ? 'verified' : 'normal'}}" wx:if="{{item.certification_text}}">{{item.certification_text}}</view>
                </view>
                <view class="teacher-school">{{item.university || '未填写'}} - {{item.major || '未填写'}}</view>
              </view>
            </view>


            <!-- 擅长科目 -->
            <view class="teacher-subjects">
              <text class="subject-label">擅长科目：</text>
              <text class="subject-text">{{item.teaching_subjects || '暂未设置'}}</text>
            </view>


            <!-- 查看详情按钮 -->
            <view class="detail-btn">
              <text>查看详情</text>
            </view>
          </view>
        </view>

        <!-- 完全无订单时的空状态 -->
        <view class="empty-state" wx:if="{{recommendTeachers.length === 0}}">
          <text class="empty-icon">📚</text>
          <text class="empty-title">暂无订单</text>
          <text class="empty-desc">快来发布家教需求，找到合适的教师吧</text>
          <button class="empty-btn" bindtap="goToPublish">发布家教需求</button>
        </view>
      </view>
    </view>

  </scroll-view>
</view>

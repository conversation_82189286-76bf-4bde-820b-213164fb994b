<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherAgreementMapper">
    
    <resultMap type="TeacherAgreement" id="TeacherAgreementResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="agreementContent"    column="agreement_content"    />
        <result property="signatureImage"    column="signature_image"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="pdfFilePath"    column="pdf_file_path"    />
        <result property="signTime"    column="sign_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTeacherAgreementVo">
        select id, teacher_id, teacher_name, agreement_content, signature_image, effective_date, pdf_file_path, sign_time, status, create_by, create_time, update_by, update_time, remark from teacher_agreement
    </sql>

    <select id="selectTeacherAgreementList" parameterType="TeacherAgreement" resultMap="TeacherAgreementResult">
        <include refid="selectTeacherAgreementVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="signTime != null "> and sign_time = #{signTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTeacherAgreementById" parameterType="Long" resultMap="TeacherAgreementResult">
        <include refid="selectTeacherAgreementVo"/>
        where id = #{id}
    </select>

    <select id="selectTeacherAgreementByTeacherId" parameterType="Long" resultMap="TeacherAgreementResult">
        <include refid="selectTeacherAgreementVo"/>
        where teacher_id = #{teacherId} and status = '0'
        order by create_time desc
        limit 1
    </select>
        
    <insert id="insertTeacherAgreement" parameterType="TeacherAgreement" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_agreement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="teacherName != null and teacherName != ''">teacher_name,</if>
            <if test="agreementContent != null and agreementContent != ''">agreement_content,</if>
            <if test="signatureImage != null and signatureImage != ''">signature_image,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="pdfFilePath != null and pdfFilePath != ''">pdf_file_path,</if>
            <if test="signTime != null">sign_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="teacherName != null and teacherName != ''">#{teacherName},</if>
            <if test="agreementContent != null and agreementContent != ''">#{agreementContent},</if>
            <if test="signatureImage != null and signatureImage != ''">#{signatureImage},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="pdfFilePath != null and pdfFilePath != ''">#{pdfFilePath},</if>
            <if test="signTime != null">#{signTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTeacherAgreement" parameterType="TeacherAgreement">
        update teacher_agreement
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="teacherName != null and teacherName != ''">teacher_name = #{teacherName},</if>
            <if test="agreementContent != null and agreementContent != ''">agreement_content = #{agreementContent},</if>
            <if test="signatureImage != null and signatureImage != ''">signature_image = #{signatureImage},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="pdfFilePath != null and pdfFilePath != ''">pdf_file_path = #{pdfFilePath},</if>
            <if test="signTime != null">sign_time = #{signTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherAgreementById" parameterType="Long">
        delete from teacher_agreement where id = #{id}
    </delete>

    <delete id="deleteTeacherAgreementByIds" parameterType="String">
        delete from teacher_agreement where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

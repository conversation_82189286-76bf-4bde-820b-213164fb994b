-- 创建教师会员表
CREATE TABLE IF NOT EXISTS `teacher_membership` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `teacher_id` bigint(20) NOT NULL COMMENT '教师ID',
  `membership_type` varchar(10) NOT NULL DEFAULT '1' COMMENT '会员类型（1普通会员 2高级会员）',
  `membership_date` datetime NOT NULL COMMENT '成为会员日期',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `payment_status` varchar(10) NOT NULL DEFAULT '1' COMMENT '支付状态（0未支付 1已支付 2已退款）',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `status` varchar(10) DEFAULT '1' COMMENT '状态（0无效 1有效）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_membership_type` (`membership_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_membership_date` (`membership_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师会员表';

-- 为教师信息表添加会员相关字段（带存在性检查）

-- 检查并添加会员类型字段
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = DATABASE()
                   AND TABLE_NAME = 'teacher_info'
                   AND COLUMN_NAME = 'membership_type');

SET @sql = IF(@col_exists = 0,
              'ALTER TABLE teacher_info ADD COLUMN membership_type varchar(10) DEFAULT ''1'' COMMENT ''会员类型（1普通会员 2高级会员）''',
              'SELECT ''membership_type字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加会员日期字段
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = DATABASE()
                   AND TABLE_NAME = 'teacher_info'
                   AND COLUMN_NAME = 'membership_date');

SET @sql = IF(@col_exists = 0,
              'ALTER TABLE teacher_info ADD COLUMN membership_date datetime DEFAULT NULL COMMENT ''成为会员日期''',
              'SELECT ''membership_date字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建会员类型索引
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                   WHERE TABLE_SCHEMA = DATABASE()
                   AND TABLE_NAME = 'teacher_info'
                   AND INDEX_NAME = 'idx_membership_type');

SET @sql = IF(@idx_exists = 0,
              'ALTER TABLE teacher_info ADD INDEX idx_membership_type (membership_type)',
              'SELECT ''idx_membership_type索引已存在，跳过创建'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建会员日期索引
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                   WHERE TABLE_SCHEMA = DATABASE()
                   AND TABLE_NAME = 'teacher_info'
                   AND INDEX_NAME = 'idx_membership_date');

SET @sql = IF(@idx_exists = 0,
              'ALTER TABLE teacher_info ADD INDEX idx_membership_date (membership_date)',
              'SELECT ''idx_membership_date索引已存在，跳过创建'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
-- 初始化所有教师为普通会员（如果membership_type为NULL）
UPDATE `teacher_info`
SET `membership_type` = '1'
WHERE `membership_type` IS NULL;

-- 同步更新教师信息表的会员状态（如果有会员记录）
-- 注意：只有在teacher_membership表有数据时才会执行更新
UPDATE `teacher_info` t
INNER JOIN `teacher_membership` m ON t.id = m.teacher_id
SET t.membership_type = m.membership_type,
    t.membership_date = m.membership_date
WHERE (m.status IS NULL OR m.status = '1') AND m.payment_status = '1';

-- 验证表结构
DESCRIBE teacher_membership;
DESCRIBE teacher_info;

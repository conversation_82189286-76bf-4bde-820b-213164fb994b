const config = require('../config/index.js');

/**
 * 网络检测工具
 */
class NetworkUtils {
  
  /**
   * 检查网络连接状态
   */
  static checkNetworkStatus() {
    return new Promise((resolve, reject) => {
      wx.getNetworkType({
        success: (res) => {
          console.log('网络类型:', res.networkType);
          if (res.networkType === 'none') {
            wx.showToast({
              title: '网络连接不可用',
              icon: 'none'
            });
            reject(new Error('网络连接不可用'));
          } else {
            resolve(res.networkType);
          }
        },
        fail: (err) => {
          console.error('获取网络状态失败:', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 测试服务器连接
   */
  static testServerConnection() {
    return new Promise((resolve, reject) => {
      console.log('测试服务器连接:', config.baseUrl);
      
      wx.request({
        url: `${config.baseUrl}/common/ping`, // 需要后端提供一个ping接口
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          console.log('服务器连接测试结果:', res);
          if (res.statusCode === 200) {
            resolve(true);
          } else {
            reject(new Error(`服务器响应异常: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          console.error('服务器连接失败:', err);
          let errorMsg = '服务器连接失败';
          
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMsg = '连接超时，请检查网络';
            } else if (err.errMsg.includes('fail')) {
              errorMsg = '无法连接到服务器';
            }
          }
          
          reject(new Error(errorMsg));
        }
      });
    });
  }

  /**
   * 检查上传权限和配置
   */
  static async checkUploadEnvironment() {
    try {
      console.log('=== 检查上传环境 ===');
      
      // 1. 检查网络状态
      const networkType = await this.checkNetworkStatus();
      console.log('网络检查通过:', networkType);
      
      // 2. 检查服务器连接
      await this.testServerConnection();
      console.log('服务器连接检查通过');
      
      // 3. 检查Token
      const token = wx.getStorageSync('token');
      if (!token) {
        throw new Error('用户未登录，请先登录');
      }
      console.log('Token检查通过');
      
      // 4. 检查配置
      console.log('当前配置:', {
        baseUrl: config.baseUrl,
        debug: config.debug,
        uploadMaxSize: config.uploadMaxSize
      });
      
      return {
        success: true,
        networkType,
        hasToken: !!token,
        config: config
      };
      
    } catch (error) {
      console.error('上传环境检查失败:', error);
      wx.showToast({
        title: error.message || '环境检查失败',
        icon: 'none'
      });
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  static getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: (res) => {
          console.log('文件信息:', res);
          
          // 检查文件大小（转换为MB）
          const fileSizeMB = res.size / (1024 * 1024);
          if (fileSizeMB > config.uploadMaxSize) {
            reject(new Error(`文件大小超过限制(${config.uploadMaxSize}MB)`));
            return;
          }
          
          resolve({
            size: res.size,
            sizeMB: fileSizeMB.toFixed(2)
          });
        },
        fail: (err) => {
          console.error('获取文件信息失败:', err);
          reject(new Error('获取文件信息失败'));
        }
      });
    });
  }
}

module.exports = NetworkUtils;

# 发布家教功能优化验证清单

## 🎯 优化内容

### 1. 年级二级选择
- **一级选项**：小学、初中、高中、大学、其他
- **二级选项**：根据一级选择动态显示具体年级
- **数据来源**：管理员后台可配置

### 2. 科目多选功能
- **选择方式**：弹窗多选，支持添加/移除
- **显示效果**：标签形式展示已选科目
- **数据来源**：管理员后台可配置

### 3. 可辅导时间优化
- **时间选择**：周一到周日多选（写死）
- **时间段**：起始时间和结束时间选择器
- **格式**：几时几分的时间选择

### 4. 联系人信息优化
- **手机号**：不可修改，默认为登录手机号
- **称谓选择**：联系人后面可选先生/女士
- **显示效果**：张三先生、李四女士

## 📋 数据结构

### 年级配置
```sql
-- 年级分类表
grade_category: id, category_name, sort_order, status

-- 年级信息表  
grade_info: id, category_id, grade_name, sort_order, status
```

### API响应格式
```json
{
  "code": 200,
  "data": {
    "subjects": [
      {"id": 1, "name": "语文"},
      {"id": 2, "name": "数学"}
    ],
    "grades": {
      "categories": [
        {"id": 1, "categoryName": "小学", "sortOrder": 1}
      ],
      "gradesByCategory": {
        "1": [
          {"id": 1, "gradeName": "一年级"},
          {"id": 2, "gradeName": "二年级"}
        ]
      }
    },
    "tutoringDays": [
      {"value": 1, "name": "周一"},
      {"value": 2, "name": "周二"}
    ]
  }
}
```

## 🧪 测试步骤

### 1. 执行数据库脚本
```sql
-- 执行 create_grade_config_tables.sql
-- 创建年级配置表并插入初始数据
```

### 2. 重启后端服务
确保新的API接口生效

### 3. 前端功能测试

#### 年级选择测试
- [ ] 点击学段选择器，显示：小学、初中、高中、大学、其他
- [ ] 选择"小学"后，年级选择器显示：一年级到六年级
- [ ] 选择"初中"后，年级选择器显示：初一到初三
- [ ] 未选择学段时，年级选择器为禁用状态

#### 科目选择测试
- [ ] 点击"请选择科目"显示科目选择弹窗
- [ ] 弹窗中显示所有可选科目
- [ ] 可以多选科目，选中的科目有勾选标识
- [ ] 点击确定后，选中科目以标签形式显示
- [ ] 可以点击标签上的×移除科目
- [ ] 已选科目后显示"添加科目"按钮

#### 时间选择测试
- [ ] 可辅导时间显示周一到周日的多选框
- [ ] 可以选择多个星期
- [ ] 时间段有开始时间和结束时间选择器
- [ ] 时间选择器显示几时几分格式
- [ ] 选择后自动组合为时间段格式

#### 联系人测试
- [ ] 联系人姓名输入框正常
- [ ] 称谓选择器显示"先生"和"女士"选项
- [ ] 手机号显示为登录手机号且不可修改
- [ ] 手机号下方显示提示文字

## 🔍 API测试

### 获取配置数据
```bash
curl -X GET "http://t8f63f47.natappfree.cc/applet/parent/publish/config" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**：
- subjects数组包含所有科目
- grades对象包含分类和年级映射
- tutoringDays数组包含周一到周日

## 📱 界面效果预览

### 年级选择
```
┌─────────────────────────────────┐
│ 学生年级 *                       │
│ ┌─────────┐ ┌─────────┐         │
│ │  小学   │ │ 一年级  │         │
│ └─────────┘ └─────────┘         │
└─────────────────────────────────┘
```

### 科目选择
```
┌─────────────────────────────────┐
│ 辅导科目 *                       │
│ ┌─────┐ ┌─────┐                 │
│ │数学×│ │英语×│                 │
│ └─────┘ └─────┘                 │
│ ┌─────────────────┐             │
│ │  + 添加科目      │             │
│ └─────────────────┘             │
└─────────────────────────────────┘
```

### 时间选择
```
┌─────────────────────────────────┐
│ 可辅导时间                       │
│ ☑周一 ☑周三 ☐周五 ☐周日        │
│                                 │
│ 时间段                          │
│ 开始时间：┌─────────┐           │
│          │ 09:00   │           │
│          └─────────┘           │
│ 结束时间：┌─────────┐           │
│          │ 11:00   │           │
│          └─────────┘           │
└─────────────────────────────────┘
```

### 联系人信息
```
┌─────────────────────────────────┐
│ 联系人 *                        │
│ ┌─────────┐ ┌─────┐             │
│ │  张三   │ │先生 │             │
│ └─────────┘ └─────┘             │
│                                 │
│ 联系电话 *                      │
│ ┌─────────────────┐             │
│ │ 138****8888     │             │
│ └─────────────────┘             │
│ （默认为登录手机号，不可修改）    │
└─────────────────────────────────┘
```

## ✅ 验收标准

### 功能完整性
- ✅ 年级二级选择正常工作
- ✅ 科目多选弹窗正常显示和操作
- ✅ 时间多选和时间段选择正常
- ✅ 联系人称谓选择正常
- ✅ 手机号正确显示且不可修改

### 数据准确性
- ✅ 配置数据从后台API正确获取
- ✅ 选择的数据正确保存到表单
- ✅ 提交时数据格式正确

### 用户体验
- ✅ 界面美观，操作流畅
- ✅ 交互反馈及时
- ✅ 错误提示清晰
- ✅ 响应式适配良好

## 🚨 常见问题排查

### 配置数据获取失败
1. 检查后端API是否正常启动
2. 确认API路径是否正确
3. 验证token是否有效

### 年级选择不显示
1. 检查gradeCategories数据是否正确
2. 确认gradesByCategory映射关系
3. 验证前端数据绑定

### 科目弹窗不显示
1. 检查showSubjectModal状态
2. 确认CSS z-index设置
3. 验证弹窗事件绑定

### 时间选择异常
1. 检查tutoringDays数据格式
2. 确认checkbox事件处理
3. 验证时间格式转换

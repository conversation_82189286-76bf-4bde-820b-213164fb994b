# MyBatis "Invalid bound statement" 问题排查报告

## 🎯 问题描述
```
Invalid bound statement (not found): com.ruoyi.education.mapper.TutorOrderMapper.selectNewTutorOrderList
```

## 🔍 已检查项目

### ✅ 1. Mapper接口定义
**文件**: `TutorOrderMapper.java` (第120行)
```java
public List<TutorOrder> selectNewTutorOrderList();
```
**状态**: ✅ 正确

### ✅ 2. XML映射定义
**文件**: `TutorOrderMapper.xml` (第158-164行)
```xml
<select id="selectNewTutorOrderList" resultMap="TutorOrderResult">
    <include refid="selectTutorOrderVo"/>
    where o.status = '2'  -- 审核通过，报名中状态
      and o.selected_teacher_id is null  -- 还未选择教师
      and (o.deleted is null or o.deleted = 0)  -- 未删除的订单
    order by o.is_top desc, o.create_time desc
</select>
```
**状态**: ✅ 正确

### ✅ 3. XML namespace
**文件**: `TutorOrderMapper.xml` (第5行)
```xml
<mapper namespace="com.ruoyi.education.mapper.TutorOrderMapper">
```
**状态**: ✅ 正确

### ✅ 4. selectTutorOrderVo定义
**文件**: `TutorOrderMapper.xml` (第42-47行)
```xml
<sql id="selectTutorOrderVo">
    select o.id, o.order_no, ..., o.area_name, ..., o.create_time,
           COALESCE(o.view_count, 0) as view_count,
           COALESCE((select count(*) from order_application a where a.order_id = o.id), 0) as apply_count
    from tutor_order o
</sql>
```
**状态**: ✅ 已修复（添加了缺失的area_name字段）

### ✅ 5. MyBatis配置
**文件**: `application.yml`
```yaml
mybatis:
  typeAliasesPackage: com.ruoyi.**.domain
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  configLocation: classpath:mybatis/mybatis-config.xml
```
**状态**: ✅ 正确

### ✅ 6. MapperScan注解
**文件**: `RuoYiApplication.java`
```java
@MapperScan("com.ruoyi.**.mapper")
```
**状态**: ✅ 正确

### ✅ 7. 编译后的文件
**文件**: `target/classes/mapper/education/TutorOrderMapper.xml`
**状态**: ✅ 已正确编译，包含selectNewTutorOrderList方法

## 🚀 解决方案

### 方案1：重新编译项目（推荐）
```bash
# 清理并重新编译
mvn clean compile
# 或者在IDE中
# 1. Clean Project
# 2. Rebuild Project
```

### 方案2：检查类路径
确保以下文件在正确的位置：
- `TutorOrderMapper.java` 在 `src/main/java/com/ruoyi/education/mapper/`
- `TutorOrderMapper.xml` 在 `src/main/resources/mapper/education/`

### 方案3：重启应用
有时候是类加载器缓存问题，重启Spring Boot应用可以解决。

### 方案4：检查依赖
确保MyBatis相关依赖正确：
```xml
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
</dependency>
```

## 🔧 已修复的问题

### 问题1：selectTutorOrderVo缺少area_name字段
**修复前**:
```sql
select o.id, ..., o.city, o.detailed_address, ...
```

**修复后**:
```sql
select o.id, ..., o.city, o.area_name, o.detailed_address, ...
```

这个问题可能导致ResultMap映射失败，进而影响整个Mapper的加载。

## 📋 排查步骤

1. **✅ 已完成**: 检查XML映射语法
2. **✅ 已完成**: 检查namespace匹配
3. **✅ 已完成**: 检查方法名匹配
4. **✅ 已完成**: 修复selectTutorOrderVo中缺失的字段
5. **🔄 建议执行**: 重新编译项目
6. **🔄 建议执行**: 重启应用

## 💡 可能的根本原因

1. **编译缓存问题**: target目录中的文件可能没有及时更新
2. **类加载器缓存**: Spring Boot的类加载器可能缓存了旧版本的Mapper
3. **XML语法错误**: selectTutorOrderVo中缺少字段可能导致整个Mapper加载失败

## 🎯 建议操作

1. **立即执行**:
   ```bash
   mvn clean compile
   ```

2. **重启应用**:
   停止Spring Boot应用，然后重新启动

3. **验证修复**:
   访问 `/applet/tutorOrder/new` 接口，检查是否还有错误

## 📞 如果问题仍然存在

请检查：
1. IDE是否正确识别了Mapper接口
2. 是否有其他XML语法错误
3. 数据库连接是否正常
4. 是否有其他Mapper也存在类似问题

---

**总结**: 主要问题是selectTutorOrderVo中缺少area_name字段，这可能导致ResultMap映射失败。已修复此问题，建议重新编译和重启应用。

-- 手动修复teacher_membership表缺失字段

-- 1. 查看当前表结构
DESCRIBE teacher_membership;

-- 2. 添加order_no字段（支付订单号）
ALTER TABLE teacher_membership 
ADD COLUMN order_no varchar(64) DEFAULT NULL COMMENT '支付订单号' 
AFTER payment_time;

-- 3. 添加status字段（状态）
ALTER TABLE teacher_membership 
ADD COLUMN status char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）' 
AFTER order_no;

-- 4. 验证字段是否添加成功
DESCRIBE teacher_membership;

-- 5. 更新现有数据的status字段
UPDATE teacher_membership 
SET status = '1' 
WHERE status IS NULL;

-- 6. 验证数据更新
SELECT id, teacher_id, membership_type, payment_status, order_no, status, create_time 
FROM teacher_membership 
LIMIT 5;

-- 完成提示
SELECT '✅ teacher_membership表字段修复完成！现在可以正常创建支付订单了。' as result;

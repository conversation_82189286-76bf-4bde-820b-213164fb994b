<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.TeacherInfoMapper">

    <resultMap type="TeacherInfo" id="TeacherInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="gender"    column="gender"    />
        <result property="birthday"    column="birthday"    />
        <result property="avatar"    column="avatar"    />
        <result property="status"    column="status"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTeacherInfoVo">
        select id, name, gender, birthday, avatar, status, user_id, create_time, update_time, create_by, update_by from teacher_info
    </sql>

    <select id="selectTeacherInfoList" parameterType="TeacherInfo" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectTeacherInfoById" parameterType="Long" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertTeacherInfo" parameterType="TeacherInfo" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="gender != null">gender,</if>
            <if test="birthday != null">birthday,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="gender != null">#{gender},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateTeacherInfo" parameterType="TeacherInfo">
        update teacher_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherInfoById" parameterType="Long">
        delete from teacher_info where id = #{id}
    </delete>

    <delete id="deleteTeacherInfoByIds" parameterType="String">
        delete from teacher_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据教师ID查询所教的科目列表 -->
    <select id="selectSubjectsByTeacherId" parameterType="Long" resultMap="com.ruoyi.education.mapper.SubjectMapper.SubjectResult">
        SELECT s.id, s.name, s.sort
        FROM subject s
                 JOIN teacher_subject ts ON s.id = ts.subject_id
        WHERE ts.teacher_id = #{teacherId}
        ORDER BY s.sort ASC
    </select>

    <!-- 根据教师ID删除教师-科目关联 -->
    <delete id="deleteTeacherSubjectByTeacherId" parameterType="Long">
        DELETE FROM teacher_subject WHERE teacher_id = #{teacherId}
    </delete>

    <!-- 批量新增教师-科目关联 -->
    <insert id="batchInsertTeacherSubject">
        INSERT INTO teacher_subject(teacher_id, subject_id) VALUES
        <foreach collection="subjectIds" item="subjectId" separator=",">
            (#{teacherId}, #{subjectId})
        </foreach>
    </insert>

</mapper>

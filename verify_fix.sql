-- 验证管理员代发布订单生源接口修复的SQL脚本

-- 1. 查看最新的订单数据
SELECT 
    id,
    order_no,
    subject_name,
    grade,
    status,
    selected_teacher_id,
    publisher_type,
    create_time
FROM tutor_order 
ORDER BY create_time DESC 
LIMIT 10;

-- 2. 查看符合生源条件的订单（应该包含管理员发布的订单）
SELECT 
    id,
    order_no,
    subject_name,
    grade,
    status,
    selected_teacher_id,
    publisher_type,
    create_time
FROM tutor_order 
WHERE status = '2'  -- 审核通过
  AND selected_teacher_id IS NULL  -- 还未选择教师
ORDER BY is_top DESC, create_time DESC;

-- 3. 统计各种状态的订单数量
SELECT 
    status,
    COUNT(*) as count,
    CASE status
        WHEN '1' THEN '待审核'
        WHEN '2' THEN '审核通过'
        WHEN '3' THEN '审核拒绝'
        WHEN '4' THEN '报名中'
        WHEN '5' THEN '已选择教师'
        WHEN '6' THEN '试课中'
        WHEN '7' THEN '进行中'
        WHEN '8' THEN '已完成'
        WHEN '9' THEN '已取消'
        ELSE '未知状态'
    END as status_name
FROM tutor_order 
GROUP BY status
ORDER BY status;

-- 4. 查看管理员发布的订单
SELECT 
    id,
    order_no,
    subject_name,
    grade,
    status,
    selected_teacher_id,
    publisher_type,
    create_time
FROM tutor_order 
WHERE publisher_type = '2'  -- 管理员发布
ORDER BY create_time DESC;

-- 5. 验证字段是否存在（如果字段不存在会报错）
SELECT 
    COUNT(*) as total_orders,
    COUNT(selected_teacher_id) as orders_with_selected_teacher,
    COUNT(*) - COUNT(selected_teacher_id) as orders_without_selected_teacher
FROM tutor_order;

-- 创建年级配置相关表

-- 1. 创建年级分类表（一级选项）
CREATE TABLE IF NOT EXISTS `grade_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='年级分类表';

-- 2. 创建年级表（二级选项）
CREATE TABLE IF NOT EXISTS `grade_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `grade_name` varchar(50) NOT NULL COMMENT '年级名称',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='年级信息表';

-- 3. 插入初始数据
INSERT INTO `grade_category` (`category_name`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('小学', 1, '0', 'admin', NOW()),
('初中', 2, '0', 'admin', NOW()),
('高中', 3, '0', 'admin', NOW()),
('大学', 4, '0', 'admin', NOW()),
('其他', 5, '0', 'admin', NOW());

-- 4. 插入年级数据
INSERT INTO `grade_info` (`category_id`, `grade_name`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
-- 小学
(1, '一年级', 1, '0', 'admin', NOW()),
(1, '二年级', 2, '0', 'admin', NOW()),
(1, '三年级', 3, '0', 'admin', NOW()),
(1, '四年级', 4, '0', 'admin', NOW()),
(1, '五年级', 5, '0', 'admin', NOW()),
(1, '六年级', 6, '0', 'admin', NOW()),
-- 初中
(2, '初一', 1, '0', 'admin', NOW()),
(2, '初二', 2, '0', 'admin', NOW()),
(2, '初三', 3, '0', 'admin', NOW()),
-- 高中
(3, '高一', 1, '0', 'admin', NOW()),
(3, '高二', 2, '0', 'admin', NOW()),
(3, '高三', 3, '0', 'admin', NOW()),
-- 大学
(4, '大一', 1, '0', 'admin', NOW()),
(4, '大二', 2, '0', 'admin', NOW()),
(4, '大三', 3, '0', 'admin', NOW()),
(4, '大四', 4, '0', 'admin', NOW()),
-- 其他
(5, '成人教育', 1, '0', 'admin', NOW()),
(5, '职业培训', 2, '0', 'admin', NOW());

-- 5. 创建可辅导时间配置表
CREATE TABLE IF NOT EXISTS `tutoring_time_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `day_name` varchar(20) NOT NULL COMMENT '星期名称',
  `day_value` int(11) NOT NULL COMMENT '星期值（1-7）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='可辅导时间配置表';

-- 6. 插入星期数据
INSERT INTO `tutoring_time_config` (`day_name`, `day_value`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('周一', 1, 1, '0', 'admin', NOW()),
('周二', 2, 2, '0', 'admin', NOW()),
('周三', 3, 3, '0', 'admin', NOW()),
('周四', 4, 4, '0', 'admin', NOW()),
('周五', 5, 5, '0', 'admin', NOW()),
('周六', 6, 6, '0', 'admin', NOW()),
('周日', 7, 7, '0', 'admin', NOW());

-- 7. 验证数据
SELECT '=== 年级分类数据 ===' as info;
SELECT * FROM grade_category ORDER BY sort_order;

SELECT '=== 年级信息数据 ===' as info;
SELECT gc.category_name, gi.grade_name, gi.sort_order
FROM grade_info gi
LEFT JOIN grade_category gc ON gi.category_id = gc.id
ORDER BY gc.sort_order, gi.sort_order;

SELECT '=== 可辅导时间配置 ===' as info;
SELECT * FROM tutoring_time_config ORDER BY sort_order;

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.PlatformCommissionConfigMapper">
    
    <resultMap type="PlatformCommissionConfig" id="PlatformCommissionConfigResult">
        <result property="id"    column="id"    />
        <result property="configName"    column="config_name"    />
        <result property="minAmount"    column="min_amount"    />
        <result property="maxAmount"    column="max_amount"    />
        <result property="commissionRate"    column="commission_rate"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlatformCommissionConfigVo">
        select id, config_name, min_amount, max_amount, commission_rate, is_enabled, sort_order, remark, create_by, create_time, update_by, update_time from platform_commission_config
    </sql>

    <select id="selectPlatformCommissionConfigList" parameterType="PlatformCommissionConfig" resultMap="PlatformCommissionConfigResult">
        <include refid="selectPlatformCommissionConfigVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="minAmount != null "> and min_amount = #{minAmount}</if>
            <if test="maxAmount != null "> and max_amount = #{maxAmount}</if>
            <if test="commissionRate != null "> and commission_rate = #{commissionRate}</if>
            <if test="isEnabled != null  and isEnabled != ''"> and is_enabled = #{isEnabled}</if>
        </where>
        order by sort_order asc, id asc
    </select>
    
    <select id="selectPlatformCommissionConfigById" parameterType="Long" resultMap="PlatformCommissionConfigResult">
        <include refid="selectPlatformCommissionConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionConfigByAmount" parameterType="java.math.BigDecimal" resultMap="PlatformCommissionConfigResult">
        <include refid="selectPlatformCommissionConfigVo"/>
        where is_enabled = '1'
        and min_amount &lt;= #{amount}
        and (max_amount is null or max_amount &gt; #{amount})
        order by sort_order asc
        limit 1
    </select>

    <select id="selectEnabledCommissionConfigs" resultMap="PlatformCommissionConfigResult">
        <include refid="selectPlatformCommissionConfigVo"/>
        where is_enabled = '1'
        order by sort_order asc, min_amount asc
    </select>
        
    <insert id="insertPlatformCommissionConfig" parameterType="PlatformCommissionConfig" useGeneratedKeys="true" keyProperty="id">
        insert into platform_commission_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="minAmount != null">min_amount,</if>
            <if test="maxAmount != null">max_amount,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="minAmount != null">#{minAmount},</if>
            <if test="maxAmount != null">#{maxAmount},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlatformCommissionConfig" parameterType="PlatformCommissionConfig">
        update platform_commission_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="minAmount != null">min_amount = #{minAmount},</if>
            <if test="maxAmount != null">max_amount = #{maxAmount},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlatformCommissionConfigById" parameterType="Long">
        delete from platform_commission_config where id = #{id}
    </delete>

    <delete id="deletePlatformCommissionConfigByIds" parameterType="String">
        delete from platform_commission_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

# 家长登录JWT错误修复指南

## 🎯 问题现象

从日志可以看到：
```
ERROR c.r.f.w.s.TokenService - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
DEBUG c.r.e.m.P.selectParentInfoByOpenid - ==> Parameters: mock_openid_for_parent(String)
DEBUG c.r.e.m.P.selectParentInfoByOpenid - <==      Total: 0
DEBUG c.r.e.m.P.selectParentInfoByPhoneNumber - ==> Parameters: 13800138000(String)  
DEBUG c.r.e.m.P.selectParentInfoByPhoneNumber - <==      Total: 0
登录异常: 获取用户账户异常
```

---

## 🔍 问题分析

### 1. JWT Token格式错误
- **错误**：`JWT strings must contain exactly 2 period characters. Found: 0`
- **原因**：Token生成过程中出现异常，导致返回的不是有效的JWT格式

### 2. 数据库查询无结果
- **查询openid**：`mock_openid_for_parent` → 0条记录
- **查询手机号**：`13800138000` → 0条记录
- **原因**：数据库中没有对应的测试数据

### 3. 数据不匹配
- **代码中的手机号**：`13800138000`
- **之前插入的手机号**：`13800138001`
- **不匹配导致查询失败**

---

## ✅ 修复步骤

### 步骤1：执行修复SQL脚本

在数据库中执行以下SQL：

```sql
-- 1. 删除可能存在的错误数据
DELETE FROM parent_info WHERE openid = 'mock_openid_for_parent';

-- 2. 插入正确的测试数据
INSERT INTO `parent_info` (
    `openid`, 
    `user_name`, 
    `nick_name`, 
    `avatar`, 
    `real_name`, 
    `phone_number`, 
    `total_orders`, 
    `completed_orders`, 
    `average_rating`, 
    `status`, 
    `blacklist_status`, 
    `create_by`, 
    `create_time`
) VALUES (
    'mock_openid_for_parent',     -- 与代码中的openid一致
    'parent:1', 
    '测试家长', 
    '/assets/images/default_avatar.png', 
    '张家长', 
    '13800138000',                -- 与代码中的手机号一致
    0, 
    0, 
    0.00, 
    '0', 
    '0', 
    'system', 
    NOW()
);

-- 3. 验证数据是否正确插入
SELECT * FROM parent_info WHERE openid = 'mock_openid_for_parent';
SELECT * FROM parent_info WHERE phone_number = '13800138000';
```

### 步骤2：重新编译并重启后端服务

```bash
# 停止当前服务
# 找到Java进程并停止

# 重新编译
cd RuoYi-Vue-master
mvn clean compile

# 重新启动服务
.\ry.bat
```

### 步骤3：验证修复

1. **检查数据库数据**：
```sql
SELECT * FROM parent_info WHERE openid = 'mock_openid_for_parent';
```
应该返回1条记录。

2. **测试家长登录**：
- 选择"我是家长"身份
- 点击登录按钮
- 观察后端控制台日志

---

## 🧪 测试验证

### 1. 数据库验证
```sql
-- 检查测试数据是否存在
SELECT 
    id, 
    openid, 
    user_name, 
    nick_name, 
    phone_number, 
    status 
FROM parent_info 
WHERE openid = 'mock_openid_for_parent' 
   OR phone_number = '13800138000';
```

**预期结果**：应该返回1条记录，包含正确的openid和手机号。

### 2. 接口测试
```bash
# 使用curl测试家长登录
curl -X POST http://t8f63f47.natappfree.cc/applet/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "mock_code",
    "userType": "parent",
    "nickName": "测试家长",
    "avatar": ""
  }'
```

**预期结果**：应该返回包含token和userInfo的成功响应。

### 3. 前端测试
- 打开小程序登录页面
- 选择"我是家长"身份
- 点击登录按钮
- 检查是否成功跳转到家长端首页

---

## 📋 关键数据对应关系

### 代码中的模拟数据
```java
// AppletLoginController.java 第56-58行
String mockOpenid = "mock_openid_for_" + userType;  // mock_openid_for_parent
String mockPhoneNumber = "13800138000";
```

### 数据库中的测试数据
```sql
INSERT INTO parent_info (openid, phone_number, ...) VALUES 
('mock_openid_for_parent', '13800138000', ...);
```

**必须完全匹配**，否则查询会失败。

---

## 🔍 日志分析

### 修复前的错误日志
```
ERROR TokenService - JWT strings must contain exactly 2 period characters. Found: 0
DEBUG selectParentInfoByOpenid - Total: 0
DEBUG selectParentInfoByPhoneNumber - Total: 0
登录异常: 获取用户账户异常
```

### 修复后的正常日志
```
DEBUG selectParentInfoByOpenid - Total: 1
INFO 家长登录成功，ID: 1
DEBUG TokenService - 创建Token成功
```

---

## ⚠️ 注意事项

### 1. 数据一致性
- openid必须与代码中的模拟值完全一致
- 手机号必须与代码中的模拟值完全一致
- 状态字段必须为'0'（正常状态）

### 2. 字符编码
- 确保数据库连接使用utf8mb4编码
- 确保中文字符正确存储

### 3. 权限检查
- 确保数据库用户有INSERT、SELECT权限
- 确保表结构正确创建

---

## 🎉 预期结果

修复完成后：
- ✅ 家长登录不再报JWT错误
- ✅ 数据库查询能找到对应记录
- ✅ 成功生成有效的JWT Token
- ✅ 正确跳转到家长端首页
- ✅ TabBar显示家长端3个导航项

---

## 🚨 如果仍有问题

### 检查后端详细日志
修复后的代码会打印完整的异常堆栈信息：
```java
} catch (Exception e) {
    System.err.println("登录异常: " + e.getMessage());
    e.printStackTrace(); // 完整堆栈信息
    return AjaxResult.error("登录失败，请重试");
}
```

### 常见问题排查
1. **数据库连接问题**：检查数据库是否正常连接
2. **表结构问题**：确认parent_info表已正确创建
3. **权限问题**：确认数据库用户有足够权限
4. **编码问题**：确认数据库和连接编码一致

现在请执行修复SQL脚本，然后重启后端服务进行测试！🚀

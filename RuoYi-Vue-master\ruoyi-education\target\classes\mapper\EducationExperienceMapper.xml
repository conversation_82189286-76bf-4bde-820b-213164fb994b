<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.EducationExperienceMapper">

    <resultMap type="EducationExperience" id="EducationExperienceResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="school"    column="school"    />
        <result property="degree"    column="degree"    />
        <result property="major"    column="major"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectEducationExperienceVo">
        select id, teacher_id, school, degree, major, start_date, end_date, create_time, update_time, create_by, update_by from education_experience
    </sql>

    <select id="selectEducationExperienceList" parameterType="EducationExperience" resultMap="EducationExperienceResult">
        <include refid="selectEducationExperienceVo"/>
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="school != null  and school != ''"> and school like concat('%', #{school}, '%')</if>
        </where>
    </select>

    <select id="selectEducationExperienceById" parameterType="Long" resultMap="EducationExperienceResult">
        <include refid="selectEducationExperienceVo"/>
        where id = #{id}
    </select>

    <insert id="insertEducationExperience" parameterType="EducationExperience" useGeneratedKeys="true" keyProperty="id">
        insert into education_experience
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="school != null and school != ''">school,</if>
            <if test="degree != null and degree != ''">degree,</if>
            <if test="major != null and major != ''">major,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="school != null and school != ''">#{school},</if>
            <if test="degree != null and degree != ''">#{degree},</if>
            <if test="major != null and major != ''">#{major},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateEducationExperience" parameterType="EducationExperience">
        update education_experience
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="school != null and school != ''">school = #{school},</if>
            <if test="degree != null and degree != ''">degree = #{degree},</if>
            <if test="major != null and major != ''">major = #{major},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEducationExperienceById" parameterType="Long">
        delete from education_experience where id = #{id}
    </delete>

    <delete id="deleteEducationExperienceByIds" parameterType="String">
        delete from education_experience where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 
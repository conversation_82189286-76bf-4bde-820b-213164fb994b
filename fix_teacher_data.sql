-- 修复教师数据脚本

-- 1. 检查当前教师数据状态
SELECT '=== 检查教师基本信息 ===' as info;
SELECT 
    id, real_name, nick_name, 
    birthday, age, 
    university, major, grade,
    certification_status, status
FROM teacher_info 
WHERE status = '0' 
ORDER BY id 
LIMIT 10;

-- 2. 检查生日字段为空的教师
SELECT '=== 生日字段为空的教师 ===' as info;
SELECT 
    id, real_name, nick_name, birthday, age
FROM teacher_info 
WHERE status = '0' AND birthday IS NULL;

-- 3. 为测试教师添加生日数据
UPDATE teacher_info 
SET birthday = '1998-05-15'
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 1) as temp)
  AND birthday IS NULL;

UPDATE teacher_info 
SET birthday = '1999-08-20'
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 1,1) as temp)
  AND birthday IS NULL;

UPDATE teacher_info 
SET birthday = '1997-12-10'
WHERE id = (SELECT id FROM (SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 2,1) as temp)
  AND birthday IS NULL;

-- 4. 检查科目关联数据
SELECT '=== 检查科目关联数据 ===' as info;
SELECT 
    t.id, t.real_name,
    GROUP_CONCAT(s.subject_name) as subjects
FROM teacher_info t
LEFT JOIN teacher_subject ts ON t.id = ts.teacher_id
LEFT JOIN course_subject s ON ts.subject_id = s.id
WHERE t.status = '0' AND t.certification_status = '4'
GROUP BY t.id
LIMIT 5;

-- 5. 为没有科目的教师添加科目关联
-- 首先检查是否有科目数据
SELECT '=== 检查科目表数据 ===' as info;
SELECT id, subject_name FROM course_subject ORDER BY id LIMIT 10;

-- 为前3个教师添加科目关联（如果没有的话）
INSERT IGNORE INTO teacher_subject (teacher_id, subject_id)
SELECT t.id, s.id
FROM teacher_info t
CROSS JOIN course_subject s
WHERE t.status = '0' 
  AND t.id IN (
    SELECT id FROM (
      SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 3
    ) as temp
  )
  AND s.id IN (1, 2, 3); -- 假设前3个科目是数学、语文、英语

-- 6. 检查教育背景数据
SELECT '=== 检查教育背景数据 ===' as info;
SELECT 
    teacher_id, school, degree, major, start_date, end_date
FROM education_experience 
WHERE teacher_id IN (
    SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 5
);

-- 7. 为测试教师添加教育背景（如果没有的话）
INSERT IGNORE INTO education_experience (teacher_id, school, degree, major, start_date, end_date, create_time)
SELECT 
    t.id,
    CASE 
        WHEN t.id % 3 = 1 THEN '北京大学'
        WHEN t.id % 3 = 2 THEN '清华大学'
        ELSE '复旦大学'
    END as school,
    '本科' as degree,
    CASE 
        WHEN t.id % 3 = 1 THEN '数学与应用数学'
        WHEN t.id % 3 = 2 THEN '计算机科学与技术'
        ELSE '英语'
    END as major,
    '2016-09-01' as start_date,
    '2020-06-30' as end_date,
    NOW() as create_time
FROM teacher_info t
WHERE t.status = '0' 
  AND t.id IN (
    SELECT id FROM (
      SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 3
    ) as temp
  )
  AND NOT EXISTS (
    SELECT 1 FROM education_experience e WHERE e.teacher_id = t.id
  );

-- 8. 检查教学经历数据
SELECT '=== 检查教学经历数据 ===' as info;
SELECT 
    teacher_id, teaching_target, content, start_date, end_date
FROM teaching_experience 
WHERE teacher_id IN (
    SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 5
);

-- 9. 为测试教师添加教学经历（如果没有的话）
INSERT IGNORE INTO teaching_experience (teacher_id, teaching_target, content, start_date, end_date, create_time)
SELECT 
    t.id,
    CASE 
        WHEN t.id % 3 = 1 THEN '初三数学辅导'
        WHEN t.id % 3 = 2 THEN '高一英语家教'
        ELSE '小学全科辅导'
    END as teaching_target,
    CASE 
        WHEN t.id % 3 = 1 THEN '辅导学生数学成绩从70分提升到90分，掌握了解题技巧和思维方法。'
        WHEN t.id % 3 = 2 THEN '帮助学生提高英语口语和写作能力，期末考试成绩显著提升。'
        ELSE '负责学生课后作业辅导，培养良好的学习习惯和自主学习能力。'
    END as content,
    '2020-09-01' as start_date,
    '2021-06-30' as end_date,
    NOW() as create_time
FROM teacher_info t
WHERE t.status = '0' 
  AND t.id IN (
    SELECT id FROM (
      SELECT id FROM teacher_info WHERE status = '0' ORDER BY id LIMIT 3
    ) as temp
  )
  AND NOT EXISTS (
    SELECT 1 FROM teaching_experience e WHERE e.teacher_id = t.id
  );

-- 10. 最终验证数据
SELECT '=== 最终验证 ===' as info;
SELECT 
    t.id, t.real_name, t.birthday, 
    TIMESTAMPDIFF(YEAR, t.birthday, CURDATE()) as calculated_age,
    GROUP_CONCAT(DISTINCT s.subject_name) as subjects,
    COUNT(DISTINCT e.id) as education_count,
    COUNT(DISTINCT te.id) as experience_count
FROM teacher_info t
LEFT JOIN teacher_subject ts ON t.id = ts.teacher_id
LEFT JOIN course_subject s ON ts.subject_id = s.id
LEFT JOIN education_experience e ON t.id = e.teacher_id
LEFT JOIN teaching_experience te ON t.id = te.teacher_id
WHERE t.status = '0' AND t.certification_status = '4'
GROUP BY t.id
ORDER BY t.id
LIMIT 5;

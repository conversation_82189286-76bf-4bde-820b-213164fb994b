-- 修复teacher_membership表缺失的membership_date字段

-- 1. 检查当前表结构
SELECT '=== 当前teacher_membership表结构 ===' as info;
DESCRIBE teacher_membership;

-- 2. 检查membership_date字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'membership_date字段已存在'
        ELSE 'membership_date字段不存在，需要添加'
    END as membership_date_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership' 
  AND COLUMN_NAME = 'membership_date';

-- 3. 添加membership_date字段（如果不存在）
-- 使用动态SQL避免重复添加错误
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'teacher_membership' 
                   AND COLUMN_NAME = 'membership_date');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE teacher_membership ADD COLUMN membership_date datetime NOT NULL COMMENT ''成为会员日期'' AFTER membership_type',
              'SELECT ''membership_date字段已存在，跳过添加'' as info');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 验证字段添加结果
SELECT '=== 验证字段添加结果 ===' as info;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ membership_date字段存在'
        ELSE '❌ membership_date字段仍然不存在'
    END as membership_date_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'teacher_membership' 
  AND COLUMN_NAME = 'membership_date';

-- 5. 显示更新后的表结构
SELECT '=== 更新后的teacher_membership表结构 ===' as info;
DESCRIBE teacher_membership;

-- 6. 如果表中已有数据但membership_date为空，则更新为当前时间
UPDATE teacher_membership 
SET membership_date = create_time 
WHERE membership_date IS NULL AND create_time IS NOT NULL;

UPDATE teacher_membership 
SET membership_date = NOW() 
WHERE membership_date IS NULL;

SELECT '🎉 teacher_membership表字段修复完成！' as result;

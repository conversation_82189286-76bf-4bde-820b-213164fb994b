package com.ruoyi.education.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.education.service.IPlatformConfigService;

/**
 * 小程序端配置接口
 */
@RestController
@RequestMapping("/applet/config")
public class AppletConfigController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AppletConfigController.class);

    @Autowired
    private IPlatformConfigService platformConfigService;

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/value")
    public AjaxResult getConfigValue(@RequestParam String configKey) {
        try {
            String configValue = platformConfigService.selectConfigValueByKey(configKey);
            return AjaxResult.success(configValue);
        } catch (Exception e) {
            logger.error("获取配置失败", e);
            return AjaxResult.error("获取配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取筛选选项配置
     */
    @GetMapping("/filterOptions")
    public AjaxResult getFilterOptions() {
        try {
            Map<String, Object> filterOptions = new HashMap<>();

            // 获取年级选项
            String gradesConfig = platformConfigService.selectConfigValueByKey("filter.grades");
            filterOptions.put("grades", parseJsonConfig(gradesConfig));

            // 获取科目选项
            String subjectsConfig = platformConfigService.selectConfigValueByKey("filter.subjects");
            filterOptions.put("subjects", parseJsonConfig(subjectsConfig));

            // 获取城市选项
            String citiesConfig = platformConfigService.selectConfigValueByKey("filter.cities");
            filterOptions.put("cities", parseJsonConfig(citiesConfig));

            return AjaxResult.success(filterOptions);
        } catch (Exception e) {
            logger.error("获取筛选选项失败", e);
            return AjaxResult.error("获取筛选选项失败：" + e.getMessage());
        }
    }

    /**
     * 获取轮播图列表
     */
    @GetMapping("/banners")
    public AjaxResult getBanners(@RequestParam(value = "type", defaultValue = "teacher") String type) {
        try {
            String configKey = type + ".banners";
            String bannersConfig = platformConfigService.selectConfigValueByKey(configKey);
            if (StringUtils.hasText(bannersConfig)) {
                List<Object> banners = JSON.parseArray(bannersConfig);
                return AjaxResult.success(banners);
            }
            return AjaxResult.success(new ArrayList<>());
        } catch (Exception e) {
            logger.error("获取轮播图失败", e);
            return AjaxResult.error("获取轮播图失败：" + e.getMessage());
        }
    }

    /**
     * 获取公告列表
     */
    @GetMapping("/announcements")
    public AjaxResult getAnnouncements(@RequestParam(value = "type", defaultValue = "teacher") String type) {
        try {
            String configKey = type + ".announcements";
            String announcementConfig = platformConfigService.selectConfigValueByKey(configKey);
            if (StringUtils.hasText(announcementConfig)) {
                // 尝试解析为JSON数组（新格式）
                try {
                    List<Object> announcements = JSON.parseArray(announcementConfig);
                    return AjaxResult.success(announcements);
                } catch (Exception jsonException) {
                    // 如果JSON解析失败，尝试按行分割（兼容旧格式）
                    String[] lines = announcementConfig.split("\n");
                    List<Map<String, Object>> announcements = new ArrayList<>();
                    for (int i = 0; i < lines.length; i++) {
                        String line = lines[i].trim();
                        if (!line.isEmpty()) {
                            Map<String, Object> announcement = new HashMap<>();
                            announcement.put("id", i + 1);
                            announcement.put("title", line);
                            announcement.put("content", line);
                            announcement.put("sort", i + 1);
                            announcements.add(announcement);
                        }
                    }
                    return AjaxResult.success(announcements);
                }
            }
            return AjaxResult.success(new ArrayList<>());
        } catch (Exception e) {
            logger.error("获取公告失败", e);
            return AjaxResult.error("获取公告失败：" + e.getMessage());
        }
    }

    /**
     * 获取城市列表
     */
    @GetMapping("/cities")
    public AjaxResult getCities() {
        try {
            String citiesConfig = platformConfigService.selectConfigValueByKey("cities");
            if (StringUtils.hasText(citiesConfig)) {
                List<Object> cities = JSON.parseArray(citiesConfig);
                return AjaxResult.success(cities);
            }
            // 返回默认城市列表
            List<Map<String, String>> defaultCities = new ArrayList<>();
            defaultCities.add(createCityMap("上海", "shanghai"));
            defaultCities.add(createCityMap("北京", "beijing"));
            defaultCities.add(createCityMap("广州", "guangzhou"));
            defaultCities.add(createCityMap("深圳", "shenzhen"));
            defaultCities.add(createCityMap("杭州", "hangzhou"));
            defaultCities.add(createCityMap("南京", "nanjing"));
            defaultCities.add(createCityMap("苏州", "suzhou"));
            defaultCities.add(createCityMap("成都", "chengdu"));
            return AjaxResult.success(defaultCities);
        } catch (Exception e) {
            logger.error("获取城市列表失败", e);
            return AjaxResult.error("获取城市列表失败：" + e.getMessage());
        }
    }

    /**
     * 创建城市映射
     */
    private Map<String, String> createCityMap(String name, String value) {
        Map<String, String> city = new HashMap<>();
        city.put("name", name);
        city.put("value", value);
        return city;
    }

    /**
     * 解析JSON配置
     */
    private Object parseJsonConfig(String configValue) {
        if (configValue == null || configValue.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return JSON.parseArray(configValue);
        } catch (Exception e) {
            logger.warn("解析JSON配置失败: " + configValue, e);
            return new ArrayList<>();
        }
    }
}

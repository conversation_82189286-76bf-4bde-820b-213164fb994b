{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\education\\contentManagement\\components\\BannerManagement.vue?vue&type=style&index=0&id=0becdb78&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\education\\contentManagement\\components\\BannerManagement.vue", "mtime": 1753722814422}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\css-loader@3.6.0_webpack@4.47.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1746002926193}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1746002957864}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748798848719}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.47.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1746002959099}, {"path": "C:\\Users\\<USER>\\Desktop\\众桥辅导\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\.pnpm\\vue-loader@15.11.1_@vue+com_4755dba6f2870aee45d66f491d9e60c2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1746002957679}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmJhbm5lci1tYW5hZ2VtZW50IHsNCiAgcGFkZGluZzogMTZweCAwOw0KfQ0KDQouYmFubmVyLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmJhbm5lci1saXN0IHsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLmJhbm5lci1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouYmFubmVyLXByZXZpZXcgew0KICBoZWlnaHQ6IDEyMHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBiYWNrZ3JvdW5kOiAjZjVmNWY1Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KLmJhbm5lci1wcmV2aWV3IGltZyB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG9iamVjdC1maXQ6IGNvdmVyOw0KfQ0KDQouYmFubmVyLWluZm8gew0KICBwYWRkaW5nOiAxMnB4Ow0KfQ0KDQouYmFubmVyLXRpdGxlIHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiA0cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KfQ0KDQouYmFubmVyLXNvcnQgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTk5Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5iYW5uZXItYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogOHB4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQp9DQoNCi5iYW5uZXItYWN0aW9ucyAuZWwtYnV0dG9uIHsNCiAgcGFkZGluZzogNHB4IDhweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouZW1wdHktc3RhdGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHggMjBweDsNCiAgY29sb3I6ICM5OTk7DQp9DQoNCi5lbXB0eS1zdGF0ZSBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBkaXNwbGF5OiBibG9jazsNCn0NCg0KLmVtcHR5LXN0YXRlIHAgew0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5zYXZlLWFjdGlvbnMgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmctdG9wOiAxNnB4Ow0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLmZvcm0tdGlwcyB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM5OTk7DQogIG1hcmdpbi10b3A6IDRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLmltYWdlLXByZXZpZXcgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5pbWFnZS1wcmV2aWV3IGltZyB7DQogIG1heC13aWR0aDogMTAwJTsNCiAgbWF4LWhlaWdodDogMzAwcHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KOmRlZXAoLmVsLWNhcmRfX2JvZHkpIHsNCiAgcGFkZGluZzogMDsNCn0NCg0KOmRlZXAoLmVsLWZvcm0taXRlbSkgew0KICBtYXJnaW4tYm90dG9tOiAxOHB4Ow0KfQ0KDQovKiDkuIrkvKDnu4Tku7bmoLflvI8gKi8NCi51cGxvYWQtY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5iYW5uZXItdXBsb2FkZXIgOmRlZXAoLmVsLXVwbG9hZCkgew0KICBib3JkZXI6IDFweCBkYXNoZWQgI2Q5ZDlkOTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgd2lkdGg6IDIwMHB4Ow0KICBoZWlnaHQ6IDEwMHB4Ow0KICBkaXNwbGF5OiBibG9jazsNCiAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuM3M7DQp9DQoNCi5iYW5uZXItdXBsb2FkZXIgOmRlZXAoLmVsLXVwbG9hZDpob3Zlcikgew0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQp9DQoNCi51cGxvYWQtY29udGVudCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQoudXBsb2FkLXBsYWNlaG9sZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQp9DQoNCi5iYW5uZXItdXBsb2FkZXItaWNvbiB7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgY29sb3I6ICM4YzkzOWQ7DQogIG1hcmdpbi1ib3R0b206IDRweDsNCn0NCg0KLnVwbG9hZC10ZXh0IHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzhjOTM5ZDsNCn0NCg0KLmJhbm5lci1pbWFnZSB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG9iamVjdC1maXQ6IGNvdmVyOw0KICBkaXNwbGF5OiBibG9jazsNCn0NCg0KLnVwbG9hZC10aXBzIHsNCiAgbWFyZ2luLXRvcDogOHB4Ow0KfQ0KDQoudXBsb2FkLXRpcHMgcCB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM5OTk7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBsaW5lLWhlaWdodDogMS40Ow0KfQ0K"}, {"version": 3, "sources": ["BannerManagement.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgXA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "BannerManagement.vue", "sourceRoot": "src/views/education/contentManagement/components", "sourcesContent": ["<template>\r\n  <div class=\"banner-management\">\r\n    <div class=\"banner-header\">\r\n      <span>{{ tabName }}轮播图配置</span>\r\n      <el-button type=\"primary\" size=\"small\" @click=\"addBanner\">\r\n        <i class=\"el-icon-plus\"></i> 添加轮播图\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 轮播图列表 -->\r\n    <div class=\"banner-list\" v-if=\"localBanners.length > 0\">\r\n      <el-row :gutter=\"16\">\r\n        <el-col :span=\"8\" v-for=\"(banner, index) in localBanners\" :key=\"index\">\r\n          <el-card class=\"banner-item\" shadow=\"hover\">\r\n            <div class=\"banner-preview\">\r\n              <img :src=\"banner.image\" :alt=\"banner.title\" @error=\"handleImageError\" />\r\n            </div>\r\n            <div class=\"banner-info\">\r\n              <div class=\"banner-title\">{{ banner.title }}</div>\r\n              <div class=\"banner-sort\">排序: {{ banner.sort }}</div>\r\n              <div class=\"banner-actions\">\r\n                <el-button type=\"text\" size=\"small\" @click=\"editBanner(index)\">\r\n                  <i class=\"el-icon-edit\"></i> 编辑\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"small\" @click=\"deleteBanner(index)\" style=\"color: #f56c6c;\">\r\n                  <i class=\"el-icon-delete\"></i> 删除\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"small\" @click=\"moveBanner(index, 'up')\" :disabled=\"index === 0\">\r\n                  <i class=\"el-icon-top\"></i> 上移\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"small\" @click=\"moveBanner(index, 'down')\" :disabled=\"index === localBanners.length - 1\">\r\n                  <i class=\"el-icon-bottom\"></i> 下移\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 空状态 -->\r\n    <div v-else class=\"empty-state\">\r\n      <i class=\"el-icon-picture-outline\"></i>\r\n      <p>暂无轮播图配置</p>\r\n      <el-button type=\"primary\" @click=\"addBanner\">添加第一张轮播图</el-button>\r\n    </div>\r\n\r\n    <!-- 保存按钮 -->\r\n    <div class=\"save-actions\" v-if=\"localBanners.length > 0\">\r\n      <el-button type=\"primary\" @click=\"saveBanners\">保存轮播图配置</el-button>\r\n      <el-button @click=\"resetBanners\">重置</el-button>\r\n    </div>\r\n\r\n    <!-- 编辑轮播图对话框 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"bannerForm\" :model=\"currentBanner\" :rules=\"bannerRules\" label-width=\"100px\">\r\n        <el-form-item label=\"轮播图标题\" prop=\"title\">\r\n          <el-input v-model=\"currentBanner.title\" placeholder=\"请输入轮播图标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"图片上传\" prop=\"image\">\r\n          <div class=\"upload-container\">\r\n            <el-upload\r\n              class=\"banner-uploader\"\r\n              :action=\"uploadAction\"\r\n              :headers=\"uploadHeaders\"\r\n              :show-file-list=\"false\"\r\n              :on-success=\"handleUploadSuccess\"\r\n              :on-error=\"handleUploadError\"\r\n              :before-upload=\"beforeUpload\"\r\n              accept=\"image/*\"\r\n              :disabled=\"false\"\r\n            >\r\n              <div class=\"upload-content\">\r\n                <img v-if=\"currentBanner.image\" :src=\"currentBanner.image\" class=\"banner-image\" />\r\n                <div v-else class=\"upload-placeholder\">\r\n                  <i class=\"el-icon-plus banner-uploader-icon\"></i>\r\n                  <div class=\"upload-text\">点击上传图片</div>\r\n                </div>\r\n              </div>\r\n            </el-upload>\r\n            <div class=\"upload-tips\">\r\n              <p>建议图片尺寸：750x320 像素，格式：jpg、png，大小不超过2MB</p>\r\n              <el-input\r\n                v-model=\"currentBanner.image\"\r\n                placeholder=\"或直接输入图片URL地址\"\r\n                style=\"margin-top: 8px;\"\r\n              >\r\n                <template slot=\"append\">\r\n                  <el-button @click=\"previewImage\">预览</el-button>\r\n                </template>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"链接类型\" prop=\"linkType\">\r\n          <el-radio-group v-model=\"currentBanner.linkType\">\r\n            <el-radio label=\"none\">无链接</el-radio>\r\n            <el-radio label=\"page\">页面跳转</el-radio>\r\n            <el-radio label=\"web\">外部链接</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"currentBanner.linkType !== 'none'\"\r\n          label=\"链接地址\"\r\n          prop=\"linkUrl\"\r\n        >\r\n          <el-input\r\n            v-model=\"currentBanner.linkUrl\"\r\n            :placeholder=\"currentBanner.linkType === 'page' ? '请输入页面路径，如：/pages/teacher/certification/index' : '请输入完整的URL地址'\"\r\n          />\r\n          <div class=\"form-tips\">\r\n            {{ currentBanner.linkType === 'page' ? '小程序内页面路径' : '外部网页链接地址' }}\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序权重\" prop=\"sort\">\r\n          <el-input-number v-model=\"currentBanner.sort\" :min=\"1\" :max=\"99\" controls-position=\"right\" />\r\n          <div class=\"form-tips\">数字越小越靠前显示</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitBanner\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog title=\"图片预览\" :visible.sync=\"imagePreviewVisible\" width=\"400px\" append-to-body>\r\n      <div class=\"image-preview\">\r\n        <img :src=\"currentBanner.image\" alt=\"预览图片\" @error=\"handleImageError\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: 'BannerManagement',\r\n  props: {\r\n    banners: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    tabName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localBanners: [],\r\n      dialogVisible: false,\r\n      imagePreviewVisible: false,\r\n      dialogTitle: '',\r\n      currentBanner: {\r\n        title: '',\r\n        image: '',\r\n        linkType: 'none',\r\n        linkUrl: '',\r\n        sort: 1\r\n      },\r\n      // 上传配置\r\n      uploadAction: process.env.VUE_APP_BASE_API + \"/common/uploadSftp\",\r\n      editingIndex: -1,\r\n      bannerRules: {\r\n        title: [\r\n          { required: true, message: '请输入轮播图标题', trigger: 'blur' },\r\n          { min: 1, max: 50, message: '标题长度在 1 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        image: [\r\n          { required: true, message: '请上传图片或输入图片地址', trigger: 'blur' }\r\n        ],\r\n        linkUrl: [\r\n          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }\r\n        ],\r\n        sort: [\r\n          { required: true, message: '请输入排序权重', trigger: 'blur' },\r\n          { type: 'number', min: 1, max: 99, message: '排序权重在 1 到 99 之间', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    uploadHeaders() {\r\n      return {\r\n        Authorization: \"Bearer \" + this.getToken()\r\n      };\r\n    }\r\n  },\r\n  watch: {\r\n    banners: {\r\n      immediate: true,\r\n      handler(newVal) {\r\n        this.localBanners = JSON.parse(JSON.stringify(newVal || []));\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /** 添加轮播图 */\r\n    addBanner() {\r\n      this.dialogTitle = '添加轮播图';\r\n      this.currentBanner = {\r\n        title: '',\r\n        image: '',\r\n        linkType: 'none',\r\n        linkUrl: '',\r\n        sort: this.localBanners.length + 1\r\n      };\r\n      this.editingIndex = -1;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    /** 编辑轮播图 */\r\n    editBanner(index) {\r\n      this.dialogTitle = '编辑轮播图';\r\n      this.currentBanner = JSON.parse(JSON.stringify(this.localBanners[index]));\r\n      this.editingIndex = index;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    /** 删除轮播图 */\r\n    deleteBanner(index) {\r\n      this.$confirm('确认删除这张轮播图吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.localBanners.splice(index, 1);\r\n        this.updateSort();\r\n        this.$message.success('删除成功');\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 移动轮播图 */\r\n    moveBanner(index, direction) {\r\n      if (direction === 'up' && index > 0) {\r\n        // 使用Vue.set确保响应式更新\r\n        const temp = this.localBanners[index];\r\n        this.$set(this.localBanners, index, this.localBanners[index - 1]);\r\n        this.$set(this.localBanners, index - 1, temp);\r\n        this.$message.success('上移成功');\r\n      } else if (direction === 'down' && index < this.localBanners.length - 1) {\r\n        // 使用Vue.set确保响应式更新\r\n        const temp = this.localBanners[index];\r\n        this.$set(this.localBanners, index, this.localBanners[index + 1]);\r\n        this.$set(this.localBanners, index + 1, temp);\r\n        this.$message.success('下移成功');\r\n      }\r\n      this.updateSort();\r\n      // 强制更新视图\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    /** 更新排序 */\r\n    updateSort() {\r\n      this.localBanners.forEach((banner, index) => {\r\n        banner.sort = index + 1;\r\n      });\r\n    },\r\n\r\n    /** 提交轮播图 */\r\n    submitBanner() {\r\n      this.$refs.bannerForm.validate((valid) => {\r\n        if (valid) {\r\n          if (this.editingIndex >= 0) {\r\n            // 编辑模式\r\n            this.$set(this.localBanners, this.editingIndex, JSON.parse(JSON.stringify(this.currentBanner)));\r\n          } else {\r\n            // 新增模式\r\n            this.localBanners.push(JSON.parse(JSON.stringify(this.currentBanner)));\r\n          }\r\n          this.updateSort();\r\n          this.dialogVisible = false;\r\n          this.$message.success(this.editingIndex >= 0 ? '编辑成功' : '添加成功');\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 重置轮播图 */\r\n    resetBanners() {\r\n      this.$confirm('确认重置轮播图配置吗？所有未保存的修改将丢失。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.localBanners = JSON.parse(JSON.stringify(this.banners || []));\r\n        this.$message.success('重置成功');\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 保存轮播图 */\r\n    saveBanners() {\r\n      if (this.localBanners.length === 0) {\r\n        this.$message.warning('请至少添加一张轮播图');\r\n        return;\r\n      }\r\n      this.$emit('save', JSON.parse(JSON.stringify(this.localBanners)));\r\n    },\r\n\r\n    /** 预览图片 */\r\n    previewImage() {\r\n      if (!this.currentBanner.image) {\r\n        this.$message.warning('请先输入图片地址');\r\n        return;\r\n      }\r\n      this.imagePreviewVisible = true;\r\n    },\r\n\r\n    /** 图片加载错误处理 */\r\n    handleImageError(e) {\r\n      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkFGQUZBIi8+CjxwYXRoIGQ9Ik0yMCAzMEM5IDE5IDkgOSAyMCA5QzMxIDkgMzEgMTkgMjAgMzBaIiBmaWxsPSIjRTZFNkU2Ii8+Cjwvc3ZnPgo=';\r\n    },\r\n\r\n    /** 获取token */\r\n    getToken() {\r\n      return getToken();\r\n    },\r\n\r\n    /** 上传前检查 */\r\n    beforeUpload(file) {\r\n      console.log('开始上传文件:', file);\r\n      console.log('上传配置:', {\r\n        action: this.uploadAction,\r\n        headers: this.uploadHeaders\r\n      });\r\n\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt2M = file.size / 1024 / 1024 < 2;\r\n\r\n      if (!isImage) {\r\n        this.$message.error('只能上传图片文件!');\r\n        return false;\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 2MB!');\r\n        return false;\r\n      }\r\n\r\n      this.$message.info('正在上传图片，请稍候...');\r\n      return true;\r\n    },\r\n\r\n    /** 上传成功回调 */\r\n    handleUploadSuccess(response, file) {\r\n      console.log('SFTP上传成功响应:', response);\r\n      console.log('上传的文件:', file);\r\n\r\n      if (response.code === 200) {\r\n        // SFTP上传返回的是完整的URL\r\n        this.currentBanner.image = response.url;\r\n        this.$message.success('图片上传成功');\r\n      } else {\r\n        this.$message.error('图片上传失败：' + (response.msg || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 上传失败回调 */\r\n    handleUploadError(err, file) {\r\n      console.error('上传失败:', err);\r\n      console.log('失败的文件:', file);\r\n      this.$message.error('图片上传失败，请重试');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.banner-management {\r\n  padding: 16px 0;\r\n}\r\n\r\n.banner-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.banner-list {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.banner-item {\r\n  margin-bottom: 16px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.banner-preview {\r\n  height: 120px;\r\n  overflow: hidden;\r\n  background: #f5f5f5;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.banner-preview img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.banner-info {\r\n  padding: 12px;\r\n}\r\n\r\n.banner-title {\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.banner-sort {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.banner-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.banner-actions .el-button {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #999;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-state p {\r\n  margin-bottom: 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.save-actions {\r\n  text-align: center;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.form-tips {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-preview {\r\n  text-align: center;\r\n}\r\n\r\n.image-preview img {\r\n  max-width: 100%;\r\n  max-height: 300px;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.el-card__body) {\r\n  padding: 0;\r\n}\r\n\r\n:deep(.el-form-item) {\r\n  margin-bottom: 18px;\r\n}\r\n\r\n/* 上传组件样式 */\r\n.upload-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.banner-uploader :deep(.el-upload) {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  width: 200px;\r\n  height: 100px;\r\n  display: block;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.banner-uploader :deep(.el-upload:hover) {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.banner-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 12px;\r\n  color: #8c939d;\r\n}\r\n\r\n.banner-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.upload-tips {\r\n  margin-top: 8px;\r\n}\r\n\r\n.upload-tips p {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin: 0 0 8px 0;\r\n  line-height: 1.4;\r\n}\r\n</style> "]}]}
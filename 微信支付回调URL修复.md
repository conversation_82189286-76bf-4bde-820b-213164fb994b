# 微信支付回调URL修复指南

## 问题诊断

### 错误信息
```
com.github.binarywang.wxpay.exception.WxPayException: 必填字段【[notifyUrl]】必须提供值！
```

### 问题原因
1. **配置文件中的notifyUrl是相对路径**：`/payment/notify/certification-fee`
2. **微信支付需要完整的URL**：必须包含协议、域名、端口等完整信息
3. **wxPayService.getConfig().getNotifyUrl()返回null或空值**

## 修复方案

### 1. 后端修复 - 设置完整的回调URL

已修改 `AppletCertificationFeeController.createCertificationFeeOrder()` 方法：

```java
// 设置完整的回调URL
String notifyUrl = "http://***************:8080/applet/teacher/certification/notify";
request.setNotifyUrl(notifyUrl);
System.out.println("设置的回调URL: " + notifyUrl);
```

### 2. 前端修复 - 正确处理返回数据

已修改 `applet/pages/teacher/deposit/index.js`：

```javascript
if (res.code === 200) {
  // 后端使用AjaxResult.success().put()，数据直接在根级别
  const feeAmount = res.feeAmount || '0';
  const formattedAmount = parseFloat(feeAmount).toFixed(2);
  
  this.setData({
    feeAmount: formattedAmount,
    feePaidStatus: res.feePaidStatus || '0',
  });
}
```

## 测试步骤

### 步骤1：更新数据库配置
```sql
-- 执行修复认证费用显示问题.sql脚本
UPDATE platform_config 
SET config_value = '99.00', update_time = NOW()
WHERE config_key = 'teacher.certification.fee';
```

### 步骤2：清除配置缓存
```
GET /applet/teacher/certification/clear-cache
```

### 步骤3：测试认证费用显示
```
GET /applet/teacher/certification/fee-info
```

预期响应：
```json
{
  "code": 200,
  "msg": "操作成功",
  "feeAmount": "99.00",
  "feePaidStatus": "1"
}
```

### 步骤4：测试支付订单创建
```
POST /applet/teacher/certification/create-order
Headers: Authorization: Bearer {token}
```

预期响应：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "appId": "wxd8b4dd3890d705e4",
    "timeStamp": "1640000000",
    "nonceStr": "randomstring",
    "package": "prepay_id=wx123456789",
    "signType": "MD5",
    "paySign": "signature"
  }
}
```

### 步骤5：测试小程序页面
1. 重新编译小程序
2. 登录教师账号
3. 访问认证费用页面
4. 验证费用显示为 ¥99.00
5. 点击缴费按钮测试支付流程

## 回调URL说明

### 当前设置的回调URL
```
http://***************:8080/applet/teacher/certification/notify
```

### 回调处理逻辑
- 接收微信支付成功通知
- 解析支付结果
- 根据openid查找教师
- 更新教师认证费缴纳状态为"1"
- 返回成功响应给微信

### 回调接口代码
```java
@PostMapping("/notify")
public String handleCertificationFeeNotify(@RequestBody String xmlData) throws Exception {
    final WxPayOrderNotifyResult notifyResult = this.wxPayService.parseOrderNotifyResult(xmlData);
    
    String outTradeNo = notifyResult.getOutTradeNo();
    String openid = notifyResult.getOpenid();
    
    // 根据openid查找教师并更新缴费状态
    TeacherInfo teacherInfo = teacherInfoMapper.selectTeacherInfoByOpenid(openid);
    if (teacherInfo != null) {
        teacherInfo.setCertificationFeePaid("1");
        teacherInfoMapper.updateTeacherInfo(teacherInfo);
        
        logger.info("教师认证费缴费成功，教师ID: {}, 订单号: {}", teacherInfo.getId(), outTradeNo);
    }
    
    return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
}
```

## 预期结果

### 1. 认证费用正确显示
- 页面显示：¥99.00
- 缴费状态正确显示

### 2. 支付订单创建成功
- 无notifyUrl错误
- 返回微信支付参数
- 可以调起微信支付

### 3. 支付回调正常处理
- 接收微信支付通知
- 更新教师缴费状态
- 返回成功响应

### 4. 报名校验生效
- 未缴费教师报名被拦截
- 已缴费教师可以正常报名

## 故障排除

### 问题1：仍然显示notifyUrl错误
**解决**：检查服务器IP和端口是否正确，确保外网可访问

### 问题2：回调接口404
**解决**：确认回调URL路径正确，检查Controller映射

### 问题3：支付成功但状态未更新
**解决**：检查回调处理逻辑，确认openid匹配

### 问题4：前端显示0.00
**解决**：执行数据库更新脚本，清除配置缓存

现在系统应该能够正常创建支付订单并处理支付回调了！

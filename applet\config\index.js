// 小程序配置文件

// 开发环境配置
const development = {
  // 后端API地址 - 开发环境使用局域网IP，真机可以访问
  baseUrl: 'http://tb7e6638.natappfree.cc',
  // 是否开启调试模式
  debug: true,
  // 上传文件大小限制（MB）
  uploadMaxSize: 10
};

// 生产环境配置
const production = {
  // 后端API地址 - 生产环境使用域名，需要在微信公众平台配置白名单
  baseUrl: 'https://your-domain.com', // TODO: 替换为实际的生产环境域名
  // 是否开启调试模式
  debug: false,
  // 上传文件大小限制（MB）
  uploadMaxSize: 10
};

// 根据环境变量选择配置
// 小程序中可以通过 __wxConfig 或其他方式判断环境
// 这里简化处理，默认使用开发环境
const config = development;

// 导出配置
module.exports = config;

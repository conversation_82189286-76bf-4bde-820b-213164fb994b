const { request } = require('../utils/request.js');

/**
 * 家长端API接口
 */

// 获取家长端轮播图列表（从内容管理获取）
function getBannerList() {
  return request({
    url: '/applet/config/banners',
    method: 'get',
    data: { type: 'parent' }
  });
}

// 获取家长端通知公告列表（从内容管理获取）
function getAnnouncementList() {
  return request({
    url: '/applet/config/announcements',
    method: 'get',
    data: { type: 'parent' }
  });
}

// 获取家长统计数据
function getParentStats() {
  return request({
    url: '/applet/parent/stats',
    method: 'get'
  });
}

// 获取家长最近订单
function getRecentOrders() {
  return request({
    url: '/applet/parent/orders/recent',
    method: 'get'
  });
}

// 获取推荐教师
function getRecommendTeachers(params = {}) {
  return request({
    url: '/applet/parent/teachers/recommend',
    method: 'get',
    data: {
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 5
    }
  });
}

// 创建家教订单
function createOrder(orderData) {
  return request({
    url: '/applet/parent/orders/create',
    method: 'post',
    data: orderData
  });
}

// 获取家长个人信息
function getParentProfile() {
  return request({
    url: '/applet/parent/profile',
    method: 'get'
  });
}

// 更新家长个人信息（仅姓名和头像）
function updateParentProfile(profileData) {
  return request({
    url: '/applet/parent/profile',
    method: 'put',
    data: profileData
  });
}

// 获取家长发布的所有订单列表
function getAllOrders(params = {}) {
  return request({
    url: '/applet/parent/orders/all',
    method: 'get',
    data: params
  });
}

// 获取家长进行中的订单列表
function getOngoingOrders(params = {}) {
  return request({
    url: '/applet/parent/orders/ongoing',
    method: 'get',
    data: params
  });
}

// 获取家长已完成的订单列表
function getCompletedOrders(params = {}) {
  return request({
    url: '/applet/parent/orders/completed',
    method: 'get',
    data: params
  });
}

// 获取订单的报名列表
function getOrderApplications(orderId) {
  return request({
    url: `/applet/parent/myOrder/${orderId}/applications`,
    method: 'get'
  });
}

// 发布家教订单
function publishOrder(orderData) {
  return request({
    url: '/applet/parent/publishOrder',
    method: 'post',
    data: orderData
  });
}

module.exports = {
  getBannerList,
  getAnnouncementList,
  getParentStats,
  getRecentOrders,
  getRecommendTeachers,
  createOrder,
  getParentProfile,
  updateParentProfile,
  getAllOrders,
  getOngoingOrders,
  getCompletedOrders,
  getOrderApplications,
  publishOrder
};

-- 插入测试教师数据
-- 用于测试推荐教师功能

-- 插入测试教师1
INSERT INTO teacher_info (
    openid, username, nick_name, real_name, phone_number, gender, age, 
    university, major, grade, self_introduction, certification_status, 
    status, rating, create_time, update_time
) VALUES (
    'test_openid_1', 'teacher1', '唐老师', '唐某某', '13800138001', '1', 25,
    '湘潭大学', '本科生', '大三',
    '本人性格开朗、乐观向上、爱与人沟通，具有较强的组织协调能力和团队合作精神。在教学工作中，本人认真负责，有耐心，善于引导学生思考。',
    '2', '0', 4.8, NOW(), NOW()
);

-- 插入测试教师2
INSERT INTO teacher_info (
    openid, username, nick_name, real_name, phone_number, gender, age, 
    university, major, grade, self_introduction, certification_status, 
    status, rating, create_time, update_time
) VALUES (
    'test_openid_2', 'teacher2', '董老师', '董某某', '13800138002', '0', 23,
    '长沙理工大学', '本科生', '大二',
    '有耐心，善于掌控全局，学习生活长期担任班干部，有条不紊，上课有条理，书面分明，有参分明。',
    '2', '0', 4.6, NOW(), NOW()
);

-- 插入测试教师3
INSERT INTO teacher_info (
    openid, username, nick_name, real_name, phone_number, gender, age, 
    university, major, grade, self_introduction, certification_status, 
    status, rating, create_time, update_time
) VALUES (
    'test_openid_3', 'teacher3', '王老师', '王某某', '13800138003', '1', 24,
    '长沙学院', '本科生', '大四',
    '性格开朗有耐心，擅长引导学生思考，有丰富的家教经验，能够根据学生特点制定个性化教学方案。',
    '2', '0', 4.9, NOW(), NOW()
);

-- 插入测试教师4
INSERT INTO teacher_info (
    openid, username, nick_name, real_name, phone_number, gender, age, 
    university, major, grade, self_introduction, certification_status, 
    status, rating, create_time, update_time
) VALUES (
    'test_openid_4', 'teacher4', '贾老师', '贾某某', '13800138004', '1', 22,
    '长沙学院', '本科生', '大二',
    '性格开朗有耐心，善于与学生沟通，能够发现学生的学习问题并及时解决，有较强的责任心。',
    '2', '0', 4.7, NOW(), NOW()
);

-- 为测试教师添加科目关联（假设科目表中已有数据）
-- 如果subject表中没有数据，需要先插入科目数据

-- 检查是否有科目数据
-- SELECT * FROM subject LIMIT 5;

-- 如果没有科目数据，先插入一些基础科目
INSERT IGNORE INTO subject (id, subject_name, create_time, update_time) VALUES
(1, '数学', NOW(), NOW()),
(2, '英语', NOW(), NOW()),
(3, '语文', NOW(), NOW()),
(4, '物理', NOW(), NOW()),
(5, '化学', NOW(), NOW()),
(6, '生物', NOW(), NOW()),
(7, '历史', NOW(), NOW()),
(8, '地理', NOW(), NOW()),
(9, '政治', NOW(), NOW());

-- 为教师分配科目（需要先获取教师ID）
-- 这部分需要根据实际插入的教师ID来调整

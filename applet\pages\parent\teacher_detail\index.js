// 导入API
const parentApi = require('../../../api/parent.js');

Page({
  data: {
    teacherId: null,
    teacherDetail: {},
    loading: true
  },

  onLoad(options) {
    console.log('教师详情页面加载，参数:', options);
    
    if (options.teacherId) {
      this.setData({
        teacherId: options.teacherId
      });
      this.loadTeacherDetail();
    } else {
      wx.showToast({
        title: '教师ID缺失',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载教师详情
  async loadTeacherDetail() {
    try {
      console.log('开始加载教师详情，ID:', this.data.teacherId);
      
      this.setData({ loading: true });
      
      const res = await parentApi.getTeacherDetail(this.data.teacherId);
      console.log('教师详情API响应:', res);
      
      if (res.code === 200) {
        const teacherDetail = res.data || {};
        console.log('教师详情数据:', teacherDetail);

        // 处理数据，确保所有字段都有默认值
        const processedDetail = {
          ...teacherDetail,
          avatar: teacherDetail.avatar || '/assets/images/default_avatar.png',
          display_name: teacherDetail.display_name || teacherDetail.nick_name || '未知老师',
          age: teacherDetail.age || '未知',
          gender_text: teacherDetail.gender_text || '未知',
          university: teacherDetail.university || '未填写',
          major: teacherDetail.major || '未填写',
          grade: teacherDetail.grade || '未填写',
          teaching_subjects: teacherDetail.teaching_subjects || '暂未设置',
          education_background: teacherDetail.education_background || '',
          experience_background: teacherDetail.experience_background || '',
          self_introduction: teacherDetail.self_introduction || '',
          total_orders: teacherDetail.total_orders || 0,
          success_orders: teacherDetail.success_orders || 0,
          certification_text: teacherDetail.certification_text || '未认证',
          certification_status: teacherDetail.certification_status || '0'
        };

        // 处理接课成功经历数据
        let successExperienceList = [];
        if (teacherDetail.success_experience) {
          try {
            successExperienceList = JSON.parse(teacherDetail.success_experience);
            // 格式化时间显示
            successExperienceList = successExperienceList.map(item => ({
              ...item,
              completedTime: this.formatTime(item.completedTime)
            }));
          } catch (e) {
            console.error('解析成功经历数据失败:', e);
            successExperienceList = [];
          }
        }

        // 处理教育背景数据
        let educationList = [];
        if (teacherDetail.education_background) {
          const educationItems = teacherDetail.education_background.split(';');
          educationList = educationItems.map(item => {
            const trimmedItem = item.trim();
            if (!trimmedItem) return null;

            // 解析格式：学校-学历-专业(时间)
            const match = trimmedItem.match(/^(.+?)-(.+?)-(.+?)(?:\((.+?)\))?$/);
            if (match) {
              return {
                school: match[1],
                degree: match[2],
                major: match[3],
                timeRange: match[4] || ''
              };
            }
            return {
              school: trimmedItem,
              degree: '',
              major: '',
              timeRange: ''
            };
          }).filter(item => item !== null);
        }

        // 处理教学经历数据
        let experienceList = [];
        if (teacherDetail.experience_background) {
          const experienceItems = teacherDetail.experience_background.split(';');
          experienceList = experienceItems.map(item => {
            const trimmedItem = item.trim();
            if (!trimmedItem) return null;

            // 解析格式：教学对象(时间): 内容
            const match = trimmedItem.match(/^(.+?)(?:\((.+?)\))?(?::\s*(.+))?$/);
            if (match) {
              return {
                teachingTarget: match[1],
                timeRange: match[2] || '',
                content: match[3] || ''
              };
            }
            return {
              teachingTarget: trimmedItem,
              timeRange: '',
              content: ''
            };
          }).filter(item => item !== null);
        }

        this.setData({
          teacherDetail: processedDetail,
          successExperienceList: successExperienceList,
          educationList: educationList,
          experienceList: experienceList,
          loading: false
        });
      } else {
        console.error('获取教师详情失败:', res);
        wx.showToast({
          title: res.msg || '获取教师详情失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载教师详情异常:', error);
      wx.showToast({
        title: '网络错误，请稍后重试',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  // 预约教师
  bookTeacher() {
    console.log('预约教师，ID:', this.data.teacherId);
    
    const teacherDetail = this.data.teacherDetail;
    
    // 跳转到发布家教页面，并传递教师信息
    wx.navigateTo({
      url: `/pages/parent/publish/index?preferredTeacherId=${this.data.teacherId}&preferredTeacherName=${encodeURIComponent(teacherDetail.display_name || '')}`
    });
  },

  // 分享功能
  onShareAppMessage() {
    const teacherDetail = this.data.teacherDetail;
    return {
      title: `推荐${teacherDetail.display_name || '优秀教师'}`,
      path: `/pages/parent/teacher_detail/index?teacherId=${this.data.teacherId}`,
      imageUrl: teacherDetail.avatar || '/assets/images/default_avatar.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const teacherDetail = this.data.teacherDetail;
    return {
      title: `推荐${teacherDetail.display_name || '优秀教师'}`,
      imageUrl: teacherDetail.avatar || '/assets/images/default_avatar.png'
    };
  },

  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return '';

    try {
      const date = new Date(timeStr);
      const now = new Date();
      const diff = now - date;
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (days === 0) {
        return '今天';
      } else if (days === 1) {
        return '昨天';
      } else if (days < 7) {
        return `${days}天前`;
      } else if (days < 30) {
        const weeks = Math.floor(days / 7);
        return `${weeks}周前`;
      } else if (days < 365) {
        const months = Math.floor(days / 30);
        return `${months}个月前`;
      } else {
        const years = Math.floor(days / 365);
        return `${years}年前`;
      }
    } catch (e) {
      return timeStr;
    }
  }
});

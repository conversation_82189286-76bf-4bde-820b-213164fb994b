// 导入API
import { parentApi } from '../../../api/parent.js';

Page({
  data: {
    teacherId: null,
    teacherDetail: {},
    loading: true
  },

  onLoad(options) {
    console.log('教师详情页面加载，参数:', options);
    
    if (options.teacherId) {
      this.setData({
        teacherId: options.teacherId
      });
      this.loadTeacherDetail();
    } else {
      wx.showToast({
        title: '教师ID缺失',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载教师详情
  async loadTeacherDetail() {
    try {
      console.log('开始加载教师详情，ID:', this.data.teacherId);
      
      this.setData({ loading: true });
      
      const res = await parentApi.getTeacherDetail(this.data.teacherId);
      console.log('教师详情API响应:', res);
      
      if (res.code === 200) {
        const teacherDetail = res.data || {};
        console.log('教师详情数据:', teacherDetail);
        
        this.setData({
          teacherDetail: teacherDetail,
          loading: false
        });
        
        // 设置页面标题
        if (teacherDetail.display_name) {
          wx.setNavigationBarTitle({
            title: teacherDetail.display_name + '的详情'
          });
        }
      } else {
        console.error('获取教师详情失败:', res);
        wx.showToast({
          title: res.msg || '获取教师详情失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载教师详情异常:', error);
      wx.showToast({
        title: '网络错误，请稍后重试',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  // 预约教师
  bookTeacher() {
    console.log('预约教师，ID:', this.data.teacherId);
    
    const teacherDetail = this.data.teacherDetail;
    
    // 跳转到发布家教页面，并传递教师信息
    wx.navigateTo({
      url: `/pages/parent/publish/index?preferredTeacherId=${this.data.teacherId}&preferredTeacherName=${encodeURIComponent(teacherDetail.display_name || '')}`
    });
  },

  // 分享功能
  onShareAppMessage() {
    const teacherDetail = this.data.teacherDetail;
    return {
      title: `推荐${teacherDetail.display_name || '优秀教师'}`,
      path: `/pages/parent/teacher_detail/index?teacherId=${this.data.teacherId}`,
      imageUrl: teacherDetail.avatar || '/assets/images/default_avatar.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const teacherDetail = this.data.teacherDetail;
    return {
      title: `推荐${teacherDetail.display_name || '优秀教师'}`,
      imageUrl: teacherDetail.avatar || '/assets/images/default_avatar.png'
    };
  }
});

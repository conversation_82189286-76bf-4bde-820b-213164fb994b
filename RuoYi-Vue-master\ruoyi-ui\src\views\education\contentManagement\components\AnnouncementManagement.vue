<template>
  <div class="announcement-management">
    <div class="announcement-header">
      <span>{{ tabName }}公告管理</span>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="addAnnouncement">
          <i class="el-icon-plus"></i> 添加公告
        </el-button>
        <el-button type="success" size="small" @click="saveAnnouncements" :disabled="localAnnouncements.length === 0">
          <i class="el-icon-check"></i> 保存公告
        </el-button>
        <el-button type="info" size="small" @click="resetAnnouncements">
          <i class="el-icon-refresh"></i> 重置
        </el-button>
      </div>
    </div>

    <!-- 公告列表 -->
    <div class="announcement-list" v-if="localAnnouncements.length > 0">
      <el-table :data="localAnnouncements" border stripe>
        <el-table-column prop="title" label="公告标题" min-width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="公告内容" min-width="300">
          <template slot-scope="scope">
            <div class="content-preview" v-html="getContentPreview(scope.row.content)"></div>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.sort }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editAnnouncement(scope.$index)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button type="text" size="small" @click="deleteAnnouncement(scope.$index)" style="color: #f56c6c;">
              <i class="el-icon-delete"></i> 删除
            </el-button>
            <el-button type="text" size="small" @click="moveAnnouncement(scope.$index, 'up')" :disabled="scope.$index === 0">
              <i class="el-icon-top"></i> 上移
            </el-button>
            <el-button type="text" size="small" @click="moveAnnouncement(scope.$index, 'down')" :disabled="scope.$index === localAnnouncements.length - 1">
              <i class="el-icon-bottom"></i> 下移
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="el-icon-chat-line-round"></i>
      <p>暂无公告配置</p>
      <el-button type="primary" @click="addAnnouncement">添加第一条公告</el-button>
    </div>

    <!-- 添加/编辑公告对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form ref="announcementForm" :model="currentAnnouncement" :rules="announcementRules" label-width="80px">
        <el-form-item label="公告标题" prop="title">
          <el-input 
            v-model="currentAnnouncement.title" 
            placeholder="请输入公告标题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <editor
            v-model="currentAnnouncement.content"
            :min-height="200"
            :height="300"
            placeholder="请输入公告详细内容，支持富文本格式"
          />
          <div class="form-tips">支持富文本格式：粗体、斜体、颜色、链接等</div>
        </el-form-item>
        <el-form-item label="排序权重" prop="sort">
          <el-input-number 
            v-model="currentAnnouncement.sort" 
            :min="1" 
            :max="99" 
            controls-position="right"
            style="width: 150px"
          />
          <div class="form-tips">数字越小越靠前显示</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAnnouncement">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Editor from "@/components/Editor";

export default {
  name: 'AnnouncementManagement',
  components: {
    Editor
  },
  props: {
    announcements: {
      type: Array,
      default: () => []
    },
    tabName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localAnnouncements: [],
      dialogVisible: false,
      dialogTitle: '',
      currentAnnouncement: {
        title: '',
        content: '',
        sort: 1
      },
      editingIndex: -1,
      announcementRules: {
        title: [
          { required: true, message: '请输入公告标题', trigger: 'blur' },
          { min: 1, max: 50, message: '标题长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入公告内容', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序权重', trigger: 'blur' },
          { type: 'number', min: 1, max: 99, message: '排序权重在 1 到 99 之间', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    announcements: {
      immediate: true,
      handler(newVal) {
        this.localAnnouncements = JSON.parse(JSON.stringify(newVal || []));
      }
    }
  },
  methods: {
    /** 添加公告 */
    addAnnouncement() {
      this.dialogTitle = '添加公告';
      this.currentAnnouncement = {
        title: '',
        content: '',
        sort: this.localAnnouncements.length + 1
      };
      this.editingIndex = -1;
      this.dialogVisible = true;
    },

    /** 编辑公告 */
    editAnnouncement(index) {
      this.dialogTitle = '编辑公告';
      this.currentAnnouncement = JSON.parse(JSON.stringify(this.localAnnouncements[index]));
      this.editingIndex = index;
      this.dialogVisible = true;
    },

    /** 删除公告 */
    deleteAnnouncement(index) {
      this.$confirm('确认删除这条公告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.localAnnouncements.splice(index, 1);
        this.updateSort();
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    /** 移动公告 */
    moveAnnouncement(index, direction) {
      if (direction === 'up' && index > 0) {
        // 使用Vue.set确保响应式更新
        const temp = this.localAnnouncements[index];
        this.$set(this.localAnnouncements, index, this.localAnnouncements[index - 1]);
        this.$set(this.localAnnouncements, index - 1, temp);
        this.$message.success('上移成功');
      } else if (direction === 'down' && index < this.localAnnouncements.length - 1) {
        // 使用Vue.set确保响应式更新
        const temp = this.localAnnouncements[index];
        this.$set(this.localAnnouncements, index, this.localAnnouncements[index + 1]);
        this.$set(this.localAnnouncements, index + 1, temp);
        this.$message.success('下移成功');
      }
      this.updateSort();
      // 强制更新视图
      this.$forceUpdate();
    },

    /** 更新排序 */
    updateSort() {
      this.localAnnouncements.forEach((announcement, index) => {
        announcement.sort = index + 1;
      });
    },

    /** 提交公告 */
    submitAnnouncement() {
      this.$refs.announcementForm.validate((valid) => {
        if (valid) {
          // 检查标题是否重复
          const existingIndex = this.localAnnouncements.findIndex((announcement, index) => 
            announcement.title === this.currentAnnouncement.title && index !== this.editingIndex
          );
          
          if (existingIndex >= 0) {
            this.$message.error('公告标题已存在，请使用不同的标题');
            return;
          }

          if (this.editingIndex >= 0) {
            // 编辑模式
            this.$set(this.localAnnouncements, this.editingIndex, JSON.parse(JSON.stringify(this.currentAnnouncement)));
          } else {
            // 新增模式
            this.localAnnouncements.push(JSON.parse(JSON.stringify(this.currentAnnouncement)));
          }
          this.updateSort();
          this.dialogVisible = false;
          this.$message.success(this.editingIndex >= 0 ? '编辑成功' : '添加成功');
        }
      });
    },

    /** 重置公告 */
    resetAnnouncements() {
      this.$confirm('确认重置公告配置吗？所有未保存的修改将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.localAnnouncements = JSON.parse(JSON.stringify(this.announcements || []));
        this.$message.success('重置成功');
      }).catch(() => {});
    },

    /** 保存公告 */
    saveAnnouncements() {
      if (this.localAnnouncements.length === 0) {
        this.$message.warning('请至少添加一条公告');
        return;
      }
      this.$emit('save', JSON.parse(JSON.stringify(this.localAnnouncements)));
    },

    /** 获取内容预览 */
    getContentPreview(content) {
      if (!content) return '';

      // 移除HTML标签，只显示纯文本预览
      const textContent = content.replace(/<[^>]*>/g, '');

      // 限制预览长度
      if (textContent.length > 100) {
        return textContent.substring(0, 100) + '...';
      }

      return textContent;
    }
  }
};
</script>

<style scoped>
.announcement-management {
  padding: 16px 0;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.announcement-list {
  margin-bottom: 24px;
}

.content-preview {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  color: #606266;
}

.content-preview :deep(p) {
  margin: 0;
  display: inline;
}

.content-preview :deep(br) {
  display: none;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-state p {
  margin-bottom: 16px;
  font-size: 14px;
}

.form-tips {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}
</style>

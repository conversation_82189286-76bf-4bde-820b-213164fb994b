-- 认证费用配置SQL
-- 确保系统配置表中有认证费用配置项

-- 检查是否已存在认证费用配置（系统配置表）
SELECT * FROM sys_config WHERE config_key = 'teacher.certification.fee';

-- 如果不存在，则插入认证费用配置到系统配置表
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
SELECT '教师认证费用', 'teacher.certification.fee', '99.00', 'Y', 'admin', NOW(), 'admin', NOW(), '教师进行身份认证需要缴纳的费用金额'
WHERE NOT EXISTS (
    SELECT 1 FROM sys_config WHERE config_key = 'teacher.certification.fee'
);

-- 如果已存在但值为0，则更新为99.00
UPDATE sys_config
SET config_value = '99.00',
    update_time = NOW(),
    update_by = 'admin'
WHERE config_key = 'teacher.certification.fee'
  AND (config_value = '0' OR config_value = '0.00' OR config_value IS NULL OR config_value = '');

-- 验证配置是否正确
SELECT
    config_key,
    config_value,
    config_name,
    remark,
    create_time,
    update_time
FROM sys_config
WHERE config_key = 'teacher.certification.fee';

-- 查看所有相关配置
SELECT
    config_key,
    config_value,
    config_name
FROM sys_config
WHERE config_key LIKE '%teacher%' OR config_key LIKE '%certification%'
ORDER BY config_key;

-- 同时检查平台配置表（如果存在的话）
SELECT
    config_key,
    config_value,
    config_name
FROM platform_config
WHERE config_key LIKE '%teacher%' OR config_key LIKE '%certification%'
ORDER BY config_key;

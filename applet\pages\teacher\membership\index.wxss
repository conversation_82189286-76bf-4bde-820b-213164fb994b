/* 会员页面样式 - 简约高级风格 */
.membership-page {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6b7280;
  font-size: 28rpx;
  font-weight: 500;
}

/* 主要内容 */
.main-content {
  padding: 40rpx 24rpx;
}

/* 会员状态卡片 */
.membership-card {
  position: relative;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.vip-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.normal-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-pattern {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.card-content {
  position: relative;
  padding: 48rpx 32rpx;
  z-index: 1;
}

.status-section {
  margin-bottom: 40rpx;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 50rpx;
  margin-bottom: 20rpx;
}

.vip-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
}

.normal-badge {
  background: rgba(99, 102, 241, 0.1);
  border: 1rpx solid rgba(99, 102, 241, 0.2);
}

.badge-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.badge-text {
  font-size: 28rpx;
  font-weight: 600;
}

.vip-card .badge-text {
  color: white;
}

.normal-card .badge-text {
  color: #6366f1;
}

.status-desc {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.vip-card .status-desc {
  color: white;
}

.normal-card .status-desc {
  color: #374151;
}

.member-since {
  font-size: 24rpx;
  opacity: 0.8;
}

.vip-card .member-since {
  color: rgba(255, 255, 255, 0.8);
}

.normal-card .member-since {
  color: #6b7280;
}

/* 操作区域 */
.action-section {
  display: flex;
  justify-content: center;
}

.upgrade-button {
  color: #000;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200rpx;
}

.upgrade-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.3);
}

.button-text {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.button-price {
  font-size: 36rpx;
  font-weight: 700;
}

.vip-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.vip-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.vip-message {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.vip-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 500;
}

.upgrade-btn:active {
  transform: scale(0.95);
}

/* 区域标题 */
.section-header {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  text-align: center;
}

/* 权益展示 */
.benefits-section {
  margin-bottom: 10rpx;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.benefit-card {
  display: flex;
  background: white;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.benefit-card.locked {
  opacity: 0.6;
}

.benefit-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.vip-benefit {
  border: 2rpx solid #e0e7ff;
  margin-bottom:20rpx
}

.normal-benefit {
  border: 2rpx solid #f3f4f6;
}

.benefit-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.benefit-icon-wrapper {
  position: relative;
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 16rpx;
}

.benefit-icon {
  font-size: 32rpx;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon {
  font-size: 24rpx;
  color: white;
}

.benefit-tag {
  font-size: 20rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.vip-tag {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.free-tag {
  background: #e5e7eb;
  color: #6b7280;
}

.benefit-info {
  text-align: left;
  flex:1
}

.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.benefit-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* 升级说明 */
.upgrade-info {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  border: 1rpx solid #e2e8f0;
}

.info-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.info-content {
  space-y: 16rpx;
}

.info-point {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.info-point:last-child {
  margin-bottom: 0;
}

.point-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  width: 40rpx;
  text-align: center;
}

.point-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.4;
  flex: 1;
}

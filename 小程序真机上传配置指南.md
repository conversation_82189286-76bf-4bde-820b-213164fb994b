# 小程序真机上传功能配置指南

## 问题原因

小程序在真机上上传失败的主要原因：

1. **网络地址问题**：真机无法访问 `localhost` 地址
2. **域名白名单**：微信小程序要求所有网络请求的域名都必须在白名单中
3. **HTTPS要求**：生产环境必须使用HTTPS协议
4. **网络环境**：真机和开发服务器需要在同一网络环境中

## 解决方案

### 1. 开发环境配置

#### 步骤1：确保网络连通性
- 确保手机和开发电脑在同一局域网内
- 使用电脑的局域网IP地址替代localhost
- 当前配置：`http://*************:8080`

#### 步骤2：配置微信开发者工具
1. 打开微信开发者工具
2. 点击右上角"详情"
3. 在"本地设置"中勾选：
   - ✅ 不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书
   - ✅ 开启调试模式

#### 步骤3：测试网络连接
使用新增的网络检测功能：
```javascript
const NetworkUtils = require('./utils/network.js');

// 检查上传环境
NetworkUtils.checkUploadEnvironment()
  .then(result => {
    console.log('环境检查通过:', result);
  })
  .catch(error => {
    console.error('环境检查失败:', error);
  });
```

### 2. 生产环境配置

#### 步骤1：域名配置
1. 登录微信公众平台 (mp.weixin.qq.com)
2. 进入"开发" -> "开发管理" -> "开发设置"
3. 在"服务器域名"中配置：
   - **request合法域名**：`https://your-domain.com`
   - **uploadFile合法域名**：`https://your-domain.com`
   - **downloadFile合法域名**：`https://your-domain.com`

#### 步骤2：HTTPS证书配置
- 确保服务器配置了有效的SSL证书
- 证书必须是受信任的CA机构颁发
- 支持TLS 1.2及以上版本

#### 步骤3：更新配置文件
修改 `applet/config/index.js`：
```javascript
const production = {
  baseUrl: 'https://your-domain.com', // 替换为实际域名
  debug: false,
  uploadMaxSize: 10
};
```

### 3. 调试方法

#### 方法1：使用控制台日志
上传功能已增加详细日志，可以在微信开发者工具控制台查看：
- 上传URL和参数
- 网络请求状态
- 服务器响应内容
- 错误详细信息

#### 方法2：网络状态检查
```javascript
// 检查网络状态
NetworkUtils.checkNetworkStatus()
  .then(networkType => {
    console.log('网络类型:', networkType);
  });

// 测试服务器连接
NetworkUtils.testServerConnection()
  .then(() => {
    console.log('服务器连接正常');
  })
  .catch(error => {
    console.error('服务器连接失败:', error);
  });
```

#### 方法3：文件信息检查
```javascript
// 检查文件大小和格式
NetworkUtils.getFileInfo(filePath)
  .then(fileInfo => {
    console.log('文件信息:', fileInfo);
  });
```

### 4. 常见错误及解决方案

#### 错误1：`request:fail url not in domain list`
**原因**：域名未在微信公众平台配置白名单
**解决**：在微信公众平台添加域名到白名单

#### 错误2：`request:fail ssl hand shake error`
**原因**：SSL证书问题
**解决**：检查服务器SSL证书配置

#### 错误3：`request:fail timeout`
**原因**：网络超时
**解决**：检查网络连接，增加超时时间

#### 错误4：`request:fail -2:net::ERR_NAME_NOT_RESOLVED`
**原因**：域名解析失败
**解决**：检查域名配置和DNS设置

### 5. 最佳实践

1. **开发阶段**：
   - 使用局域网IP地址
   - 开启"不校验合法域名"选项
   - 启用详细日志调试

2. **测试阶段**：
   - 使用测试域名
   - 配置测试环境的域名白名单
   - 进行真机测试

3. **生产阶段**：
   - 使用正式域名和HTTPS
   - 配置生产环境域名白名单
   - 关闭调试模式

### 6. 配置检查清单

- [ ] 网络连通性（手机能访问开发服务器）
- [ ] IP地址配置正确
- [ ] 微信开发者工具设置正确
- [ ] 域名白名单配置（生产环境）
- [ ] SSL证书配置（生产环境）
- [ ] 上传接口正常工作
- [ ] 文件大小限制合理
- [ ] 错误处理完善

## 联系支持

如果按照以上步骤仍然无法解决问题，请提供：
1. 错误日志截图
2. 网络环境描述
3. 微信开发者工具版本
4. 手机型号和微信版本

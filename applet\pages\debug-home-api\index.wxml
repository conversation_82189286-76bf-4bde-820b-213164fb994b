<view class="container">
  <view class="header">
    <text class="title">首页API调试</text>
    <button class="retry-btn" bindtap="retryTest" disabled="{{isLoading}}">
      {{isLoading ? '测试中...' : '重新测试'}}
    </button>
  </view>

  <view class="test-results">
    <view 
      class="test-item {{item.status}}"
      wx:for="{{testResults}}"
      wx:key="name"
    >
      <view class="test-header">
        <text class="test-name">{{item.name}}</text>
        <text class="test-status">{{item.status === 'success' ? '✅' : '❌'}}</text>
      </view>
      
      <view class="test-details">
        <text class="detail-item">状态码: {{item.code}}</text>
        <text class="detail-item">消息: {{item.message}}</text>
        
        <view class="data-section" wx:if="{{item.data}}">
          <text class="data-title">返回数据:</text>
          <view class="data-content">
            <text class="data-text">{{item.data}}</text>
          </view>
        </view>
      </view>
      
      <button 
        class="copy-btn" 
        bindtap="copyResult" 
        data-index="{{index}}"
        size="mini"
      >
        复制结果
      </button>
    </view>
  </view>

  <view class="loading" wx:if="{{isLoading}}">
    <text>正在测试API接口...</text>
  </view>

  <view class="empty" wx:if="{{testResults.length === 0 && !isLoading}}">
    <text>暂无测试结果</text>
  </view>

  <view class="instructions">
    <text class="instruction-title">使用说明:</text>
    <text class="instruction-item">1. 确保后端服务已启动</text>
    <text class="instruction-item">2. 在管理后台"内容管理"中配置轮播图和公告</text>
    <text class="instruction-item">3. 检查网络连接和API地址配置</text>
    <text class="instruction-item">4. 新接口路径：/applet/config/banners、/announcements、/cities</text>
  </view>
</view>

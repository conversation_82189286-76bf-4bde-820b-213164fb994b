<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.SubjectMapper">

    <resultMap type="Subject" id="SubjectResult">
        <result property="id"    column="id"    />
        <result property="name"    column="subject_name"    />
        <result property="sort"    column="sort_order"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectSubjectVo">
        select id, subject_name, sort_order, create_time, update_time, create_by, update_by from course_subject
    </sql>

    <select id="selectSubjectList" parameterType="Subject" resultMap="SubjectResult">
        <include refid="selectSubjectVo"/>
        <where>
            <if test="name != null  and name != ''"> and subject_name like concat('%', #{name}, '%')</if>
        </where>
        order by sort_order asc, id asc
    </select>

    <select id="selectSubjectById" parameterType="Long" resultMap="SubjectResult">
        <include refid="selectSubjectVo"/>
        where id = #{id}
    </select>

    <insert id="insertSubject" parameterType="Subject" useGeneratedKeys="true" keyProperty="id">
        insert into course_subject
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">subject_name,</if>
            <if test="sort != null">sort_order,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateSubject" parameterType="Subject">
        update course_subject
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">subject_name = #{name},</if>
            <if test="sort != null">sort_order = #{sort},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSubjectById" parameterType="Long">
        delete from course_subject where id = #{id}
    </delete>

    <delete id="deleteSubjectByIds" parameterType="String">
        delete from course_subject where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 
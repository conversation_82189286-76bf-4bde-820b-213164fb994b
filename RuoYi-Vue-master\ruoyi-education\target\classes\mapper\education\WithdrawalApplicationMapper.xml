<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.WithdrawalApplicationMapper">
    
    <resultMap type="WithdrawalApplication" id="WithdrawalApplicationResult">
        <result property="id"    column="id"    />
        <result property="withdrawalNo"    column="withdrawal_no"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="applyAmount"    column="apply_amount"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="accountHolder"    column="account_holder"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="transferTime"    column="transfer_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWithdrawalApplicationVo">
        select id, withdrawal_no, teacher_id, teacher_name, apply_amount,
               bank_name, bank_account, account_holder, apply_status,
               audit_by, audit_time, transfer_time, create_by, create_time, update_by, update_time
        from withdrawal_application
    </sql>

    <select id="selectWithdrawalApplicationList" parameterType="WithdrawalApplication" resultMap="WithdrawalApplicationResult">
        <include refid="selectWithdrawalApplicationVo"/>
        <where>  
            <if test="withdrawalNo != null  and withdrawalNo != ''"> and withdrawal_no = #{withdrawalNo}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="applyStatus != null  and applyStatus != ''"> and apply_status = #{applyStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWithdrawalApplicationById" parameterType="Long" resultMap="WithdrawalApplicationResult">
        <include refid="selectWithdrawalApplicationVo"/>
        where id = #{id}
    </select>

    <select id="selectWithdrawalApplicationByTeacherId" parameterType="Long" resultMap="WithdrawalApplicationResult">
        <include refid="selectWithdrawalApplicationVo"/>
        where teacher_id = #{teacherId}
        order by create_time desc
    </select>

    <select id="selectWithdrawalApplicationByNo" parameterType="String" resultMap="WithdrawalApplicationResult">
        <include refid="selectWithdrawalApplicationVo"/>
        where withdrawal_no = #{withdrawalNo}
    </select>
        
    <insert id="insertWithdrawalApplication" parameterType="WithdrawalApplication" useGeneratedKeys="true" keyProperty="id">
        insert into withdrawal_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="withdrawalNo != null">withdrawal_no,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="applyAmount != null">apply_amount,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="serviceFee != null">service_fee,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="accountHolder != null">account_holder,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="applyReason != null">apply_reason,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="transferTime != null">transfer_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="withdrawalNo != null">#{withdrawalNo},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="applyAmount != null">#{applyAmount},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="serviceFee != null">#{serviceFee},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="accountHolder != null">#{accountHolder},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="applyReason != null">#{applyReason},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="transferTime != null">#{transferTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWithdrawalApplication" parameterType="WithdrawalApplication">
        update withdrawal_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="withdrawalNo != null">withdrawal_no = #{withdrawalNo},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="applyAmount != null">apply_amount = #{applyAmount},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="serviceFee != null">service_fee = #{serviceFee},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="accountHolder != null">account_holder = #{accountHolder},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="applyReason != null">apply_reason = #{applyReason},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="transferTime != null">transfer_time = #{transferTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWithdrawalApplicationById" parameterType="Long">
        delete from withdrawal_application where id = #{id}
    </delete>

    <delete id="deleteWithdrawalApplicationByIds" parameterType="String">
        delete from withdrawal_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 审核通过 -->
    <update id="approveWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '1', audit_by = #{auditBy}, audit_time = NOW()
        where id = #{id}
    </update>

    <!-- 审核拒绝 -->
    <update id="rejectWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '2', audit_by = #{auditBy}, reject_reason = #{rejectReason}, audit_time = NOW()
        where id = #{id}
    </update>

    <!-- 完成提现 -->
    <update id="completeWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '3', transfer_time = NOW()
        where id = #{id}
    </update>

    <!-- 取消提现 -->
    <update id="cancelWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '4'
        where id = #{id}
    </update>

    <!-- 标记违规 -->
    <update id="markViolationWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '5', reject_reason = #{violationReason}
        where id = #{id}
    </update>

    <!-- 批量审核通过 -->
    <update id="batchApproveWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '1', audit_by = #{auditBy}, audit_time = NOW()
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量审核拒绝 -->
    <update id="batchRejectWithdrawalApplication">
        update withdrawal_application 
        set apply_status = '2', audit_by = #{auditBy}, reject_reason = #{rejectReason}, audit_time = NOW()
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>

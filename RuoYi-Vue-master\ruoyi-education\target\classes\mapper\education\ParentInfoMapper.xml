<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.ParentInfoMapper">
    
    <resultMap type="ParentInfo" id="ParentInfoResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="avatar"    column="avatar"    />
        <result property="realName"    column="real_name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="totalOrders"    column="total_orders"    />
        <result property="completedOrders"    column="completed_orders"    />
        <result property="averageRating"    column="average_rating"    />
        <result property="status"    column="status"    />
        <result property="blacklistStatus"    column="blacklist_status"    />
        <result property="blacklistReason"    column="blacklist_reason"    />
        <result property="blacklistTime"    column="blacklist_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectParentInfoVo">
        select id, openid, user_name, nick_name, avatar, real_name, phone_number, total_orders, completed_orders, average_rating, status, blacklist_status, blacklist_reason, blacklist_time, create_by, create_time, update_by, update_time, remark from parent_info
    </sql>

    <select id="selectParentInfoList" parameterType="ParentInfo" resultMap="ParentInfoResult">
        <include refid="selectParentInfoVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="blacklistStatus != null  and blacklistStatus != ''"> and blacklist_status = #{blacklistStatus}</if>
        </where>
    </select>
    
    <select id="selectParentInfoById" parameterType="Long" resultMap="ParentInfoResult">
        <include refid="selectParentInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectParentInfoByOpenid" parameterType="String" resultMap="ParentInfoResult">
        <include refid="selectParentInfoVo"/>
        where openid = #{openid}
    </select>

    <select id="selectParentInfoByPhoneNumber" parameterType="String" resultMap="ParentInfoResult">
        <include refid="selectParentInfoVo"/>
        where phone_number = #{phoneNumber}
    </select>
        
    <insert id="insertParentInfo" parameterType="ParentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into parent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null">openid,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="realName != null">real_name,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="totalOrders != null">total_orders,</if>
            <if test="completedOrders != null">completed_orders,</if>
            <if test="averageRating != null">average_rating,</if>
            <if test="status != null">status,</if>
            <if test="blacklistStatus != null">blacklist_status,</if>
            <if test="blacklistReason != null">blacklist_reason,</if>
            <if test="blacklistTime != null">blacklist_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null">#{openid},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="realName != null">#{realName},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="totalOrders != null">#{totalOrders},</if>
            <if test="completedOrders != null">#{completedOrders},</if>
            <if test="averageRating != null">#{averageRating},</if>
            <if test="status != null">#{status},</if>
            <if test="blacklistStatus != null">#{blacklistStatus},</if>
            <if test="blacklistReason != null">#{blacklistReason},</if>
            <if test="blacklistTime != null">#{blacklistTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateParentInfo" parameterType="ParentInfo">
        update parent_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null">openid = #{openid},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="totalOrders != null">total_orders = #{totalOrders},</if>
            <if test="completedOrders != null">completed_orders = #{completedOrders},</if>
            <if test="averageRating != null">average_rating = #{averageRating},</if>
            <if test="status != null">status = #{status},</if>
            <if test="blacklistStatus != null">blacklist_status = #{blacklistStatus},</if>
            <if test="blacklistReason != null">blacklist_reason = #{blacklistReason},</if>
            <if test="blacklistTime != null">blacklist_time = #{blacklistTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteParentInfoById" parameterType="Long">
        delete from parent_info where id = #{id}
    </delete>

    <delete id="deleteParentInfoByIds" parameterType="String">
        delete from parent_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="blacklistParentByIds">
        update parent_info set blacklist_status = '1', blacklist_reason = #{blacklistReason}, blacklist_time = now()
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="unblacklistParentByIds">
        update parent_info set blacklist_status = '0', blacklist_reason = null, blacklist_time = null
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
